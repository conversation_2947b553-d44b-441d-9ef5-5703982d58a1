package com.yhl.scp.mds.routing.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.map.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.routing.convertor.RoutingChangeConvertor;
import com.yhl.scp.mds.routing.domain.entity.RoutingChangeDO;
import com.yhl.scp.mds.routing.domain.service.RoutingChangeDomainService;
import com.yhl.scp.mds.routing.dto.RoutingChangeDTO;
import com.yhl.scp.mds.routing.infrastructure.dao.RoutingChangeDao;
import com.yhl.scp.mds.routing.infrastructure.po.RoutingChangePO;
import com.yhl.scp.mds.routing.service.RoutingChangeService;
import com.yhl.scp.mds.routing.vo.RoutingChangeVO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class RoutingChangeServiceImpl extends AbstractService implements RoutingChangeService {

    @Resource
    private RoutingChangeDao routingChangeDao;

    @Resource
    private RoutingChangeDomainService routingChangeDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(RoutingChangeDTO routingChangeDTO) {
        // 0.数据转换
        RoutingChangeDO routingChangeDO = RoutingChangeConvertor.INSTANCE.dto2Do(routingChangeDTO);
        RoutingChangePO routingChangePO = RoutingChangeConvertor.INSTANCE.dto2Po(routingChangeDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        routingChangeDomainService.validation(routingChangeDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(routingChangePO);
        routingChangeDao.insert(routingChangePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(RoutingChangeDTO routingChangeDTO) {
        // 0.数据转换
        RoutingChangeDO routingChangeDO = RoutingChangeConvertor.INSTANCE.dto2Do(routingChangeDTO);
        RoutingChangePO routingChangePO = RoutingChangeConvertor.INSTANCE.dto2Po(routingChangeDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        routingChangeDomainService.validation(routingChangeDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(routingChangePO);
        routingChangeDao.update(routingChangePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<RoutingChangeDTO> list) {
        List<RoutingChangePO> newList = RoutingChangeConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        routingChangeDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<RoutingChangeDTO> list) {
        List<RoutingChangePO> newList = RoutingChangeConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        routingChangeDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return routingChangeDao.deleteBatch(idList);
        }
        return routingChangeDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public RoutingChangeVO selectByPrimaryKey(String id) {
        RoutingChangePO po = routingChangeDao.selectByPrimaryKey(id);
        return RoutingChangeConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mds_rou_routing_change")
    public List<RoutingChangeVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mds_rou_routing_change")
    public List<RoutingChangeVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<RoutingChangeVO> dataList = routingChangeDao.selectByCondition(sortParam, queryCriteriaParam);
        RoutingChangeServiceImpl target = springBeanUtils.getBean(RoutingChangeServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<RoutingChangeVO> selectByParams(Map<String, Object> params) {
        List<RoutingChangePO> list = routingChangeDao.selectByParams(params);
        return RoutingChangeConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<RoutingChangeVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void doTransitionRoutingChange(List<RoutingChangeDTO> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        list = list.stream()
                .collect(Collectors.toMap(
                        item -> item.getStockPointId() + "_" + item.getProductId(), // 以 stockPointId_productId 作为 key
                        Function.identity(), // 保留原始对象
                        (existing, replacement) -> existing // 如果 key 冲突，保留第一个出现的对象
                ))
                .values()
                .stream()
                .collect(Collectors.toList());


        List<RoutingChangePO> oldPos = routingChangeDao.selectRoutingChangeId(list);
        List<RoutingChangeDTO> insertList = new ArrayList<>();
        Map<String, RoutingChangePO> oldPosMap = CollectionUtils.isEmpty(oldPos) ?
                MapUtil.newHashMap() :
                oldPos.stream().collect(Collectors.toMap(t -> t.getStockPointId() + "_" + t.getProductId(),
                        Function.identity(), (v1, v2) -> v1));
        for (RoutingChangeDTO routingChangeDTO : list){
            RoutingChangeDTO dto =new RoutingChangeDTO();
            String id =
                    routingChangeDTO.getStockPointId() + "_" + routingChangeDTO.getProductId();

            if (oldPosMap.containsKey(id)) {
                continue;
            }
            dto.setStockPointId(routingChangeDTO.getStockPointId());
            dto.setProductId(routingChangeDTO.getProductId());
            insertList.add(dto);
        }
        if(CollectionUtils.isNotEmpty(insertList)){
            this.doCreateBatch(insertList);
        }
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.ROUTING_CHANGE.getCode();
    }

    @Override
    public List<RoutingChangeVO> invocation(List<RoutingChangeVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
