package com.yhl.scp.mds.bom.service.impl;


import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.util.MapUtils;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.bom.dto.ProductAboutBomDTO;
import com.yhl.scp.mds.bom.dto.ProductBomDTO;
import com.yhl.scp.mds.bom.dto.ProductBomVersionDTO;
import com.yhl.scp.mds.bom.infrastructure.dao.MdsProductBomDao;
import com.yhl.scp.mds.bom.infrastructure.po.ProductBomPO;
import com.yhl.scp.mds.bom.service.ProductAboutBomService;
import com.yhl.scp.mds.bom.service.MdsProductBomService;
import com.yhl.scp.mds.bom.service.MdsProductBomVersionService;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.newproduct.dto.NewProductStockPointDTO;
import com.yhl.scp.mds.newproduct.infrastructure.dao.NewProductStockPointDao;
import com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.routing.dto.RoutingChangeDTO;
import com.yhl.scp.mds.routing.service.*;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProductAboutBomServiceImpl extends AbstractService implements ProductAboutBomService {

    @Resource
    private NewStockPointService newStockPointService;

    @Resource
    private NewProductStockPointService productStockPointService;

    @Resource
    private NewProductStockPointDao newProductStockPointDao;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private MdsProductBomService mdsProductBomService;

    @Resource
    private MdsProductBomDao mdsProductBomDao;

    @Resource
    private MdsProductBomVersionService mdsProductBomVersionService;

    @Resource
    private StandardStepService standardStepService;

    @Resource
    private NewRoutingStepInputService newRoutingStepInputService;
    @Resource
    private NewRoutingStepService newRoutingStepService;

    @Resource
    private MrpFeign mrpFeign;

    @Resource
    private RoutingChangeService routingChangeService;

    @Override
    public String getObjectType() {
        return null;
    }

    /**
     * 同步接口入口
     *
     * @param tenantCode
     * @param beginTime
     * @param endTime
     * @param orgId
     * @param scenario
     */
    @Override
    public BaseResponse<Object> syncData(String tenantCode, String beginTime, String endTime, String orgId,
                                         String scenario) {
        try {
            log.info("开始同步bom数据");
            HashMap<String, Object> map = MapUtil.newHashMap();
            if (StringUtils.isEmpty(tenantCode)) {
                tenantCode = SystemHolder.getTenantId();
            }
            if (!StringUtils.isEmpty(beginTime)) {
                map.put("triggerType", YesOrNoEnum.YES.getCode());
                map.put("lastUpdateDate", beginTime);
                map.put("endDate", endTime);
            }
            //同步表中product_id为空的数据
            syncEmpty(scenario);
            //获取对应库存点编码
            List<String> codes = new ArrayList<>();
            if (!StringUtils.isEmpty(orgId)) {
                codes.add(orgId);
            } else {
                List<NewStockPointVO> newStockPointVOS = newStockPointService.selectAll();
                for (NewStockPointVO stockPointVO : newStockPointVOS) {
                    String interfaceFlag = stockPointVO.getInterfaceFlag();
                    if (StringUtils.isNotEmpty(interfaceFlag)) {
                        String[] split = interfaceFlag.split(",");
                        boolean b = Arrays.stream(split).anyMatch(x -> ApiCategoryEnum.ROUTING_STEP_INPUT.getCode().equals(x));
                        if (b) {
                            codes.add(stockPointVO.getStockPointCode());
                        }
                    }
                }
            }
            log.info("同步的库存点编码有{}", codes);
            for (String code : codes) {
                map.put("organizationCode", code);
                map.put("scenario",scenario);
                //获取ERP的BOM替代料关系数据
                newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.ERP.getCode(),
                        ApiCategoryEnum.ROUTING_STEP_INPUT.getCode(), map);
            }
            newRoutingStepService.doCreatFirstStepInputAndOut(null);
            return BaseResponse.success("同步操作完成");
        } catch (Exception e) {
            log.error("同步bom报错,{}", e.getMessage());
            throw new BusinessException("同步bom报错", e.getMessage());
        }
    }

    @Override
    public BaseResponse syncProductAboutBomData(String scenario, List<ProductAboutBomDTO> list) {
        //获取库存点Id
        try {
            Map<String, Object> map = MapUtil.newHashMap();
            String organizationCode = list.get(0).getOrganizationCode();
            map.put("stockPointCode", organizationCode);
            List<NewStockPointVO> newStockPointVOS = newStockPointService.selectByParams(map);
            String stockPointId = newStockPointVOS.get(0).getId();
            //同步bomVersion数据
            syncBomVersion(list, stockPointId, organizationCode);
            //同步bom数据
            syncBom(list, stockPointId, organizationCode, scenario);
            //更新原片替代映射数据
            mrpFeign.syncGlassSubstitutionRelationship(scenario);
            return BaseResponse.success("数据同步成功");
        } catch (Exception e) {
            log.error("运行报错，{}", e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 物料变更时触发
     * @param productStockPointDTOS
     */
    @Override
    public void syncBomStatus(List<NewProductStockPointDTO> productStockPointDTOS) {
        try {
            log.info("同步物料表变更bom和bom_version数据状态");
            Map<String, String> collect = productStockPointDTOS.stream().collect(Collectors.toMap(
                    x -> x.getId(),
                    x -> x.getEnabled()
            ));
            Set<String> ids = collect.keySet();
            log.info("更新的物料数据{}条", ids.size());
            if(ids.isEmpty()){
                return;
            }
            //1、查询头表中的物料编码
            List<ProductBomVersionDTO> updateBomVersionList = new ArrayList<>();
            List<ProductBomDTO> updateBomList1 = new ArrayList<>();
            //获取bom_version的数据集合
            Map<String, Object> bomVersionMap = MapUtil.newHashMap();
            bomVersionMap.put("productIds", ids);
            List<ProductBomVersionVO> productBomVersionVOS = mdsProductBomVersionService.selectByParams(bomVersionMap);
            //过滤掉状态不变化的数据
            productBomVersionVOS = productBomVersionVOS.stream().filter(x -> !x.getEnabled().equals(collect.get(x.getProductId()))).collect(Collectors.toList());
            if (!productBomVersionVOS.isEmpty()) {
                //获取bomVersion的id集合
                Set<String> bomVersionIds = productBomVersionVOS.stream().map(ProductBomVersionVO::getId).collect(Collectors.toSet());
                //获取bom数据集合
                Map<String, Object> bomMap = MapUtil.newHashMap();
                bomMap.put("bomVersionIds", bomVersionIds);
                List<ProductBomVO> productBomVOS = mdsProductBomService.selectByParams(bomMap);
                Map<String, List<ProductBomVO>> bomDataMap = productBomVOS.stream().collect(Collectors.groupingBy(ProductBomVO::getBomVersionId));
                //获取bom中物料的状态
                List<NewProductStockPointPO> stockPointPOS = newProductStockPointDao.selectByPrimaryKeys(
                        productBomVOS.stream().map(ProductBomVO::getIoProductId).collect(Collectors.toList()));
                Map<String, String> bomProductStockPointMap = stockPointPOS.stream().collect(Collectors.toMap(
                        x -> x.getId(),
                        x -> x.getEnabled()
                ));
                for (ProductBomVersionVO productBomVersionVo : productBomVersionVOS) {
                    //获取修改后的编码状态
                    String productId = productBomVersionVo.getProductId();
                    String status = collect.get(productId);
                    //更新bomVerson状态
                    productBomVersionVo.setEnabled(status);
                    ProductBomVersionDTO productBomVersionDTO = new ProductBomVersionDTO();
                    BeanUtils.copyProperties(productBomVersionVo, productBomVersionDTO);
                    updateBomVersionList.add(productBomVersionDTO);
                    //获取该头表下的bom集合
                    List<ProductBomVO> productBomVOS1 = bomDataMap.get(productBomVersionVo.getId());
                    if (!productBomVOS1.isEmpty()) {
                        //变成失效
                        if (YesOrNoEnum.NO.getCode().equals(status)) {
                            for (ProductBomVO productBomVo : productBomVOS1) {
                                if (YesOrNoEnum.YES.getCode().equals(productBomVo.getEnabled())) {
                                    productBomVo.setEnabled(YesOrNoEnum.NO.getCode());
                                    ProductBomDTO productBomDTO = new ProductBomDTO();
                                    BeanUtils.copyProperties(productBomVo, productBomDTO);
                                    updateBomList1.add(productBomDTO);
                                }
                            }
                        } else {
                            //变成生效
                            for (ProductBomVO productBomVo : productBomVOS1) {
                                String ioProductId = productBomVo.getIoProductId();
                                if (StringUtils.isNotEmpty(ioProductId)) {
                                    String enabled = bomProductStockPointMap.get(ioProductId);
                                    //生效前是失效，状态为NO 所以只要下面if不同，就说明物料状态为YES，bom的状态就变为YES
                                    if (!productBomVo.getEnabled().equals(enabled)) {
                                        productBomVo.setEnabled(enabled);
                                        ProductBomDTO productBomDTO = new ProductBomDTO();
                                        BeanUtils.copyProperties(productBomVo, productBomDTO);
                                        updateBomList1.add(productBomDTO);
                                    }
                                }
                            }
                        }
                    }
                }
                if (!updateBomVersionList.isEmpty()) {
                    log.info("根据bom头表推测行表，修改头表数据{}条", updateBomVersionList.size());
                    mdsProductBomVersionService.doUpdateBatch(updateBomVersionList);
                }
                if (!updateBomList1.isEmpty()) {
                    log.info("根据bom头表推测行表，修改行表数据{}条", updateBomList1.size());
                    mdsProductBomService.doUpdateBatch(updateBomList1);
                }
            }

            //2、查询行表中的物料编码
            List<ProductBomDTO> updateBomList2 = new ArrayList<>();
            Map<String, Object> bomQueryMap = MapUtils.newHashMap();
            bomQueryMap.put("ioProductIds", ids);
            List<ProductBomVO> productBomVOS1 = mdsProductBomService.selectByParams(bomQueryMap);
            //获取所有的bomVersionId
            if (!productBomVOS1.isEmpty()) {
                List<String> allBomVersionIds = productBomVOS1.stream().map(ProductBomVO::getBomVersionId).collect(Collectors.toList());
                List<String> queryBomVersionId = allBomVersionIds.stream().distinct().collect(Collectors.toList());
                //获取对应的头表
                List<ProductBomVersionVO> productBomVersionVOS1 = mdsProductBomVersionService.selectByPrimaryKeys(queryBomVersionId);
                Map<String, String> queryBomVerionMap = productBomVersionVOS1.stream().collect(Collectors.toMap(
                        x -> x.getId(),
                        x -> x.getEnabled()
                ));
                for (ProductBomVO bom : productBomVOS1) {
                    String status = queryBomVerionMap.get(bom.getBomVersionId());
                    if (StringUtils.isNotEmpty(status)) {
                        //bom_verion是有效的
                        if (YesOrNoEnum.YES.getCode().equals(status)) {
                            //如果状态发送变化
                            if (!bom.getEnabled().equals(collect.get(bom.getIoProductId()))) {
                                bom.setEnabled(collect.get(bom.getIoProductId()));
                                ProductBomDTO productBomDTO = new ProductBomDTO();
                                BeanUtils.copyProperties(bom, productBomDTO);
                                updateBomList2.add(productBomDTO);
                            }
                        }
                    }
                }
                if (!updateBomList2.isEmpty()) {
                    log.info("直接根据bom行表，修改行表数据{}条", updateBomList2.size());
                    mdsProductBomService.doUpdateBatch(updateBomList2);
                }
            }
        } catch (Exception e) {
            log.error("同步物料表数据时，给bom数据的状态进行变更失败，{}", e.getMessage());
        }
    }

    /**
     * 同步现在旧数据中ioProductId为空的数据
     */
    private void syncEmpty(String scenario) {
        //维护本次需要转换的工艺路径输入物品数据
        List<String> allComponentSequenceIdList = Lists.newArrayList();

        List<RoutingChangeDTO> routingChangeDTOList = new ArrayList<>();

        //获取当前bom表中ioProductID为空的数据bom
        HashMap<String, Object> map = MapUtil.newHashMap();
        map.put("ioProductIsNull", YesOrNoEnum.YES.getCode());
        map.put("ioProductSyncCodeIsNotNull", YesOrNoEnum.YES.getCode());
        List<ProductBomVO> productBomVOS = mdsProductBomService.selectByParams(map);

        //获取当前bomVersion表中ioProductID为空的数据
        HashMap<String, Object> versionMap = MapUtil.newHashMap();
        versionMap.put("productIsNull", YesOrNoEnum.YES.getCode());
        versionMap.put("productSyncCodeIsNotNull", YesOrNoEnum.YES.getCode());
        List<ProductBomVersionVO> productBomVersionVOS = mdsProductBomVersionService.selectByParams(versionMap);
        //判断两个集合是否为空
        if (productBomVOS.isEmpty() && productBomVersionVOS.isEmpty()){
            log.info("bom_version和bom表中没有product_id为空的数据");
            return;
        }
        Set<String> productSyncCodeSet = new HashSet<>();
        Map<String, String> bomVersionMap = new HashMap<>();
        //获取bom对应的头表数据
        if (!productBomVOS.isEmpty()){
            List<String> bomVersionIds = productBomVOS.stream().map(ProductBomVO::getBomVersionId).collect(Collectors.toList());
            List<ProductBomVersionVO> productBomVersionVOS1 = mdsProductBomVersionService.selectByPrimaryKeys(bomVersionIds);
            bomVersionMap = productBomVersionVOS1.stream().collect(Collectors.toMap(BaseVO::getId, BaseVO::getEnabled));
            productSyncCodeSet.addAll(productBomVOS.stream().map(ProductBomVO::getIoProductSyncCode).collect(Collectors.toSet()));
        }
        if (!productBomVersionVOS.isEmpty()){
            productSyncCodeSet.addAll(productBomVersionVOS.stream().map(ProductBomVersionVO::getProductSyncCode).collect(Collectors.toSet()));
        }
        //去物料表中匹配数据  物料
        HashMap<String, Object> map1 = MapUtil.newHashMap();
        map1.put("productCodes", productSyncCodeSet);
        List<NewProductStockPointVO> newProductStockPointVOS = productStockPointService.selectByParams(map1);
        //获取物料表的map集合，以物料编码和组织id为key
        Map<String, NewProductStockPointVO> productStockPointMap = newProductStockPointVOS.stream().collect(Collectors.toMap(
                x -> x.getProductCode() + "-" + x.getOrganizationId(),
                x -> x
        ));
        if (!newProductStockPointVOS.isEmpty()) {
            //1、匹配bom表
            List<ProductBomDTO> list = ListUtil.list(false);
            for (ProductBomVO productBomVO : productBomVOS) { //一个获取对象 一个获取状态
                NewProductStockPointVO newProductStockPointVO = productStockPointMap.get(productBomVO.getIoProductSyncCode() + "-" + productBomVO.getIoStockPointId());
                if (Objects.nonNull(newProductStockPointVO)) {
                    ProductBomDTO productBomDTO = new ProductBomDTO();
                    //赋值状态
                    String bomVersionStatus = bomVersionMap.get(productBomVO.getBomVersionId());
                    if (StringUtils.isNotEmpty(bomVersionStatus)) {
                        if (YesOrNoEnum.YES.getCode().equals(bomVersionStatus)) {
                            //如果状态发送变化
                            if (!productBomVO.getEnabled().equals(newProductStockPointVO.getEnabled())) {
                                productBomVO.setEnabled(newProductStockPointVO.getEnabled());
                            }
                        } else {
                            if (!productBomVO.getEnabled().equals(bomVersionStatus)) {
                                productBomVO.setEnabled(bomVersionStatus);
                            }
                        }
                    }
                    //赋值物料id以及去掉编码
                    productBomVO.setIoProductId(newProductStockPointVO.getId());
                    productBomVO.setIoProductSyncCode(null);
                    BeanUtils.copyProperties(productBomVO, productBomDTO);
                    list.add(productBomDTO);
                }
            }
            if (!list.isEmpty()) {
                log.info("匹配bom时，更新数据{}条",list.size());
                mdsProductBomService.doUpdateBatch(list);
                allComponentSequenceIdList.addAll(list.stream()
                        .map(ProductBomDTO::getComponentSequenceId).collect(Collectors.toList()));
                routingChangeDTOList.addAll(list.stream().map(item -> {
                    RoutingChangeDTO dto = new RoutingChangeDTO();
                    dto.setProductId(item.getIoProductId());
                    dto.setStockPointId(item.getIoStockPointId());
                    return dto;
                }).collect(Collectors.toList()));

            }
            //2、匹配bomVersion表
            if (productBomVersionVOS.isEmpty()){
                return;
            }
            //获取对应的bom表数据
            Set<String> ids = productBomVersionVOS.stream().map(ProductBomVersionVO::getId).collect(Collectors.toSet());
            Map<String, Object> queryBomMap = MapUtil.newHashMap();
            queryBomMap.put("bomVersionIds", ids);
            List<ProductBomVO> productBomVOList = mdsProductBomService.selectByParams(queryBomMap);
            //获取bom表的map集合，以bomVersionId划分
            Map<String, List<ProductBomVO>> productBomMap = productBomVOList.stream().collect(Collectors.groupingBy(ProductBomVO::getBomVersionId));
            //获取bom的物料对象
            List<String> productIds = productBomVOList.stream().map(ProductBomVO::getIoProductId).collect(Collectors.toList());
            List<NewProductStockPointVO> bomProductStockPointVOS1 = productStockPointService.selectByPrimaryKeys(productIds);
            //获取bom物料的Map集合
            Map<String, NewProductStockPointVO> bomProductStockPointMap1 = bomProductStockPointVOS1.stream().collect(Collectors.toMap(NewProductStockPointVO::getId,
                    Function.identity(), (a, b) -> a, LinkedHashMap::new));
            List<ProductBomVersionDTO> versionList = ListUtil.list(false);
            List<ProductBomDTO> bomDTOList = ListUtil.list(false);
            for (ProductBomVersionVO productBomVersionVO : productBomVersionVOS) {
                NewProductStockPointVO newProductStockPointVO = productStockPointMap.get(productBomVersionVO.getProductSyncCode() +"-" + productBomVersionVO.getStockPointId());
                if (Objects.nonNull(newProductStockPointVO)) {
                    ProductBomVersionDTO productBomVersionDTO = new ProductBomVersionDTO();
                    String enabled = newProductStockPointVO.getEnabled();
                    if (!productBomVersionVO.getEnabled().equals(enabled)) {
                        productBomVersionVO.setEnabled(enabled);
                        //获取的bom集合对象
                        List<ProductBomVO> productBomVOS1 = productBomMap.get(productBomVersionVO.getId());
                        if (!productBomVOS1.isEmpty()) {
                            if (YesOrNoEnum.NO.getCode().equals(enabled)) {
                                productBomVOS1.forEach(x -> {
                                    x.setEnabled(YesOrNoEnum.NO.getCode());
                                    ProductBomDTO productBomDTO = new ProductBomDTO();
                                    BeanUtils.copyProperties(x, productBomDTO);
                                    bomDTOList.add(productBomDTO);
                                });
                            } else {
                                for (ProductBomVO bomVO : productBomVOS1) {
                                    String ioProductId = bomVO.getIoProductId();
                                    if (StringUtils.isNotEmpty(ioProductId)) {
                                        NewProductStockPointVO newProductStockPointVO1 = bomProductStockPointMap1.get(ioProductId);
                                        if (Objects.isNull(newProductStockPointVO1)) {
                                            continue;
                                        }
                                        if (!bomVO.getEnabled().equals(newProductStockPointVO1.getEnabled())) {
                                            bomVO.setEnabled(newProductStockPointVO1.getEnabled());
                                            ProductBomDTO productBomDTO = new ProductBomDTO();
                                            BeanUtils.copyProperties(bomVO, productBomDTO);
                                            bomDTOList.add(productBomDTO);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    productBomVersionVO.setProductId(newProductStockPointVO.getId());
                    productBomVersionVO.setProductSyncCode(null);
                    BeanUtils.copyProperties(productBomVersionVO, productBomVersionDTO);
                    versionList.add(productBomVersionDTO);
                }
            }
            if (!versionList.isEmpty()) {
                mdsProductBomVersionService.doUpdateBatch(versionList);
            }
            if (!bomDTOList.isEmpty()) {
                mdsProductBomService.doUpdateBatch(bomDTOList);
                allComponentSequenceIdList.addAll(bomDTOList.stream()
                        .map(ProductBomDTO::getComponentSequenceId).collect(Collectors.toList()));
                routingChangeDTOList.addAll(bomDTOList.stream().map(item -> {
                    RoutingChangeDTO dto = new RoutingChangeDTO();
                    dto.setProductId(item.getIoProductId());
                    dto.setStockPointId(item.getIoStockPointId());
                    return dto;
                }).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(allComponentSequenceIdList)){
                log.info("物料表接口触发需要修改的bom有{}条,工艺路径步骤也需要修改这些数据",allComponentSequenceIdList.size());
                List<NewStockPointVO> stockPointVOList = newStockPointService.selectAll();
                newRoutingStepInputService.doTransitionRoutingStepInput(allComponentSequenceIdList, null ,
                        null, stockPointVOList, scenario);
            }
            if (CollectionUtils.isNotEmpty(routingChangeDTOList)){
                routingChangeService.doTransitionRoutingChange(routingChangeDTOList);
            }
        }
    }

    /**
     * 同步bomVersion数据
     *
     * @param list
     * @param stockPointId
     * @param organizationCode
     */
    private void syncBomVersion(List<ProductAboutBomDTO> list, String stockPointId, String organizationCode) {
        //更新和新增集合
        List<ProductBomVersionDTO> insertBomVersionList = Lists.newArrayList();
        List<ProductBomVersionDTO> updateBomVersionList = Lists.newArrayList();
        //按照头ID分类且只获取一个
        Map<String, ProductAboutBomDTO> versionData = list.stream().
                collect(Collectors.toMap(
                        item -> item.getBillSequenceId(),
                        item -> item,
                        (item1, item2) -> item1
                ));
        Set<String> billSequenceIds = versionData.keySet();

        //查询出bpim存在的数据
        HashMap<String, Object> queryVersionMap = MapUtils.newHashMap();
        queryVersionMap.put("billSequenceIds", billSequenceIds);
        List<ProductBomVersionVO> productBomVersionVOS = mdsProductBomVersionService.selectByParams(queryVersionMap);
        Map<String, ProductBomVersionVO> existingVersionData = productBomVersionVOS.stream().collect(Collectors.toMap(
                item -> item.getBillSequenceId(),
                item -> item
        ));
        Set<String> existingBillSequenceIds = existingVersionData.keySet();
        //获取物料表数据
        Set<String> assemItemCodes = list.stream().map(ProductAboutBomDTO::getAssemblyItemCode).collect(Collectors.toSet());
        HashMap<String, Object> itemMap = MapUtil.newHashMap();
        itemMap.put("stockPointCode", organizationCode);
        itemMap.put("productCodes", assemItemCodes);
        List<NewProductStockPointVO> newProductStockPointVOS = productStockPointService.selectByParams(itemMap);
        //数据遍历
        for (String key : billSequenceIds) {
            ProductAboutBomDTO dto = versionData.get(key);
            //获取物品Id
            Optional<NewProductStockPointVO> first1 = newProductStockPointVOS.stream().filter(x -> dto.getAssemblyItemCode().equals(x.getProductCode())).findFirst();
            String productId = null;
            if (first1.isPresent()) {
                productId = first1.get().getId();
            }
            //判断数据是否有效
            String flag = "有效".equals(dto.getAssemblyItemStatus()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode();
            //判断数据是新增还是修改
            if (existingBillSequenceIds.contains(key)) {
                ProductBomVersionVO productBomVersionVO = existingVersionData.get(key);
                if (!productBomVersionVO.getEnabled().equals(flag)) {
                    productBomVersionVO.setEnabled(flag);
                    ProductBomVersionDTO productBomVersionDTO = new ProductBomVersionDTO();
                    BeanUtils.copyProperties(productBomVersionVO, productBomVersionDTO);
                    updateBomVersionList.add(productBomVersionDTO);
                }
            } else {
                //赋值
                ProductBomVersionDTO newDto = ProductBomVersionDTO.builder()
                        .stockPointId(stockPointId).productId(productId).enabled(flag).billSequenceId(key).build();
                if (StringUtils.isEmpty(productId)) {
                    newDto.setProductSyncCode(dto.getAssemblyItemCode());
                }
                insertBomVersionList.add(newDto);
            }
        }
        //数据插入和更新
        if (!updateBomVersionList.isEmpty()) {
            mdsProductBomVersionService.doUpdateBatch(updateBomVersionList);
        }
        if (!insertBomVersionList.isEmpty()) {
            mdsProductBomVersionService.doCreateBatch(insertBomVersionList);
        }
    }

    /**
     * 同步bom数据
     *
     * @param list
     * @param stockPointId
     * @param organizationCode
     */
    private void syncBom(List<ProductAboutBomDTO> list, String stockPointId, String organizationCode, String scenario) {
        //更新和新增集合
        List<ProductBomDTO> insertBomList = Lists.newArrayList();
        List<ProductBomDTO> updateBomList = Lists.newArrayList();
        List<RoutingChangeDTO> routingChangeDTOList = new ArrayList<>();
        //根据头id分类获取每一类数据集合
        LinkedHashMap<String, List<ProductAboutBomDTO>> collect = list.stream().collect(Collectors.groupingBy(ProductAboutBomDTO::getBillSequenceId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                innerList -> {
                                    return innerList;
                                }
                        )
                )).entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new
                ));
        //通过行id获取bpim已有的数据
        Map<String, ProductAboutBomDTO> bomDataMap = list.stream().collect(Collectors.toMap(ProductAboutBomDTO::getComponentSequenceId,
                item -> item
        ));
        Set<String> keySet = bomDataMap.keySet();
        //获取bom的旧数据
        Map<String, Object> bomMap = MapUtil.newHashMap();
        bomMap.put("componentSequenceIds", keySet);
        List<ProductBomVO> oldBomList = mdsProductBomService.selectByParams(bomMap);
        //获取旧数据的map类
        Map<String, ProductBomVO> oldDataMap = oldBomList.stream().collect(Collectors.toMap(ProductBomVO::getComponentSequenceId,
                item -> item
        ));
        Set<String> oldKeySet = oldDataMap.keySet();
        //获取标准工艺数据
        List<StandardStepVO> standardStepVOS = standardStepService.selectAll();
        //获取物料表数据
        Set<String> componentItemCodes = list.stream().map(ProductAboutBomDTO::getComponentItemCode).collect(Collectors.toSet());
        HashMap<String, Object> itemComponentItemMap = MapUtil.newHashMap();
        itemComponentItemMap.put("stockPointCode", organizationCode);
        itemComponentItemMap.put("productCodes", componentItemCodes);
        List<NewProductStockPointVO> newProductStockPointVOS1 = productStockPointService.selectByParams(itemComponentItemMap);
        //根据同步的数据去获取bomVersion的数据
        HashMap<String, Object> map = MapUtil.newHashMap();
        map.put("billSequenceIds", collect.keySet());
        List<ProductBomVersionVO> productBomVersionVOS = mdsProductBomVersionService.selectByParams(map);
        //获取每一个头id中行号最高的数据集合
        List<String> bomVersionIds = productBomVersionVOS.stream().map(ProductBomVersionVO::getId).collect(Collectors.toList());
        List<ProductBomPO> rowNumList = mdsProductBomDao.selectEveryFirstData(bomVersionIds);
        //遍历Map集合
        for (String key : collect.keySet()) {
            List<ProductAboutBomDTO> productAboutBomDTOS = collect.get(key);
            //通过头id找到bom_version对应的主键id
            Optional<ProductBomVersionVO> first2 = productBomVersionVOS.stream().filter(x -> key.equals(x.getBillSequenceId())).findFirst();
            if (!first2.isPresent()) {
                log.error("没有在mds_rou_product_bom_version表中找到头id{}对应的主键id。", key);
                continue;
            }
            //获取头id对应的bomVersion主键ID
            String id = first2.get().getId();
            //设置行号
            int rowNumber = 10;
            Optional<ProductBomPO> first3 = rowNumList.stream().filter(x -> id.equals(x.getBomVersionId())).findFirst();
            if (first3.isPresent()) {
                //获取同样头id中行号最大的数据
                rowNumber = first3.get().getRowNum() + 10;
            }
            for (ProductAboutBomDTO dto : productAboutBomDTOS) {
                ProductBomDTO productBomDTO = new ProductBomDTO();
                if (oldKeySet.contains(dto.getComponentSequenceId())) {
                    ProductBomVO productBomVO = oldDataMap.get(dto.getComponentSequenceId());
                    //先判断是否有新数据
                    if (Objects.isNull(productBomVO.getLastUpdateDate()) || dto.getLastUpdateDate().after(productBomVO.getLastUpdateDate())) {
                        //新数据的值赋值给旧数据，再用旧数据覆盖给空数据
                        productBomVO.setYield(dto.getComponentYieldFactor());
                        productBomVO.setIoFactor(dto.getComponentQuantity());
                        productBomVO.setWipSupplyType(dto.getWipSupplyType());
                        productBomVO.setStartTime(dto.getStartDate());
                        productBomVO.setEndTime(dto.getEndDate());
                        productBomVO.setLastUpdateDate(dto.getLastUpdateDate());
                        productBomVO.setEnabled(!"有效".equals(dto.getAssemblyItemStatus()) ? YesOrNoEnum.NO.getCode() :
                                ("有效".equals(dto.getComponentItemStatus()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode()));
                        //旧舒服覆盖给空数据
                        BeanUtils.copyProperties(productBomVO, productBomDTO);
                        updateBomList.add(productBomDTO);
                    } else {
                        if (StringUtils.isEmpty(productBomVO.getIoProductId())) {
                            //获取物品Id
                            Optional<NewProductStockPointVO> first1 = newProductStockPointVOS1.stream().filter(x -> dto.getComponentItemCode().equals(x.getProductCode())).findFirst();
                            if (first1.isPresent()) {
                                productBomVO.setIoProductId(first1.get().getId());
                                productBomVO.setIoProductSyncCode(null);
                                //旧数据覆盖给新数据
                                BeanUtils.copyProperties(productBomVO, productBomDTO);
                                updateBomList.add(productBomDTO);
                            }
                        }
                    }
                } else {
                    //标准工艺id
                    Optional<StandardStepVO> first = standardStepVOS.stream()
                            .filter(x -> dto.getOrganizationCode().equals(x.getStockPointCode()) && dto.getOperationSeqNum().equals(x.getStandardStepCode()))
                            .findFirst();
                    if (first.isPresent()) {
                        productBomDTO.setStandardStepId(first.get().getId());
                    }
                    //获取物品Id  如果匹配不到，则将编码存入ioProductSyncCode中
                    Optional<NewProductStockPointVO> first1 = newProductStockPointVOS1.stream().filter(x -> dto.getComponentItemCode().equals(x.getProductCode())).findFirst();
                    if (first1.isPresent()) {
                        productBomDTO.setIoProductId(first1.get().getId());
                    } else {
                        productBomDTO.setIoProductSyncCode(dto.getComponentItemCode());
                    }
                    productBomDTO.setBomVersionId(id);
                    productBomDTO.setRowNum(rowNumber);
                    productBomDTO.setComponentSequenceId(dto.getComponentSequenceId());
                    productBomDTO.setWipSupplyType(dto.getWipSupplyType());
                    productBomDTO.setYield(dto.getComponentYieldFactor());
                    productBomDTO.setIoFactor(dto.getComponentQuantity());
                    productBomDTO.setIoStockPointId(stockPointId);
                    productBomDTO.setIoType("INPUT");
                    productBomDTO.setMainMaterial(YesOrNoEnum.YES.getCode());
                    productBomDTO.setScrap(new BigDecimal(0));
                    productBomDTO.setEnabled(!"有效".equals(dto.getAssemblyItemStatus()) ? YesOrNoEnum.NO.getCode() :
                            ("有效".equals(dto.getComponentItemStatus()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode()));
                    productBomDTO.setStartTime(dto.getStartDate());
                    productBomDTO.setEndTime(dto.getEndDate());
                    productBomDTO.setLastUpdateDate(dto.getLastUpdateDate());
                    insertBomList.add(productBomDTO);
                    rowNumber += 10;
                }
            }
        }
        //维护本次需要转换的工艺路径输入物品数据
        List<String> allComponentSequenceIdList = Lists.newArrayList();
        if (!updateBomList.isEmpty()) {
            mdsProductBomService.doUpdateBatch(updateBomList);
            allComponentSequenceIdList.addAll(updateBomList.stream()
            		.map(ProductBomDTO::getComponentSequenceId).collect(Collectors.toList()));
            routingChangeDTOList.addAll(updateBomList.stream().map(item -> {
                RoutingChangeDTO dto = new RoutingChangeDTO();
                dto.setProductId(item.getIoProductId());
                dto.setStockPointId(item.getIoStockPointId());
                return dto;
            }).collect(Collectors.toList()));
        }
        if (!insertBomList.isEmpty()) {
            mdsProductBomService.doCreateBatch(insertBomList);
            allComponentSequenceIdList.addAll(insertBomList.stream()
            		.map(ProductBomDTO::getComponentSequenceId).collect(Collectors.toList()));
            routingChangeDTOList.addAll(insertBomList.stream().map(item -> {
                RoutingChangeDTO dto = new RoutingChangeDTO();
                dto.setProductId(item.getIoProductId());
                dto.setStockPointId(item.getIoStockPointId());
                return dto;
            }).collect(Collectors.toList()));
        }
        //物品BOM转换为工艺路径输入物品
        if(CollectionUtils.isNotEmpty(allComponentSequenceIdList)) {
            List<NewStockPointVO> stockPointVOList = newStockPointService.selectAll();
        	newRoutingStepInputService.doTransitionRoutingStepInput(allComponentSequenceIdList, null ,null,
                    stockPointVOList, scenario);
        }
        if (CollectionUtils.isNotEmpty(routingChangeDTOList)){
            routingChangeService.doTransitionRoutingChange(routingChangeDTOList);
        }
    }
}
