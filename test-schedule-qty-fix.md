# 已排产量计算修复测试指南

## 🧪 测试目标
验证已排产量计算修复是否正确工作

## 📋 测试用例

### 测试用例1：存在的产品编码
- **产品编码**：`00001LFW00007B`
- **预期结果**：已排产量 = 1640.0000
- **验证方法**：调用DFP报表接口，检查该产品的scheduleQty字段

### 测试用例2：不存在的产品编码
- **产品编码**：`01009LDR06001`
- **预期结果**：已排产量 = 0（因为产品不存在）
- **验证方法**：调用DFP报表接口，确认不会出现异常

### 测试用例3：调试日志验证
- **产品编码**：`00001LFW00007B`
- **预期结果**：在日志中看到详细的调试信息
- **验证方法**：检查应用日志，确认包含🔍 [调试]标记的日志

## 🔧 测试步骤

### 1. 准备测试环境
```bash
# 确保应用已部署最新代码
# 确保数据库连接正常
# 确保日志级别设置为INFO或DEBUG
```

### 2. 执行API测试
```bash
# 调用需求交付生产报表接口
POST /api/dfp/report/demand-delivery-production
Content-Type: application/json

{
  "scenario": "your_scenario_id",
  "planStartTime": "2024-12-09",
  "planEndTime": "2024-12-12",
  "oemCodes": ["your_oem_code"],
  "inWarehouseWarning": false
}
```

### 3. 验证结果
1. **检查响应数据**：
   - 查找产品`00001LFW00007B`
   - 验证`scheduleQty`字段值为1640.0000

2. **检查日志输出**：
   - 搜索包含"🔍 [调试]"的日志
   - 确认产品匹配逻辑正确执行
   - 确认数量计算过程正确

3. **性能验证**：
   - 确认接口响应时间正常
   - 确认没有异常或错误日志

## 📊 预期日志示例

```log
INFO  - 🔍 [调试] 开始计算产品00001LFW00007B的已排产量，主生产计划任务数量: 50
INFO  - 🔍 [调试] 产品00001LFW00007B找到9个成型工序任务
INFO  - 🔍 [调试] 产品00001LFW00007B的工单94356bf4-0194-356c2bcc-40280c9b-079a匹配成功，累加数量: 220.0000, 当前总量: 220.0000
INFO  - 🔍 [调试] 产品00001LFW00007B的工单94356bf4-0194-356c2c21-40280c9b-07a8匹配成功，累加数量: 60.0000, 当前总量: 280.0000
...
INFO  - 🔍 [调试] 产品00001LFW00007B的已排产量计算完成，总数量: 1640.0000, 处理任务数: 9, 跳过任务数: 0
```

## ✅ 成功标准

1. **功能正确性**：
   - 产品`00001LFW00007B`的已排产量为1640.0000
   - 不存在的产品返回0，不抛出异常

2. **日志完整性**：
   - 调试产品的详细日志正确输出
   - 匹配过程和计算过程清晰可见

3. **性能稳定性**：
   - 接口响应时间在可接受范围内
   - 没有内存泄漏或异常

4. **兼容性**：
   - 其他产品的计算不受影响
   - 报表其他功能正常工作

## 🚨 故障排查

### 如果已排产量不正确
1. 检查产品编码是否存在于数据库
2. 检查成型工序任务是否存在
3. 检查工单数据是否完整
4. 查看详细的调试日志

### 如果出现异常
1. 检查数据库连接
2. 检查MPS服务是否正常
3. 查看异常堆栈信息
4. 检查输入参数是否正确

### 如果性能下降
1. 检查数据库查询性能
2. 检查是否有死锁或长事务
3. 监控内存使用情况
4. 检查日志输出量是否过大