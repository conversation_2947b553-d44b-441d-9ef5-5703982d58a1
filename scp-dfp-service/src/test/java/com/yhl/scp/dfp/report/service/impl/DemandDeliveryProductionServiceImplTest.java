package com.yhl.scp.dfp.report.service.impl;

import com.yhl.scp.dfp.report.service.impl.DemandDeliveryProductionServiceImpl;
import com.yhl.scp.mps.plan.vo.MasterPlanTaskVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.mps.feign.MpsFeign;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 需求发货生产报表服务实现类单元测试
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-28
 */
@ExtendWith(MockitoExtension.class)
class DemandDeliveryProductionServiceImplTest {

    private static final Logger log = LoggerFactory.getLogger(DemandDeliveryProductionServiceImplTest.class);

    @InjectMocks
    private DemandDeliveryProductionServiceImpl demandDeliveryProductionService;

    @Mock
    private MpsFeign mpsFeign;

    // 测试常量
    private static final String TEST_SCENARIO = "TEST_SCENARIO";
    private static final String TEST_PRODUCT_CODE = "TEST_PRODUCT_001";
    private static final String TEST_ORDER_ID_1 = "ORDER_001";
    private static final String TEST_ORDER_ID_2 = "ORDER_002";
    private static final String TEST_TOP_ORDER_ID_1 = "TOP_ORDER_001";
    private static final String TEST_TOP_ORDER_ID_2 = "TOP_ORDER_002";
    private static final String PACKAGING_OPERATION = "包装";

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    /**
     * 测试 getWorkOrderMap 方法 - 正常场景
     */
    @Test
    void testGetWorkOrderMap_NormalCase() throws Exception {
        // 准备测试数据
        List<MasterPlanTaskVO> operationTasks = createMasterPlanTasks();
        List<WorkOrderVO> mockWorkOrders = createMockWorkOrders();
        
        // Mock 外部调用
        when(mpsFeign.selectWorkOrderByParams(eq(TEST_SCENARIO), any(Map.class)))
                .thenReturn(mockWorkOrders);

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getWorkOrderMap", String.class, List.class);
        method.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, WorkOrderVO> result = (Map<String, WorkOrderVO>) method
                .invoke(demandDeliveryProductionService, TEST_SCENARIO, operationTasks);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey(TEST_ORDER_ID_1));
        assertTrue(result.containsKey(TEST_ORDER_ID_2));
        
        WorkOrderVO workOrder1 = result.get(TEST_ORDER_ID_1);
        assertEquals(TEST_ORDER_ID_1, workOrder1.getId());
        assertEquals(new BigDecimal("100"), workOrder1.getQuantity());
        
        // 验证 Mock 调用
        verify(mpsFeign, times(1)).selectWorkOrderByParams(eq(TEST_SCENARIO), any(Map.class));
    }

    /**
     * 测试 getWorkOrderMap 方法 - 空操作任务列表
     */
    @Test
    void testGetWorkOrderMap_EmptyOperationTasks() throws Exception {
        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getWorkOrderMap", String.class, List.class);
        method.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, WorkOrderVO> result = (Map<String, WorkOrderVO>) method
                .invoke(demandDeliveryProductionService, TEST_SCENARIO, new ArrayList<>());

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证没有调用外部服务
        verify(mpsFeign, never()).selectWorkOrderByParams(any(), any());
    }

    /**
     * 测试 getWorkOrderMap 方法 - 空场景参数
     */
    @Test
    void testGetWorkOrderMap_BlankScenario() throws Exception {
        List<MasterPlanTaskVO> operationTasks = createMasterPlanTasks();
        
        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getWorkOrderMap", String.class, List.class);
        method.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, WorkOrderVO> result = (Map<String, WorkOrderVO>) method
                .invoke(demandDeliveryProductionService, "", operationTasks);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证没有调用外部服务
        verify(mpsFeign, never()).selectWorkOrderByParams(any(), any());
    }

    /**
     * 测试 getWorkOrderMap 方法 - 数据库异常
     */
    @Test
    void testGetWorkOrderMap_DatabaseException() throws Exception {
        List<MasterPlanTaskVO> operationTasks = createMasterPlanTasks();
        
        // Mock 数据库异常
        when(mpsFeign.selectWorkOrderByParams(eq(TEST_SCENARIO), any(Map.class)))
                .thenThrow(new RuntimeException("Database connection failed"));

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getWorkOrderMap", String.class, List.class);
        method.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, WorkOrderVO> result = (Map<String, WorkOrderVO>) method
                .invoke(demandDeliveryProductionService, TEST_SCENARIO, operationTasks);

        // 验证结果 - 异常时应返回空Map
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证调用了外部服务
        verify(mpsFeign, times(1)).selectWorkOrderByParams(eq(TEST_SCENARIO), any(Map.class));
    }

    /**
     * 测试 getTopWorkOrderMap 方法 - 正常场景
     */
    @Test
    void testGetTopWorkOrderMap_NormalCase() throws Exception {
        // 准备测试数据
        Map<String, WorkOrderVO> workOrderMap = createWorkOrderMap();
        List<WorkOrderVO> mockTopWorkOrders = createMockTopWorkOrders();
        
        // Mock 外部调用
        when(mpsFeign.selectWorkOrderByParams(eq(TEST_SCENARIO), any(Map.class)))
                .thenReturn(mockTopWorkOrders);

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getTopWorkOrderMap", String.class, Map.class);
        method.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, WorkOrderVO> result = (Map<String, WorkOrderVO>) method
                .invoke(demandDeliveryProductionService, TEST_SCENARIO, workOrderMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey(TEST_TOP_ORDER_ID_1));
        assertTrue(result.containsKey(TEST_TOP_ORDER_ID_2));
        
        // 验证 Mock 调用
        verify(mpsFeign, times(1)).selectWorkOrderByParams(eq(TEST_SCENARIO), any(Map.class));
    }

    /**
     * 测试 getTopWorkOrderMap 方法 - 空工单映射
     */
    @Test
    void testGetTopWorkOrderMap_EmptyWorkOrderMap() throws Exception {
        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getTopWorkOrderMap", String.class, Map.class);
        method.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, WorkOrderVO> result = (Map<String, WorkOrderVO>) method
                .invoke(demandDeliveryProductionService, TEST_SCENARIO, new HashMap<>());

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证没有调用外部服务
        verify(mpsFeign, never()).selectWorkOrderByParams(any(), any());
    }

    /**
     * 测试 getTopWorkOrderMap 方法 - 没有顶层工单ID
     */
    @Test
    void testGetTopWorkOrderMap_NoTopOrderIds() throws Exception {
        // 创建没有顶层工单ID的工单映射
        Map<String, WorkOrderVO> workOrderMap = new HashMap<>();
        WorkOrderVO workOrder = new WorkOrderVO();
        workOrder.setId(TEST_ORDER_ID_1);
        workOrder.setQuantity(new BigDecimal("100"));
        // 不设置 topOrderId
        workOrderMap.put(TEST_ORDER_ID_1, workOrder);
        
        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getTopWorkOrderMap", String.class, Map.class);
        method.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, WorkOrderVO> result = (Map<String, WorkOrderVO>) method
                .invoke(demandDeliveryProductionService, TEST_SCENARIO, workOrderMap);

        // 验证结果 - 应返回原工单映射作为备选
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(TEST_ORDER_ID_1));
        
        // 验证没有调用外部服务
        verify(mpsFeign, never()).selectWorkOrderByParams(any(), any());
    }

    /**
     * 测试 calculateScheduleQty 方法 - 正常场景
     */
    @Test
    void testCalculateScheduleQty_NormalCase() throws Exception {
        // 准备测试数据
        List<MasterPlanTaskVO> masterPlanTasks = createFormingTasks();
        Map<String, WorkOrderVO> workOrderMap = createWorkOrderMap();
        Map<String, WorkOrderVO> topWorkOrderMap = createTopWorkOrderMap();

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("calculateScheduleQty", String.class, List.class, Map.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                TEST_PRODUCT_CODE, masterPlanTasks, workOrderMap, topWorkOrderMap);

        // 验证结果 - 应该是匹配的顶层工单数量之和
        assertNotNull(result);
        assertEquals(new BigDecimal("300"), result); // 200 + 100
    }

    /**
     * 测试 calculateScheduleQty 方法 - 空产品编码
     */
    @Test
    void testCalculateScheduleQty_BlankProductCode() throws Exception {
        List<MasterPlanTaskVO> masterPlanTasks = createFormingTasks();
        Map<String, WorkOrderVO> workOrderMap = createWorkOrderMap();
        Map<String, WorkOrderVO> topWorkOrderMap = createTopWorkOrderMap();

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("calculateScheduleQty", String.class, List.class, Map.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                "", masterPlanTasks, workOrderMap, topWorkOrderMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result);
    }

    /**
     * 测试 calculateScheduleQty 方法 - 空主生产计划任务
     */
    @Test
    void testCalculateScheduleQty_EmptyMasterPlanTasks() throws Exception {
        Map<String, WorkOrderVO> workOrderMap = createWorkOrderMap();
        Map<String, WorkOrderVO> topWorkOrderMap = createTopWorkOrderMap();

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("calculateScheduleQty", String.class, List.class, Map.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                TEST_PRODUCT_CODE, new ArrayList<>(), workOrderMap, topWorkOrderMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result);
    }

    /**
     * 测试 calculateScheduleQty 方法 - 没有成型工序任务
     */
    @Test
    void testCalculateScheduleQty_NoFormingTasks() throws Exception {
        // 创建非成型工序任务
        List<MasterPlanTaskVO> masterPlanTasks = new ArrayList<>();
        MasterPlanTaskVO task = new MasterPlanTaskVO();
        task.setStandardStepName("包装"); // 非成型工序
        OperationVO operation = new OperationVO();
        operation.setOrderId(TEST_ORDER_ID_1);
        task.setOperationVO(operation);
        masterPlanTasks.add(task);
        
        Map<String, WorkOrderVO> workOrderMap = createWorkOrderMap();
        Map<String, WorkOrderVO> topWorkOrderMap = createTopWorkOrderMap();

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("calculateScheduleQty", String.class, List.class, Map.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                TEST_PRODUCT_CODE, masterPlanTasks, workOrderMap, topWorkOrderMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result);
    }

    /**
     * 测试 getTopWorkOrderQuantity 方法 - 正常场景
     */
    @Test
    void testGetTopWorkOrderQuantity_NormalCase() throws Exception {
        // 准备测试数据
        WorkOrderVO workOrder = new WorkOrderVO();
        workOrder.setId(TEST_ORDER_ID_1);
        workOrder.setTopOrderId(TEST_TOP_ORDER_ID_1);
        workOrder.setQuantity(new BigDecimal("100"));
        
        Map<String, WorkOrderVO> topWorkOrderMap = createTopWorkOrderMap();

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getTopWorkOrderQuantity", WorkOrderVO.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                workOrder, topWorkOrderMap);

        // 验证结果 - 应该返回顶层工单的数量
        assertNotNull(result);
        assertEquals(new BigDecimal("200"), result);
    }

    /**
     * 测试 getTopWorkOrderQuantity 方法 - 没有顶层工单ID
     */
    @Test
    void testGetTopWorkOrderQuantity_NoTopOrderId() throws Exception {
        // 准备测试数据
        WorkOrderVO workOrder = new WorkOrderVO();
        workOrder.setId(TEST_ORDER_ID_1);
        // 不设置 topOrderId
        workOrder.setQuantity(new BigDecimal("100"));
        
        Map<String, WorkOrderVO> topWorkOrderMap = createTopWorkOrderMap();

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getTopWorkOrderQuantity", WorkOrderVO.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                workOrder, topWorkOrderMap);

        // 验证结果 - 应该返回当前工单的数量
        assertNotNull(result);
        assertEquals(new BigDecimal("100"), result);
    }

    /**
     * 测试 getTopWorkOrderQuantity 方法 - 顶层工单不存在
     */
    @Test
    void testGetTopWorkOrderQuantity_TopWorkOrderNotExists() throws Exception {
        // 准备测试数据
        WorkOrderVO workOrder = new WorkOrderVO();
        workOrder.setId(TEST_ORDER_ID_1);
        workOrder.setTopOrderId("NON_EXISTENT_TOP_ORDER");
        workOrder.setQuantity(new BigDecimal("100"));
        
        Map<String, WorkOrderVO> topWorkOrderMap = createTopWorkOrderMap();

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getTopWorkOrderQuantity", WorkOrderVO.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                workOrder, topWorkOrderMap);

        // 验证结果 - 应该返回当前工单的数量
        assertNotNull(result);
        assertEquals(new BigDecimal("100"), result);
    }

    /**
     * 测试 matchesProduct 方法 - 产品编码直接匹配
     */
    @Test
    void testMatchesProduct_DirectProductCodeMatch() throws Exception {
        // 准备测试数据
        WorkOrderVO workOrder = new WorkOrderVO();
        workOrder.setId(TEST_ORDER_ID_1);
        workOrder.setProductCode(TEST_PRODUCT_CODE);
        workOrder.setParentProductCode("PARENT_PRODUCT_001");

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("matchesProduct", WorkOrderVO.class, String.class);
        method.setAccessible(true);
        
        boolean result = (boolean) method.invoke(demandDeliveryProductionService, 
                workOrder, TEST_PRODUCT_CODE);

        // 验证结果
        assertTrue(result);
    }

    /**
     * 测试 matchesProduct 方法 - 父产品编码匹配
     */
    @Test
    void testMatchesProduct_ParentProductCodeMatch() throws Exception {
        // 准备测试数据
        WorkOrderVO workOrder = new WorkOrderVO();
        workOrder.setId(TEST_ORDER_ID_1);
        workOrder.setProductCode("OTHER_PRODUCT_001");
        workOrder.setParentProductCode(TEST_PRODUCT_CODE);

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("matchesProduct", WorkOrderVO.class, String.class);
        method.setAccessible(true);
        
        boolean result = (boolean) method.invoke(demandDeliveryProductionService, 
                workOrder, TEST_PRODUCT_CODE);

        // 验证结果
        assertTrue(result);
    }

    /**
     * 测试 matchesProduct 方法 - 不匹配
     */
    @Test
    void testMatchesProduct_NoMatch() throws Exception {
        // 准备测试数据
        WorkOrderVO workOrder = new WorkOrderVO();
        workOrder.setId(TEST_ORDER_ID_1);
        workOrder.setProductCode("OTHER_PRODUCT_001");
        workOrder.setParentProductCode("PARENT_PRODUCT_001");

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("matchesProduct", WorkOrderVO.class, String.class);
        method.setAccessible(true);
        
        boolean result = (boolean) method.invoke(demandDeliveryProductionService, 
                workOrder, TEST_PRODUCT_CODE);

        // 验证结果
        assertFalse(result);
    }

    /**
     * 测试 matchesProduct 方法 - 空工单对象
     */
    @Test
    void testMatchesProduct_NullWorkOrder() throws Exception {
        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("matchesProduct", WorkOrderVO.class, String.class);
        method.setAccessible(true);
        
        boolean result = (boolean) method.invoke(demandDeliveryProductionService, 
                null, TEST_PRODUCT_CODE);

        // 验证结果
        assertFalse(result);
    }

    /**
     * 测试 matchesProduct 方法 - 空产品编码
     */
    @Test
    void testMatchesProduct_BlankProductCode() throws Exception {
        // 准备测试数据
        WorkOrderVO workOrder = new WorkOrderVO();
        workOrder.setId(TEST_ORDER_ID_1);
        workOrder.setProductCode(TEST_PRODUCT_CODE);

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("matchesProduct", WorkOrderVO.class, String.class);
        method.setAccessible(true);
        
        boolean result = (boolean) method.invoke(demandDeliveryProductionService, 
                workOrder, "");

        // 验证结果
        assertFalse(result);
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用的主生产计划任务列表
     */
    private List<MasterPlanTaskVO> createMasterPlanTasks() {
        List<MasterPlanTaskVO> tasks = new ArrayList<>();
        
        // 任务1
        MasterPlanTaskVO task1 = new MasterPlanTaskVO();
        task1.setStandardStepName(PACKAGING_OPERATION);
        OperationVO operation1 = new OperationVO();
        operation1.setOrderId(TEST_ORDER_ID_1);
        task1.setOperationVO(operation1);
        tasks.add(task1);
        
        // 任务2
        MasterPlanTaskVO task2 = new MasterPlanTaskVO();
        task2.setStandardStepName(PACKAGING_OPERATION);
        OperationVO operation2 = new OperationVO();
        operation2.setOrderId(TEST_ORDER_ID_2);
        task2.setOperationVO(operation2);
        tasks.add(task2);
        
        return tasks;
    }

    /**
     * 创建测试用的成型工序任务列表（修正：参照MPS服务逻辑使用成型工序）
     */
    private List<MasterPlanTaskVO> createFormingTasks() {
        List<MasterPlanTaskVO> tasks = new ArrayList<>();
        
        // 任务1 - 成型工序
        MasterPlanTaskVO task1 = new MasterPlanTaskVO();
        task1.setStandardStepName(FORMING_OPERATION);
        OperationVO operation1 = new OperationVO();
        operation1.setOrderId(TEST_ORDER_ID_1);
        task1.setOperationVO(operation1);
        tasks.add(task1);
        
        // 任务2 - 成型工序
        MasterPlanTaskVO task2 = new MasterPlanTaskVO();
        task2.setStandardStepName(FORMING_OPERATION);
        OperationVO operation2 = new OperationVO();
        operation2.setOrderId(TEST_ORDER_ID_2);
        task2.setOperationVO(operation2);
        tasks.add(task2);
        
        return tasks;
    }
    
    /**
     * 创建测试用的包装工序任务列表（保留用于其他测试）
     */
    private List<MasterPlanTaskVO> createPackagingTasks() {
        return createMasterPlanTasks();
    }

    /**
     * 创建测试用的工单列表
     */
    private List<WorkOrderVO> createMockWorkOrders() {
        List<WorkOrderVO> workOrders = new ArrayList<>();
        
        // 工单1
        WorkOrderVO workOrder1 = new WorkOrderVO();
        workOrder1.setId(TEST_ORDER_ID_1);
        workOrder1.setQuantity(new BigDecimal("100"));
        workOrder1.setTopOrderId(TEST_TOP_ORDER_ID_1);
        workOrder1.setProductCode(TEST_PRODUCT_CODE);
        workOrders.add(workOrder1);
        
        // 工单2
        WorkOrderVO workOrder2 = new WorkOrderVO();
        workOrder2.setId(TEST_ORDER_ID_2);
        workOrder2.setQuantity(new BigDecimal("150"));
        workOrder2.setTopOrderId(TEST_TOP_ORDER_ID_2);
        workOrder2.setParentProductCode(TEST_PRODUCT_CODE);
        workOrders.add(workOrder2);
        
        return workOrders;
    }

    /**
     * 创建测试用的顶层工单列表
     */
    private List<WorkOrderVO> createMockTopWorkOrders() {
        List<WorkOrderVO> topWorkOrders = new ArrayList<>();
        
        // 顶层工单1
        WorkOrderVO topWorkOrder1 = new WorkOrderVO();
        topWorkOrder1.setId(TEST_TOP_ORDER_ID_1);
        topWorkOrder1.setQuantity(new BigDecimal("200"));
        topWorkOrder1.setProductCode(TEST_PRODUCT_CODE);
        topWorkOrders.add(topWorkOrder1);
        
        // 顶层工单2
        WorkOrderVO topWorkOrder2 = new WorkOrderVO();
        topWorkOrder2.setId(TEST_TOP_ORDER_ID_2);
        topWorkOrder2.setQuantity(new BigDecimal("100"));
        topWorkOrder2.setParentProductCode(TEST_PRODUCT_CODE);
        topWorkOrders.add(topWorkOrder2);
        
        return topWorkOrders;
    }

    /**
     * 创建测试用的工单映射
     */
    private Map<String, WorkOrderVO> createWorkOrderMap() {
        Map<String, WorkOrderVO> workOrderMap = new HashMap<>();
        List<WorkOrderVO> workOrders = createMockWorkOrders();
        
        for (WorkOrderVO workOrder : workOrders) {
            workOrderMap.put(workOrder.getId(), workOrder);
        }
        
        return workOrderMap;
    }

    /**
     * 创建测试用的顶层工单映射
     */
    private Map<String, WorkOrderVO> createTopWorkOrderMap() {
        Map<String, WorkOrderVO> topWorkOrderMap = new HashMap<>();
        List<WorkOrderVO> topWorkOrders = createMockTopWorkOrders();
        
        for (WorkOrderVO topWorkOrder : topWorkOrders) {
            topWorkOrderMap.put(topWorkOrder.getId(), topWorkOrder);
        }
        
        return topWorkOrderMap;
    }
}
 
   /**
     * 测试 calculateScheduleQty 方法 - 工单不存在
     */
    @Test
    void testCalculateScheduleQty_WorkOrderNotExists() throws Exception {
        // 准备测试数据 - 创建引用不存在工单的任务
        List<MasterPlanTaskVO> masterPlanTasks = new ArrayList<>();
        MasterPlanTaskVO task = new MasterPlanTaskVO();
        task.setStandardStepName(PACKAGING_OPERATION);
        OperationVO operation = new OperationVO();
        operation.setOrderId("NON_EXISTENT_ORDER");
        task.setOperationVO(operation);
        masterPlanTasks.add(task);
        
        Map<String, WorkOrderVO> workOrderMap = createWorkOrderMap();
        Map<String, WorkOrderVO> topWorkOrderMap = createTopWorkOrderMap();

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("calculateScheduleQty", String.class, List.class, Map.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                TEST_PRODUCT_CODE, masterPlanTasks, workOrderMap, topWorkOrderMap);

        // 验证结果 - 工单不存在时应返回0
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result);
    }

    /**
     * 测试 calculateScheduleQty 方法 - 包含null任务
     */
    @Test
    void testCalculateScheduleQty_WithNullTasks() throws Exception {
        // 准备测试数据 - 包含null任务
        List<MasterPlanTaskVO> masterPlanTasks = new ArrayList<>();
        masterPlanTasks.add(null); // null任务
        
        MasterPlanTaskVO validTask = new MasterPlanTaskVO();
        validTask.setStandardStepName(PACKAGING_OPERATION);
        OperationVO operation = new OperationVO();
        operation.setOrderId(TEST_ORDER_ID_1);
        validTask.setOperationVO(operation);
        masterPlanTasks.add(validTask);
        
        Map<String, WorkOrderVO> workOrderMap = createWorkOrderMap();
        Map<String, WorkOrderVO> topWorkOrderMap = createTopWorkOrderMap();

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("calculateScheduleQty", String.class, List.class, Map.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                TEST_PRODUCT_CODE, masterPlanTasks, workOrderMap, topWorkOrderMap);

        // 验证结果 - 应该跳过null任务，只处理有效任务
        assertNotNull(result);
        assertEquals(new BigDecimal("200"), result); // 只有一个有效任务的顶层工单数量
    }

    /**
     * 测试 calculateScheduleQty 方法 - 任务的OperationVO为null
     */
    @Test
    void testCalculateScheduleQty_NullOperationVO() throws Exception {
        // 准备测试数据
        List<MasterPlanTaskVO> masterPlanTasks = new ArrayList<>();
        MasterPlanTaskVO task = new MasterPlanTaskVO();
        task.setStandardStepName(PACKAGING_OPERATION);
        // 不设置 operationVO
        masterPlanTasks.add(task);
        
        Map<String, WorkOrderVO> workOrderMap = createWorkOrderMap();
        Map<String, WorkOrderVO> topWorkOrderMap = createTopWorkOrderMap();

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("calculateScheduleQty", String.class, List.class, Map.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                TEST_PRODUCT_CODE, masterPlanTasks, workOrderMap, topWorkOrderMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result);
    }

    /**
     * 测试 calculateScheduleQty 方法 - 顶层工单数量为0
     */
    @Test
    void testCalculateScheduleQty_ZeroTopWorkOrderQuantity() throws Exception {
        // 准备测试数据
        List<MasterPlanTaskVO> masterPlanTasks = createFormingTasks();
        Map<String, WorkOrderVO> workOrderMap = createWorkOrderMap();
        
        // 创建数量为0的顶层工单映射
        Map<String, WorkOrderVO> topWorkOrderMap = new HashMap<>();
        WorkOrderVO topWorkOrder = new WorkOrderVO();
        topWorkOrder.setId(TEST_TOP_ORDER_ID_1);
        topWorkOrder.setQuantity(BigDecimal.ZERO); // 数量为0
        topWorkOrder.setProductCode(TEST_PRODUCT_CODE);
        topWorkOrderMap.put(TEST_TOP_ORDER_ID_1, topWorkOrder);

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("calculateScheduleQty", String.class, List.class, Map.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                TEST_PRODUCT_CODE, masterPlanTasks, workOrderMap, topWorkOrderMap);

        // 验证结果 - 数量为0的工单应该被跳过
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result);
    }

    /**
     * 测试 getTopWorkOrderQuantity 方法 - 空工单对象
     */
    @Test
    void testGetTopWorkOrderQuantity_NullWorkOrder() throws Exception {
        Map<String, WorkOrderVO> topWorkOrderMap = createTopWorkOrderMap();

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getTopWorkOrderQuantity", WorkOrderVO.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                null, topWorkOrderMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result);
    }

    /**
     * 测试 getTopWorkOrderQuantity 方法 - 空顶层工单映射
     */
    @Test
    void testGetTopWorkOrderQuantity_NullTopWorkOrderMap() throws Exception {
        // 准备测试数据
        WorkOrderVO workOrder = new WorkOrderVO();
        workOrder.setId(TEST_ORDER_ID_1);
        workOrder.setTopOrderId(TEST_TOP_ORDER_ID_1);
        workOrder.setQuantity(new BigDecimal("100"));

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getTopWorkOrderQuantity", WorkOrderVO.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                workOrder, null);

        // 验证结果 - 应该返回当前工单的数量
        assertNotNull(result);
        assertEquals(new BigDecimal("100"), result);
    }

    /**
     * 测试 getTopWorkOrderQuantity 方法 - 工单数量为null
     */
    @Test
    void testGetTopWorkOrderQuantity_NullWorkOrderQuantity() throws Exception {
        // 准备测试数据
        WorkOrderVO workOrder = new WorkOrderVO();
        workOrder.setId(TEST_ORDER_ID_1);
        workOrder.setTopOrderId(TEST_TOP_ORDER_ID_1);
        // 不设置 quantity
        
        Map<String, WorkOrderVO> topWorkOrderMap = createTopWorkOrderMap();

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getTopWorkOrderQuantity", WorkOrderVO.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                workOrder, topWorkOrderMap);

        // 验证结果 - 应该返回顶层工单的数量
        assertNotNull(result);
        assertEquals(new BigDecimal("200"), result);
    }

    /**
     * 测试 getTopWorkOrderQuantity 方法 - 顶层工单数量为null
     */
    @Test
    void testGetTopWorkOrderQuantity_NullTopWorkOrderQuantity() throws Exception {
        // 准备测试数据
        WorkOrderVO workOrder = new WorkOrderVO();
        workOrder.setId(TEST_ORDER_ID_1);
        workOrder.setTopOrderId(TEST_TOP_ORDER_ID_1);
        workOrder.setQuantity(new BigDecimal("100"));
        
        // 创建顶层工单数量为null的映射
        Map<String, WorkOrderVO> topWorkOrderMap = new HashMap<>();
        WorkOrderVO topWorkOrder = new WorkOrderVO();
        topWorkOrder.setId(TEST_TOP_ORDER_ID_1);
        // 不设置 quantity
        topWorkOrderMap.put(TEST_TOP_ORDER_ID_1, topWorkOrder);

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getTopWorkOrderQuantity", WorkOrderVO.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                workOrder, topWorkOrderMap);

        // 验证结果 - 应该返回当前工单的数量
        assertNotNull(result);
        assertEquals(new BigDecimal("100"), result);
    }

    /**
     * 测试 getWorkOrderMap 方法 - 包含无效任务数据
     */
    @Test
    void testGetWorkOrderMap_WithInvalidTasks() throws Exception {
        // 准备测试数据 - 包含各种无效任务
        List<MasterPlanTaskVO> operationTasks = new ArrayList<>();
        
        // null任务
        operationTasks.add(null);
        
        // operationVO为null的任务
        MasterPlanTaskVO taskWithNullOperation = new MasterPlanTaskVO();
        operationTasks.add(taskWithNullOperation);
        
        // orderId为空的任务
        MasterPlanTaskVO taskWithEmptyOrderId = new MasterPlanTaskVO();
        OperationVO operationWithEmptyId = new OperationVO();
        operationWithEmptyId.setOrderId("");
        taskWithEmptyOrderId.setOperationVO(operationWithEmptyId);
        operationTasks.add(taskWithEmptyOrderId);
        
        // 有效任务
        MasterPlanTaskVO validTask = new MasterPlanTaskVO();
        OperationVO validOperation = new OperationVO();
        validOperation.setOrderId(TEST_ORDER_ID_1);
        validTask.setOperationVO(validOperation);
        operationTasks.add(validTask);
        
        List<WorkOrderVO> mockWorkOrders = Arrays.asList(createMockWorkOrders().get(0));
        
        // Mock 外部调用
        when(mpsFeign.selectWorkOrderByParams(eq(TEST_SCENARIO), any(Map.class)))
                .thenReturn(mockWorkOrders);

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getWorkOrderMap", String.class, List.class);
        method.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, WorkOrderVO> result = (Map<String, WorkOrderVO>) method
                .invoke(demandDeliveryProductionService, TEST_SCENARIO, operationTasks);

        // 验证结果 - 应该只处理有效任务
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(TEST_ORDER_ID_1));
        
        // 验证 Mock 调用
        verify(mpsFeign, times(1)).selectWorkOrderByParams(eq(TEST_SCENARIO), any(Map.class));
    }

    /**
     * 测试 getWorkOrderMap 方法 - 查询结果包含无效工单
     */
    @Test
    void testGetWorkOrderMap_WithInvalidWorkOrders() throws Exception {
        // 准备测试数据
        List<MasterPlanTaskVO> operationTasks = createMasterPlanTasks();
        
        // 创建包含无效工单的结果
        List<WorkOrderVO> mockWorkOrders = new ArrayList<>();
        
        // null工单
        mockWorkOrders.add(null);
        
        // ID为空的工单
        WorkOrderVO workOrderWithEmptyId = new WorkOrderVO();
        workOrderWithEmptyId.setQuantity(new BigDecimal("100"));
        mockWorkOrders.add(workOrderWithEmptyId);
        
        // 有效工单
        WorkOrderVO validWorkOrder = new WorkOrderVO();
        validWorkOrder.setId(TEST_ORDER_ID_1);
        validWorkOrder.setQuantity(new BigDecimal("100"));
        mockWorkOrders.add(validWorkOrder);
        
        // Mock 外部调用
        when(mpsFeign.selectWorkOrderByParams(eq(TEST_SCENARIO), any(Map.class)))
                .thenReturn(mockWorkOrders);

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getWorkOrderMap", String.class, List.class);
        method.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, WorkOrderVO> result = (Map<String, WorkOrderVO>) method
                .invoke(demandDeliveryProductionService, TEST_SCENARIO, operationTasks);

        // 验证结果 - 应该只包含有效工单
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(TEST_ORDER_ID_1));
        
        // 验证 Mock 调用
        verify(mpsFeign, times(1)).selectWorkOrderByParams(eq(TEST_SCENARIO), any(Map.class));
    }

    /**
     * 测试 getTopWorkOrderMap 方法 - 数据库异常
     */
    @Test
    void testGetTopWorkOrderMap_DatabaseException() throws Exception {
        // 准备测试数据
        Map<String, WorkOrderVO> workOrderMap = createWorkOrderMap();
        
        // Mock 数据库异常
        when(mpsFeign.selectWorkOrderByParams(eq(TEST_SCENARIO), any(Map.class)))
                .thenThrow(new RuntimeException("Database connection failed"));

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("getTopWorkOrderMap", String.class, Map.class);
        method.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, WorkOrderVO> result = (Map<String, WorkOrderVO>) method
                .invoke(demandDeliveryProductionService, TEST_SCENARIO, workOrderMap);

        // 验证结果 - 异常时应返回原工单映射作为备选
        assertNotNull(result);
        assertEquals(workOrderMap.size(), result.size());
        
        // 验证调用了外部服务
        verify(mpsFeign, times(1)).selectWorkOrderByParams(eq(TEST_SCENARIO), any(Map.class));
    }

    /**
     * 测试 calculateScheduleQty 方法 - 复合场景测试
     */
    @Test
    void testCalculateScheduleQty_ComplexScenario() throws Exception {
        // 准备复杂测试数据
        List<MasterPlanTaskVO> masterPlanTasks = new ArrayList<>();
        
        // 有效的包装任务1 - 产品编码匹配
        MasterPlanTaskVO task1 = new MasterPlanTaskVO();
        task1.setStandardStepName(PACKAGING_OPERATION);
        OperationVO operation1 = new OperationVO();
        operation1.setOrderId(TEST_ORDER_ID_1);
        task1.setOperationVO(operation1);
        masterPlanTasks.add(task1);
        
        // 有效的包装任务2 - 父产品编码匹配
        MasterPlanTaskVO task2 = new MasterPlanTaskVO();
        task2.setStandardStepName(PACKAGING_OPERATION);
        OperationVO operation2 = new OperationVO();
        operation2.setOrderId(TEST_ORDER_ID_2);
        task2.setOperationVO(operation2);
        masterPlanTasks.add(task2);
        
        // 无效任务 - 非包装工序
        MasterPlanTaskVO task3 = new MasterPlanTaskVO();
        task3.setStandardStepName("成型");
        OperationVO operation3 = new OperationVO();
        operation3.setOrderId("OTHER_ORDER");
        task3.setOperationVO(operation3);
        masterPlanTasks.add(task3);
        
        // 无效任务 - 工单不存在
        MasterPlanTaskVO task4 = new MasterPlanTaskVO();
        task4.setStandardStepName(PACKAGING_OPERATION);
        OperationVO operation4 = new OperationVO();
        operation4.setOrderId("NON_EXISTENT_ORDER");
        task4.setOperationVO(operation4);
        masterPlanTasks.add(task4);
        
        Map<String, WorkOrderVO> workOrderMap = createWorkOrderMap();
        Map<String, WorkOrderVO> topWorkOrderMap = createTopWorkOrderMap();

        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("calculateScheduleQty", String.class, List.class, Map.class, Map.class);
        method.setAccessible(true);
        
        BigDecimal result = (BigDecimal) method.invoke(demandDeliveryProductionService, 
                TEST_PRODUCT_CODE, masterPlanTasks, workOrderMap, topWorkOrderMap);

        // 验证结果 - 应该是两个有效任务的顶层工单数量之和
        assertNotNull(result);
        assertEquals(new BigDecimal("300"), result); // 200 + 100
    }

    /**
     * 测试产品匹配逻辑的边界情况
     */
    @Test
    void testMatchesProduct_EdgeCases() throws Exception {
        // 调用私有方法
        Method method = DemandDeliveryProductionServiceImpl.class
                .getDeclaredMethod("matchesProduct", WorkOrderVO.class, String.class);
        method.setAccessible(true);
        
        // 测试产品编码为null的情况
        WorkOrderVO workOrderWithNullProductCode = new WorkOrderVO();
        workOrderWithNullProductCode.setId(TEST_ORDER_ID_1);
        workOrderWithNullProductCode.setParentProductCode(TEST_PRODUCT_CODE);
        
        boolean result1 = (boolean) method.invoke(demandDeliveryProductionService, 
                workOrderWithNullProductCode, TEST_PRODUCT_CODE);
        assertTrue(result1); // 应该通过父产品编码匹配
        
        // 测试父产品编码为null的情况
        WorkOrderVO workOrderWithNullParentCode = new WorkOrderVO();
        workOrderWithNullParentCode.setId(TEST_ORDER_ID_1);
        workOrderWithNullParentCode.setProductCode(TEST_PRODUCT_CODE);
        
        boolean result2 = (boolean) method.invoke(demandDeliveryProductionService, 
                workOrderWithNullParentCode, TEST_PRODUCT_CODE);
        assertTrue(result2); // 应该通过产品编码匹配
        
        // 测试两个编码都为null的情况
        WorkOrderVO workOrderWithNullCodes = new WorkOrderVO();
        workOrderWithNullCodes.setId(TEST_ORDER_ID_1);
        
        boolean result3 = (boolean) method.invoke(demandDeliveryProductionService, 
                workOrderWithNullCodes, TEST_PRODUCT_CODE);
        assertFalse(result3); // 应该不匹配
    }
}