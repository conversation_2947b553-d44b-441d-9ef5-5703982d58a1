package com.yhl.scp.dfp.report.controller;

import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.report.dto.DemandDeliveryProductionDetailDTO;
import com.yhl.scp.dfp.report.service.DemandDeliveryProductionService;
import com.yhl.scp.dfp.report.vo.DemandDeliveryProductionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>DemandDeliveryProductionController</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-15 10:40:27
 */
@Slf4j
@Api(tags = "需求&发货&排产报表")
@RestController
@RequestMapping("demandDeliveryProduction")
public class DemandDeliveryProductionController extends BaseController {
    @Resource
    private DemandDeliveryProductionService demandDeliveryProductionService;
    @ApiOperation(value = "全量查询（取消分页）")
    @PostMapping(value = "queryDemandDeliveryProductionReport")
    public BaseResponse<List<DemandDeliveryProductionVO>> page(@RequestBody DemandDeliveryProductionDetailDTO dto) {
        List<DemandDeliveryProductionVO> dataList = demandDeliveryProductionService.queryDemandDeliveryProductionReportWithoutPagination(dto);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, dataList);
    }

    @ApiOperation(value = "导出数据")
    @GetMapping(value = "exportData")
    public void exportData(String productCode, String vehicleModelCode, Boolean inWarehouseWarning, HttpServletResponse response) {
        DemandDeliveryProductionDetailDTO dto = new DemandDeliveryProductionDetailDTO();
        dto.setProductCode(productCode);
        dto.setVehicleModelCode(vehicleModelCode);
        dto.setInWarehouseWarning(inWarehouseWarning);
        demandDeliveryProductionService.exportData(dto, response);
    }
}
