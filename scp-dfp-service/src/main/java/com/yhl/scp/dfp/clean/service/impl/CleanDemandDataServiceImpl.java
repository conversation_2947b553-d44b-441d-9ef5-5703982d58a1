package com.yhl.scp.dfp.clean.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassAReportVO;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBDedicatedReportVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yhl.platform.common.CustomThreadPoolFactory;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.dfp.calendar.service.DfpResourceCalendarService;
import com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.dfp.clean.convertor.CleanDemandDataConvertor;
import com.yhl.scp.dfp.clean.domain.entity.CleanDemandDataDO;
import com.yhl.scp.dfp.clean.domain.service.CleanDemandDataDomainService;
import com.yhl.scp.dfp.clean.dto.CleanDemandDataDTO;
import com.yhl.scp.dfp.clean.dto.CleanDemandDataDetailDTO;
import com.yhl.scp.dfp.clean.dto.CleanDemandProductInventoryReportDTO;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanDemandDataDao;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanDemandDataDetailDao;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanDemandDataDetailPO;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanDemandDataPO;
import com.yhl.scp.dfp.clean.service.CleanDemandDataDetailExitService;
import com.yhl.scp.dfp.clean.service.CleanDemandDataDetailService;
import com.yhl.scp.dfp.clean.service.CleanDemandDataService;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataDetailExitVO;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataDetailVO;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataVO;
import com.yhl.scp.dfp.clean.vo.CleanDemandProductInventoryCollectVO;
import com.yhl.scp.dfp.clean.vo.CleanDemandProductInventoryReportVO;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.DemandTypeEnum;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.common.enums.PublishStatusEnum;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastDataDao;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastDataDetailDao;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastVersionDao;
import com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataDetailPO;
import com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataPO;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataDetailService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastVersionService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.delivery.domain.service.DeliveryPlanDomainService;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedDao;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanVersionService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedMonthVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandVersionDao;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandVersionPO;
import com.yhl.scp.dfp.demand.service.DemandVersionService;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionFutureVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.oem.enums.OemBusinessTypeEnum;
import com.yhl.scp.dfp.oem.enums.OemTradeTypeEnum;
import com.yhl.scp.dfp.oem.service.OemProductLineMapService;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemProductLineMapVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.origin.infrastructure.dao.OriginDemandVersionDao;
import com.yhl.scp.dfp.stock.service.InventoryBatchDetailService;
import com.yhl.scp.dfp.stock.service.InventoryShiftService;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;
import com.yhl.scp.dfp.switchrelation.service.SwitchRelationBetweenProductService;
import com.yhl.scp.dfp.switchrelation.vo.SwitchRelationVO;
import com.yhl.scp.dfp.warehouse.infrastructure.dao.WarehouseReleaseRecordDao;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseMonthVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;

import cn.hutool.core.map.MapUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>CleanDemandDataServiceImpl</code>
 * <p>
 * 日需求数据应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:57
 */
@Slf4j
@Service
public class CleanDemandDataServiceImpl extends AbstractService implements CleanDemandDataService {

    @Resource
    private CleanDemandDataDao cleanDemandDataDao;

    @Resource
    private CleanDemandDataDetailDao cleanDemandDataDetailDao;

    @Resource
    private DemandVersionDao demandVersionDao;

    @Resource
    private OriginDemandVersionDao originDemandVersionDao;

    @Resource
    private CleanDemandDataDomainService cleanDemandDataDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private ConsistenceDemandForecastDataDao consistenceDemandForecastDataDao;

    @Resource
    private ConsistenceDemandForecastDataDetailDao consistenceDemandForecastDataDetailDao;

    @Resource
    private ConsistenceDemandForecastVersionDao consistenceDemandForecastVersionDao;

    @Resource
    private LoadingDemandSubmissionService loadingDemandSubmissionService;

    @Resource
    private LoadingDemandSubmissionDetailService loadingDemandSubmissionDetailService;

    @Resource
    private CleanDemandDataService cleanDemandDataService;

    @Resource
    private CleanDemandDataDetailService cleanDemandDataDetailService;

    public static final String YMD_PATTERN = "yyyyMMdd";

    public static final String YM_PATTERN = "yyyyMM";

    @Resource
    protected NewMdsFeign newMdsFeign;

    @Resource
    private OemProductLineMapService oemProductLineMapService;

    @Resource
    private WarehouseReleaseRecordService warehouseReleaseRecordService;

    @Resource
    private OemVehicleModelService oemVehicleModelService;

    @Resource
    DfpResourceCalendarService dfpResourceCalendarService;

    @Resource
    private DeliveryPlanDomainService deliveryPlanDomainService;
    
    @Resource
    private WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;
    
    @Resource
    private IpsNewFeign ipsNewFeign;
    
    @Resource
    private InventoryBatchDetailService inventoryBatchDetailService;
    
    @Resource
    private DeliveryPlanVersionService deliveryPlanVersionService;
    
    @Resource
    private InventoryShiftService inventoryShiftService;
    
    @Resource
    private CleanDemandDataDetailExitService cleanDemandDataDetailExitService;
    
    @Resource
    private IpsFeign ipsFeign;
    
    @Resource
    private ConsistenceDemandForecastVersionService consistenceDemandForecastVersionService;
    
    @Resource
    private ConsistenceDemandForecastDataService consistenceDemandForecastDataService;
    
    @Resource
    private ConsistenceDemandForecastDataDetailService consistenceDemandForecastDataDetailService;
    
    @Resource
    private SwitchRelationBetweenProductService switchRelationBetweenProductService;
    
    @Resource
    private DeliveryPlanPublishedDao deliveryPlanPublishedDao;
    
    @Resource
    private OemService oemService;
    
    @Resource
    private WarehouseReleaseRecordDao warehouseReleaseRecordDao;
    
    @Resource
    private MpsFeign mpsFeign;
    
    @Resource
    private DemandVersionService demandVersionService;

    @Resource
    private MrpFeign mrpFeign;

    @Override
    public BaseResponse<Void> doCreate(CleanDemandDataDTO cleanDemandDataDTO) {
        // 0.数据转换
        CleanDemandDataDO cleanDemandDataDO = CleanDemandDataConvertor.INSTANCE.dto2Do(cleanDemandDataDTO);
        CleanDemandDataPO cleanDemandDataPO = CleanDemandDataConvertor.INSTANCE.dto2Po(cleanDemandDataDTO);
        // 1.数据校验
        cleanDemandDataDomainService.validation(cleanDemandDataDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(cleanDemandDataPO);
        cleanDemandDataDao.insertWithPrimaryKey(cleanDemandDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(CleanDemandDataDTO cleanDemandDataDTO) {
        // 0.数据转换
        CleanDemandDataDO cleanDemandDataDO = CleanDemandDataConvertor.INSTANCE.dto2Do(cleanDemandDataDTO);
        CleanDemandDataPO cleanDemandDataPO = CleanDemandDataConvertor.INSTANCE.dto2Po(cleanDemandDataDTO);
        // 1.数据校验
        cleanDemandDataDomainService.validation(cleanDemandDataDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(cleanDemandDataPO);
        cleanDemandDataDao.update(cleanDemandDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<CleanDemandDataDTO> list) {
        List<CleanDemandDataPO> newList = CleanDemandDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        cleanDemandDataDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<CleanDemandDataDTO> list) {
        List<CleanDemandDataPO> newList = CleanDemandDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        cleanDemandDataDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return cleanDemandDataDao.deleteBatch(idList);
        }
        return cleanDemandDataDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public CleanDemandDataVO selectByPrimaryKey(String id) {
        CleanDemandDataPO po = cleanDemandDataDao.selectByPrimaryKey(id);
        return CleanDemandDataConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_clean_demand_data")
    public List<CleanDemandDataVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_clean_demand_data")
    public List<CleanDemandDataVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<CleanDemandDataVO> dataList = cleanDemandDataDao.selectByCondition(sortParam, queryCriteriaParam);
        CleanDemandDataServiceImpl target = SpringBeanUtils.getBean(CleanDemandDataServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<CleanDemandDataVO> selectByParams(Map<String, Object> params) {
        List<CleanDemandDataPO> list = cleanDemandDataDao.selectByParams(params);
        return CleanDemandDataConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<CleanDemandDataVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.CLEAN_DEMAND_DATA.getCode();
    }

    @Override
    public List<CleanDemandDataVO> invocation(List<CleanDemandDataVO> dataList, Map<String, Object> params, String invocation) {
        if (CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }
        // 设置明细数据
        List<String> demandIds = dataList.stream().map(CleanDemandDataVO::getId).collect(Collectors.toList());

        List<CleanDemandDataDetailPO> cleanDemandDataDetailList = cleanDemandDataDetailDao.selectByCleanDemandDataIds(demandIds);
        Map<String, List<CleanDemandDataDetailPO>> detailMap = cleanDemandDataDetailList.stream()
                .collect(Collectors.groupingBy(CleanDemandDataDetailPO::getCleanDemandDataId));
        for (CleanDemandDataVO data : dataList) {
            List<CleanDemandDataDetailPO> details = detailMap.get(data.getId());
            if (CollectionUtils.isNotEmpty(details)) {
                Map<String, String> detailList = details.stream().collect(Collectors
                        .toMap(x -> DateUtils.dateToString(x.getDemandTime(), YMD_PATTERN),
                                x -> x.getDemandQuantity() == null ? "" : x.getDemandQuantity().toString(),
                                (t1, t2) -> t2));
                data.setDetailList(detailList);
            }
        }
        return dataList;
    }

    /**
     * 重新计算是在创建日需求版本之后，如果主机厂重新替换了原始需求，需要重新计算
     */
    @Override
    public List<String> doRecalculate(String versionCode) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("versionCode", versionCode);
        params.put("versionType", VersionTypeEnum.CLEAN_DEMAND.getCode());
        DemandVersionPO demandVersionPO = demandVersionDao.selectVersionInfoByParams(params);
        if (demandVersionPO == null) {
            throw new BusinessException("没有找到需要重新计算的日需求版本");
        }
        return processDataByVersionId(demandVersionPO.getId());
    }

    // 自动计算，定时任务
    @Override
    public void autoCalculate() {
        // 获取最新原始版本号
        String originVersionId = originDemandVersionDao.selectLatestVersionId();
        // 获取最新版本号
        Map<String, Object> params = new HashMap<>(2);
        params.put("originVersionId", originVersionId);
        params.put("versionType", VersionTypeEnum.CLEAN_DEMAND.getCode());
        DemandVersionPO demandVersionPO = demandVersionDao.selectVersionInfoByParams(params);
        if (demandVersionPO == null) {
            throw new BusinessException("没有找到自动生成的日需求版本");
        }
        processDataByVersionId(demandVersionPO.getId());
    }

    /**
     * 根据版本号处理数据
     *
     * @param versionId 版本ID
     */
    @SneakyThrows
    public List<String> processDataByVersionId(String versionId) {
    	// 查询当前用户负责的物料数据
        List<NewProductStockPointVO> newProductStockPointVOS =
                this.newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(),
                        ImmutableMap.of("orderPlanner", SystemHolder.getUserId(),
                        		"enabled", YesOrNoEnum.YES.getCode()));
        if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
            throw new BusinessException("当前用户没有配置物料数据权限");
        }
        List<String> orderPlannerProductCodes = newProductStockPointVOS.stream()
        		.map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
    	ThreadPoolExecutor threadPoolExecutor = CustomThreadPoolFactory.instance();
        // 获取对应版本数据
        List<CleanDemandDataPO> cleanDemandDataPOList = cleanDemandDataDao.selectByParams(ImmutableMap
        		.of("versionId", versionId, "productCodeList", orderPlannerProductCodes));
        List<String> deletedDataIds = cleanDemandDataPOList.stream().map(CleanDemandDataPO::getId)
    			.collect(Collectors.toList());
        // 过滤无效物料权限数据
        List<String> unEnabledProductCodes = newProductStockPointVOS.stream()
        		.filter( e-> YesOrNoEnum.NO.getCode().equals(e.getEnabled()))
        		.map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());

        List<String> analysisResults = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(unEnabledProductCodes)) {
            orderPlannerProductCodes.removeAll(unEnabledProductCodes);
            if (CollectionUtils.isEmpty(orderPlannerProductCodes)) {
                // 执行删除逻辑，不计算
                cleanDemandDataService.doDelete(deletedDataIds);
                cleanDemandDataDetailDao.deleteByDataIds(deletedDataIds);
                return analysisResults;
            }
        }
        
        //查询装车需求提报数据
        DemandVersionPO demandVersionPO = demandVersionDao.selectByPrimaryKey(versionId);
        Map<String, Object> params = new HashMap<>();
        params.put("versionId", demandVersionPO.getOriginVersionId());
        params.put("productCodes", orderPlannerProductCodes);
        List<LoadingDemandSubmissionVO> loadingDemandSubmissionVOS = loadingDemandSubmissionService.selectByParams(params)
                .stream().filter(item -> StringUtils.isNotBlank(item.getProductCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(loadingDemandSubmissionVOS)) {
        	// 执行删除逻辑，不计算
            cleanDemandDataService.doDelete(deletedDataIds);
            cleanDemandDataDetailDao.deleteByDataIds(deletedDataIds);
            return analysisResults;
        }
        Map<String,List<LoadingDemandSubmissionVO>> loadingDemandSubmissionDemandMap =
                loadingDemandSubmissionVOS.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionVO::getDemandCategory));
        //获取主机厂
        List<String> oemCodeList = loadingDemandSubmissionVOS.stream().map(LoadingDemandSubmissionVO::getOemCode)
                .distinct().collect(Collectors.toList());
        Map<String, String> oemBusinessTypeMap = oemService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), "oemCodes" , oemCodeList)).stream()
        		.collect(Collectors.toMap(OemVO::getOemCode,OemVO::getBusinessType,(v1, v2) -> v1));
        //获取装车需求提报主表id
        List<String> submissionIds = loadingDemandSubmissionVOS.stream().map(LoadingDemandSubmissionVO::getId).collect(Collectors.toList());
        // 汇总当月当天前仓库收发货数据
        List<String> productCodeList = loadingDemandSubmissionVOS.stream().map(LoadingDemandSubmissionVO::getProductCode)
                .distinct().collect(Collectors.toList());
        Map<String, Object> productParams = new HashMap<>();
        productParams.put("productCodeList", productCodeList);
        Map<String, String> productVehicleModelMap = newMdsFeign.selectProductVehicleModel(SystemHolder.getScenario(), productParams);
        List<String> vehicleModeCodes = Lists.newArrayList(productVehicleModelMap.values());
        String currentMonthFirstDay = DateUtils.dateToString(DateUtils.getCurrentMonthFirstDay(), DateUtils.COMMON_DATE_STR3);
        Date currentDate = new Date();
        String currentMonth = DateUtils.dateToString(currentDate, DateUtils.YEAR_MONTH);
        String currentMonthCurrentDay = DateUtils.dateToString(currentDate, DateUtils.COMMON_DATE_STR3);
        try {    
        	//主机厂，车型，产品编码对应的发货数量(获取仓库发货数量)
	        CompletableFuture<Map<String, BigDecimal>> releaseMapFuture = CompletableFuture.supplyAsync(
	                () -> getWarehouseReleaseRecordDeliveryQtyMap(productCodeList,
	        				productVehicleModelMap, vehicleModeCodes, currentMonthFirstDay, currentMonthCurrentDay), threadPoolExecutor);
	        
	        //主机厂，车型，产品编码对应的发货数量(获取仓库发货至中转库的发货数量)
	        CompletableFuture<Map<String, BigDecimal>> releaseToMapFuture = CompletableFuture.supplyAsync(
	                () -> getWarehouseReleaseToWarehouseDeliveryQtyMap(productCodeList,
	    					productVehicleModelMap, vehicleModeCodes, currentMonthFirstDay, currentMonthCurrentDay), threadPoolExecutor);
	
			//异步查询装车需求提报数据，资源日历
	        CompletableFuture<LoadingDemandSubmissionFutureVO> loadingDemandSubmissionDetailFuture = CompletableFuture.supplyAsync(() -> {
	            	LoadingDemandSubmissionFutureVO result = new LoadingDemandSubmissionFutureVO();
	            	Map<String, Object> detailQueryMap = MapUtil.newHashMap();
	        		detailQueryMap.put("submissionIds", submissionIds);
	        		List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOS = loadingDemandSubmissionDetailService.selectByParams(detailQueryMap);
	        		//日装车需求提报数据
	                List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOSDay = loadingDemandSubmissionDetailVOS.stream()
	                        .filter(item -> StringUtils.equals(GranularityEnum.DAY.getCode(), item.getSubmissionType())).collect(Collectors.toList());
	                Map<String, List<LoadingDemandSubmissionDetailVO>> detailDayMap = CollectionUtils.isEmpty(loadingDemandSubmissionDetailVOSDay)
	                        ? MapUtil.newHashMap() : loadingDemandSubmissionDetailVOSDay.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getSubmissionId));
	                result.setDetailDayMap(detailDayMap);
	                
	                //获取未维护的资源日历（按月分组）
	                String format1 = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}";
	                loadingDemandSubmissionDetailVOSDay.forEach(item -> {
	                    if (StringUtils.isNotBlank(item.getDemandTime()) && item.getDemandTime().matches(format1)){
	                        item.setDemandTime(DateUtils.dateToString(DateUtils.stringToDate(item.getDemandTime(),
	                                DateUtils.COMMON_DATE_STR1), DateUtils.COMMON_DATE_STR3));
	                    }
	                });
	                Map<String, List<ResourceCalendarVO>> monthCalendarMap = getMonthCalendarMap(loadingDemandSubmissionDetailVOSDay);
	                result.setMonthCalendarMap(monthCalendarMap);
	                
	                //月装车需求提报数据
	                List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOSMonth = loadingDemandSubmissionDetailVOS.stream()
	                        .filter(item -> StringUtils.equals(GranularityEnum.MONTH.getCode(), item.getSubmissionType())).collect(Collectors.toList());
	                Map<String, List<LoadingDemandSubmissionDetailVO>> detailMonthMap = CollectionUtils.isEmpty(loadingDemandSubmissionDetailVOSMonth)
	                        ? MapUtil.newHashMap() : loadingDemandSubmissionDetailVOSMonth.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getSubmissionId));
	                result.setDetailMonthMap(detailMonthMap);
	        		return result;
	        }, threadPoolExecutor);
	
	        
	        // 第一层key获取一致性需求预测的data、第二层key获取一致性需求预测的dataDetail
	        CompletableFuture<Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>>> consistenceRealtionMapFuture = CompletableFuture.supplyAsync(() -> {
	        	// 根据计划周期取已发布的最新版一致性业务预测版本
	            String forecastVersionId = consistenceDemandForecastVersionDao.selectLatestPublishedVersionId(null);
	            // 根据版本ID和预测类型和年月获取数据
	            Map<String, Object> consistenceVersionParams = new HashMap<>();
	            consistenceVersionParams.put("versionId", forecastVersionId);
	            List<ConsistenceDemandForecastDataPO> consistenceDataList = consistenceDemandForecastDataDao.selectByParams(consistenceVersionParams);
	        	Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>> consistenceRealtionMap = new HashMap<>();
	            if (CollectionUtils.isNotEmpty(consistenceDataList)) {
	                List<String> demandForecastIds = consistenceDataList.stream().map(ConsistenceDemandForecastDataPO::getId)
	                        .collect(Collectors.toList());
	                List<ConsistenceDemandForecastDataDetailPO> consistenceDetailList = consistenceDemandForecastDataDetailDao
	                        .selectByConsistenceDemandForecastDataIds(demandForecastIds).stream()
	                        .filter(item -> item.getForecastTime() != null).collect(Collectors.toList());
	                Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>> consistenceDetailMap;
	                if (CollectionUtils.isNotEmpty(consistenceDetailList)) {
	                    consistenceDetailMap = consistenceDetailList.stream().collect(Collectors
	                            .groupingBy(ConsistenceDemandForecastDataDetailPO::getConsistenceDemandForecastDataId, Collectors
	                                    .toMap(x -> DateUtils.dateToString(x.getForecastTime(), DateUtils.YEAR_MONTH),
	                                            Function.identity(),
	                                            (k1, k2) -> k1)));
	
	                    // 获取主机厂、车型、产品编码和一致性业务预测数据的关联关系
	                    Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>> finalConsistenceDetailMap = consistenceDetailMap;
	                    consistenceRealtionMap = consistenceDataList.stream().collect(Collectors
	                            .toMap(x -> String.join("&", x.getOemCode(), x.getVehicleModelCode(), x.getProductCode()),
	                                    y -> finalConsistenceDetailMap.get(y.getId()),
	                                    (k1, k2) -> k1));
	                }
	            }
	            return consistenceRealtionMap;
	        }, threadPoolExecutor);
	        Map<String, List<OemProductLineMapVO>> oemProductLineMapVOMap = new HashMap<>();
	        // 查询主机厂厂线信息
	        List<OemProductLineMapVO> oemProductLineMapVOS = oemProductLineMapService.selectByOemCodeList(oemCodeList);
	        CompletableFuture<Map<String, List<ResourceCalendarVO>>> resourceCodeCalendarMapFuture = null;
	        if (CollectionUtils.isNotEmpty(oemProductLineMapVOS)) {
	            oemProductLineMapVOMap = oemProductLineMapVOS.stream().collect(Collectors
	                    .groupingBy(item -> String.join("&", item.getOemCode(), item.getVehicleModelCode())));
	            List<String> lineCodeList = oemProductLineMapVOS.stream().map(OemProductLineMapVO::getLineCode)
	                    .distinct().collect(Collectors.toList());
	            resourceCodeCalendarMapFuture = CompletableFuture.supplyAsync(() -> {
	            	// 查询装车日历
	            	Map<String, List<ResourceCalendarVO>> resourceCodeCalendarMap = new HashMap<>();
	                List<ResourceCalendarVO> resourceCalendarVOS =
	                        dfpResourceCalendarService.selectResourceByStandardResourceIds(lineCodeList);
	                if (CollectionUtils.isNotEmpty(resourceCalendarVOS)) {
	                	resourceCodeCalendarMap = resourceCalendarVOS.stream().collect(Collectors
	                            .groupingBy(item -> String.join("&", item.getStandardResourceId(), item.getPhysicalResourceId())));
	                }
	                return resourceCodeCalendarMap;
	            }, threadPoolExecutor);
	        }
	        //获取异步查询的装车需求提报数据，资源日历
	        LoadingDemandSubmissionFutureVO loadingDemandSubmissionFutureVO = loadingDemandSubmissionDetailFuture.join();
	        Map<String, List<ResourceCalendarVO>> monthCalendarMap = loadingDemandSubmissionFutureVO.getMonthCalendarMap();
	        Map<String, List<LoadingDemandSubmissionDetailVO>> detailDayMap = loadingDemandSubmissionFutureVO.getDetailDayMap();
	        Map<String, List<LoadingDemandSubmissionDetailVO>> detailMonthMap = loadingDemandSubmissionFutureVO.getDetailMonthMap();
            // 获取主机厂、车型、产品编码和一致性业务预测数据的关联关系
            Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>> consistenceRealtionMap = consistenceRealtionMapFuture.get();
            // 主机厂，车型，产品编码对应的发货数量(获取仓库发货数量)
            Map<String, BigDecimal> releaseMap = releaseMapFuture.join();
            Map<String, BigDecimal> releaseToMap = releaseToMapFuture.get();
            // 获取装车日历
            Map<String, List<ResourceCalendarVO>> resourceCodeCalendarMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(oemProductLineMapVOS)) {
                resourceCodeCalendarMap = resourceCodeCalendarMapFuture.get();
            }
            // 重新计算后需要新增的数据
	        List<CleanDemandDataDTO> cleanDemandDataDTOS = Collections.synchronizedList(new LinkedList<>());
	        List<CleanDemandDataDetailDTO> cleanDemandDataDetailDTOS = Collections.synchronizedList(new LinkedList<>());
	        for (List<LoadingDemandSubmissionVO> list : loadingDemandSubmissionDemandMap.values()){
                processDataByDemandCategory(demandVersionPO.getId(), list, productVehicleModelMap,
                        oemProductLineMapVOMap, resourceCodeCalendarMap, detailDayMap, detailMonthMap,
                        consistenceRealtionMap, currentMonth, releaseMap, releaseToMap, monthCalendarMap,
                        cleanDemandDataDTOS, cleanDemandDataDetailDTOS, oemBusinessTypeMap);
	        }
            // 插入数据校验
            analysisDiffPartNameInEachProduct(analysisResults, cleanDemandDataDTOS);

        	List<CompletableFuture<Integer>> completableFutureList = new ArrayList<>();
        	if (CollectionUtils.isNotEmpty(cleanDemandDataDTOS)) {
    			Lists.partition(cleanDemandDataDTOS, 1000).forEach(subList -> {
    				CompletableFuture<Integer> insertDataFuture = CompletableFuture.supplyAsync(() -> {
    					this.doCreateBatch(subList);
    					return 1;
    				}, threadPoolExecutor);
    				completableFutureList.add(insertDataFuture);
    			});
    		}
        	if (CollectionUtils.isNotEmpty(cleanDemandDataDetailDTOS)) {
    			Lists.partition(cleanDemandDataDetailDTOS, 2000).forEach(subList -> {
    				CompletableFuture<Integer> insertDetailFuture = CompletableFuture.supplyAsync(() -> {
    					cleanDemandDataDetailService.doCreateBatch(subList);
    					return 1;
    				}, threadPoolExecutor);
    				completableFutureList.add(insertDetailFuture);
    			});
    		}
        	// 等待插入结束
        	completableFutureList.forEach( e -> {
        		e.join();
        	});
        	// 异步执行删除
//        	CompletableFuture.runAsync(() -> {
        		if (CollectionUtils.isNotEmpty(deletedDataIds)) {
        			//删除原有数据
                    cleanDemandDataService.doDelete(deletedDataIds);
                    cleanDemandDataDetailDao.deleteByDataIds(deletedDataIds);
        		}
//        	});
		} catch (Exception e) {
			 log.error("零件需求汇总-日需求计算失败：", e);
			 throw new BusinessException("零件需求汇总-日需求计算失败");
		} finally {
			CustomThreadPoolFactory.closeOrShutdown(threadPoolExecutor);
		}
        return analysisResults;
    }

    public static final String NOTIFICATION_TEMPLATE = "主机厂编码%s、产品编码%s存在不同零件号的数据";
    private static void analysisDiffPartNameInEachProduct(List<String> resultList,
                                                          List<CleanDemandDataDTO> cleanDemandDataList) {
        Map<String, Set<String>> dataGroup =
                cleanDemandDataList.stream().collect(Collectors.groupingBy(item ->
                                item.getOemCode() + Constants.DELIMITER + item.getProductCode(),
                        Collectors.mapping(CleanDemandDataDTO::getPartName, Collectors.toSet())));
        for (Map.Entry<String, Set<String>> entry : dataGroup.entrySet()) {
            String key = entry.getKey();
            String[] keys = key.split(Constants.DELIMITER);
            Set<String> value = entry.getValue();
            if (CollectionUtils.isNotEmpty(value) && value.size() > 1) {
                resultList.add(String.format(NOTIFICATION_TEMPLATE, keys[0], keys[1]));
            }
        }
    }

	private Map<String, List<ResourceCalendarVO>> getMonthCalendarMap(
			List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOSDay) {
		List<String> monthList = loadingDemandSubmissionDetailVOSDay.stream()
                .map(item -> DateUtils.dateToString(DateUtils.stringToDate(item.getDemandTime(), "yyyy-MM-dd"), "yyyy-MM"))
                .distinct().collect(Collectors.toList());
        Collections.sort(monthList);
        Date firstMonthDate = DateUtils.stringToDate(monthList.get(0),DateUtils.YEAR_MONTH);
        Date lastMonthDate = DateUtils.stringToDate(monthList.get(monthList.size() - 1),DateUtils.YEAR_MONTH);
        Date firstDay = DateUtils.getMonthFirstDay(firstMonthDate);
        Date lastDay = DateUtils.getMonthLastDay(lastMonthDate);
        List<Date> dateList = DateUtils.getIntervalDates(firstDay,lastDay);
        List<ResourceCalendarVO> resourceCalendarVOS = new ArrayList<>();
        for (Date d : dateList){
            if (isWeekend(d)){
                continue;
            }
            ResourceCalendarVO resourceCalendarVO = new ResourceCalendarVO();
            resourceCalendarVO.setWorkDay(d);
            resourceCalendarVOS.add(resourceCalendarVO);
        }
        //按照月份分组
        Map<String, List<ResourceCalendarVO>> monthCalendarMap = resourceCalendarVOS.stream()
        		.collect(Collectors.groupingBy(e -> DateUtils.dateToString(e.getWorkDay(), DateUtils.YEAR_MONTH)));
		return monthCalendarMap;
	}
    
    protected boolean isWeekend(Date date){
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        return dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;

    }

    /**
     * 主机厂，车型，产品编码对应的发货数量(获取仓库发货数量)
     * @param productCodeList
     * @param productVehicleModelMap
     * @param vehicleModeCodes
     * @param currentMonthFirstDay
     * @param currentMonthCurrentDay
     * @return
     */
	private Map<String, BigDecimal> getWarehouseReleaseRecordDeliveryQtyMap(List<String> productCodeList,
			Map<String, String> productVehicleModelMap, List<String> vehicleModeCodes, String currentMonthFirstDay,
			String currentMonthCurrentDay) {
		Map<String, BigDecimal> releaseMap = new HashMap<>();
        if (!currentMonthCurrentDay.equals(currentMonthFirstDay)) {
            Map<String, Object> releaseParams = new HashMap<>();
            releaseParams.put("beginDate", currentMonthFirstDay);
            releaseParams.put("endDate", currentMonthCurrentDay);
            releaseParams.put("productCodes", productCodeList);
            List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecords = warehouseReleaseRecordService.selectMonthVOByParams(releaseParams);
            warehouseReleaseRecords.forEach(item -> {
                String itemCode = item.getItemCode();
                String vehicleModelCode = productVehicleModelMap.getOrDefault(itemCode, itemCode);
                item.setVehicleModelCode(vehicleModelCode);
            });
            releaseMap = warehouseReleaseRecords.stream().collect(Collectors
                    .toMap(item -> String.join("&", item.getOemCode(), item.getVehicleModelCode(), item.getItemCode(), item.getYearMonth()),
                            WarehouseReleaseRecordMonthVO::getSumQty, (k1, k2) -> k1));
        }
		return releaseMap;
	}
	
	/**
     * 主机厂，车型，产品编码对应的发货数量(获取仓库发货至中转库的发货数量)
     * @param productCodeList
     * @param productVehicleModelMap
     * @param vehicleModeCodes
     * @param currentMonthFirstDay
     * @param currentMonthCurrentDay
     * @return
     */
	private Map<String, BigDecimal> getWarehouseReleaseToWarehouseDeliveryQtyMap(List<String> productCodeList,
			Map<String, String> productVehicleModelMap, List<String> vehicleModeCodes, String currentMonthFirstDay,
			String currentMonthCurrentDay) {
		Map<String, BigDecimal> releaseMap = new HashMap<>();
        if (!currentMonthCurrentDay.equals(currentMonthFirstDay)) {
            Map<String, Object> releaseParams = new HashMap<>();
            releaseParams.put("beginDate", currentMonthFirstDay);
            releaseParams.put("endDate", currentMonthCurrentDay);
            releaseParams.put("productCodes", productCodeList);
            List<WarehouseReleaseToWarehouseMonthVO> warehouseReleaseToWarehouses = warehouseReleaseToWarehouseService.selectMonthVOByParams(releaseParams);
            warehouseReleaseToWarehouses.forEach(item -> {
                String itemCode = item.getItemCode();
                String vehicleModelCode = productVehicleModelMap.getOrDefault(itemCode, itemCode);
                item.setVehicleModelCode(vehicleModelCode);
            });
            releaseMap = warehouseReleaseToWarehouses.stream().collect(Collectors
                    .toMap(item -> String.join("&", item.getOemCode(), item.getVehicleModelCode(), item.getItemCode(), item.getYearMonth()),
                    		WarehouseReleaseToWarehouseMonthVO::getSumQty, (k1, k2) -> k1));
        }
		return releaseMap;
	}

    public void processDataByDemandCategory(String versionId,
                                            List<LoadingDemandSubmissionVO> loadingDemandSubmissionVOS,
                                            Map<String, String> productVehicleModelMap,
                                            Map<String, List<OemProductLineMapVO>> oemProductLineMapVOMap,
                                            Map<String, List<ResourceCalendarVO>> resourceCodeCalendarMap,
                                            Map<String, List<LoadingDemandSubmissionDetailVO>> detailDayMap,
                                            Map<String, List<LoadingDemandSubmissionDetailVO>> detailMonthMap,
                                            Map<String, Map<String, ConsistenceDemandForecastDataDetailPO>> consistenceRealtionMap,
                                            String currentMonth,Map<String, BigDecimal> releaseMap, Map<String, BigDecimal> releaseToMap, 
                                            Map<String, List<ResourceCalendarVO>> monthCalendarMap,
                                            List<CleanDemandDataDTO> cleanDemandDataDTOS, List<CleanDemandDataDetailDTO> cleanDemandDataDetailDTOS,
                                            Map<String, String> oemBusinessTypeMap){
        List<String> addedLoadDemandDetailIdList = new ArrayList<>();
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern(DateUtils.COMMON_DATE_STR1);
        //todo 前置做了demandTime的数据处理，此处应该不需要再单独做判断
        String format1 = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}";
        String format2 = "\\d{4}-\\d{2}-\\d{2}";
        Random random = new Random();
        loadingDemandSubmissionVOS.parallelStream().forEach( loadingDemandSubmissionVO -> {
            String oemCode = loadingDemandSubmissionVO.getOemCode();
            String productCode = loadingDemandSubmissionVO.getProductCode();
            String demandCategory = loadingDemandSubmissionVO.getDemandCategory();
            String vehicleModelCode = productVehicleModelMap.get(productCode);
            // 获取对应的厂线数据 主机厂 + 车型
            List<OemProductLineMapVO> oemProductLineMapVOS = oemProductLineMapVOMap.get(String.join("&", oemCode,
                    vehicleModelCode));

            //获取主机厂装车日历数据
            //需要根据主机厂 + 厂线去获取装车日历数据
            // -->变成产线 + 车型获取装车日历数据
            Map<String, List<ResourceCalendarVO>> oemResourceCalendarsMapOfMonth = new HashMap<>();
            if (CollectionUtils.isNotEmpty(oemProductLineMapVOS)) {
                List<ResourceCalendarVO> resourceCalendarVOS = new ArrayList<>();
                oemProductLineMapVOS.forEach(oemProductLineMapVO -> {
                    List<ResourceCalendarVO> list = resourceCodeCalendarMap.get(String
                            .join("&", oemProductLineMapVO.getLineCode(),vehicleModelCode));
                    if (CollectionUtils.isNotEmpty(list)){
                        //一天当中有多个日历,进行去重,只留一天
                        Map<String,ResourceCalendarVO> map =
                                list.stream().collect(Collectors.toMap(
                                        item -> DateUtils.dateToString(item.getWorkDay()),
                                item -> item,(existing, replacement) -> existing));
                        resourceCalendarVOS.addAll(new ArrayList<>(map.values()));
                    }
                });
                if (CollectionUtils.isNotEmpty(resourceCalendarVOS)) {
                    oemResourceCalendarsMapOfMonth = resourceCalendarVOS.stream().collect(Collectors
                            .groupingBy(item -> DateUtils.dateToString(item.getWorkDay(), "yyyy-MM")));
                }
            }
            String submissionId = UUIDUtil.getUUID();
            CleanDemandDataDTO cleanDemandDataDTO = new CleanDemandDataDTO();
            cleanDemandDataDTO.setId(submissionId);
            cleanDemandDataDTO.setVersionId(versionId);
            cleanDemandDataDTO.setDemandCategory(loadingDemandSubmissionVO.getDemandCategory());
            cleanDemandDataDTO.setOemCode(oemCode);
            cleanDemandDataDTO.setVehicleModelCode(vehicleModelCode);
            cleanDemandDataDTO.setProductCode(productCode);
            cleanDemandDataDTO.setPartName(loadingDemandSubmissionVO.getPartNumber());
            cleanDemandDataDTO.setDemandType(DemandTypeEnum.LOADING_DEMAND.getCode());
            cleanDemandDataDTO.setVersionStatus(PublishStatusEnum.UNPUBLISH.getCode());
            cleanDemandDataDTOS.add(cleanDemandDataDTO);
            List<LoadingDemandSubmissionDetailVO> submissionDetailVOS = detailDayMap.get(loadingDemandSubmissionVO.getId());
            if (CollectionUtils.isNotEmpty(submissionDetailVOS)) {
            	//获取一致性预测需求数据
                Map<String, ConsistenceDemandForecastDataDetailPO> consistenceDemandForecastDataDetailPOMap = consistenceRealtionMap
                        .get(String.join("&", cleanDemandDataDTO.getOemCode(),
                                cleanDemandDataDTO.getVehicleModelCode(), cleanDemandDataDTO.getProductCode()));

                //获取月份数据
                List<LoadingDemandSubmissionDetailVO> monthSubmissionDetailVOList = detailMonthMap.get(loadingDemandSubmissionVO.getId());
                Map<String, List<LoadingDemandSubmissionDetailVO>> monthDetailMapOfMonth = new HashMap<>();
                if (CollectionUtils.isNotEmpty(monthSubmissionDetailVOList)) {
                    monthDetailMapOfMonth = monthSubmissionDetailVOList.stream().collect(Collectors
                            .groupingBy(LoadingDemandSubmissionDetailVO::getDemandTime));
                }
                //根据月份数据去重
                List<String> monthList = submissionDetailVOS.stream()
                        .map(item -> DateUtils.dateToString(DateUtils.stringToDate(item.getDemandTime(), "yyyy-MM-dd"), "yyyy-MM"))
                        .distinct().collect(Collectors.toList());

                Map<String, List<LoadingDemandSubmissionDetailVO>> dayDetailMapOfMonth = submissionDetailVOS.stream().collect(Collectors
                        .groupingBy(item -> DateUtils
                                .dateToString(DateUtils.stringToDate(item.getDemandTime(), "yyyy-MM-dd"), "yyyy-MM")));
                //by需求时间分组，如果时间不为空，不进行均分，则用此map数据创建日需求
                Map<String, List<LoadingDemandSubmissionDetailVO>> detailVOMapOfDemandTime =
                        submissionDetailVOS.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getDemandTime));
                for (String month : monthList) {
                    // 获取月份的天数
                    List<LocalDate> allDaysInMonth = getAllDaysInMonth(month);
                    List<Date> dayDateList = new ArrayList<>();

                    BigDecimal daySummaryQuantity = BigDecimal.ZERO;
                    List<LoadingDemandSubmissionDetailVO> detailVOList = dayDetailMapOfMonth.get(month);
                    List<LoadingDemandSubmissionDetailVO> avgQuantityDayDetailList = new ArrayList<>();
                    if (CollectionUtils.isEmpty(detailVOList)) {
                        continue;
                    }
                    List<String> demandTimeList =
                            detailVOList.stream().map(LoadingDemandSubmissionDetailVO::getDemandTime).distinct().collect(Collectors.toList());
                    // 获取日期最早的数据
                    LoadingDemandSubmissionDetailVO earliestDetailVO = detailVOList.stream().min(Comparator.comparing(LoadingDemandSubmissionDetailVO::getDemandTime)).get();
                    LocalDate earliestLocalDate = null;
                    if (earliestDetailVO.getDemandTime().matches(format1)){
                        earliestLocalDate = LocalDate.parse(earliestDetailVO.getDemandTime(), formatter2);
                    }else if (earliestDetailVO.getDemandTime().matches(format2)){
                        earliestLocalDate = LocalDate.parse(earliestDetailVO.getDemandTime(), formatter);
                    }else {
                        throw new BusinessException("日期格式和预期不匹配:"+earliestDetailVO.getDemandTime());
                    }
                    List<ResourceCalendarVO> resourceCalendarVOList = oemResourceCalendarsMapOfMonth.get(month);
                    if (CollectionUtils.isEmpty(resourceCalendarVOList)) {
                    	resourceCalendarVOList = monthCalendarMap.get(month);
//                            resourceCalendarVOList = dfpResourceCalendarService.getMonthFillResourceCalendar(month);
                    }
                    //过滤resourceCalendarVOList在earliestLocalDate等于或者之后的日期
                    resourceCalendarVOList = resourceCalendarVOList.stream().filter(item -> item.getWorkDay().after(DateUtils.stringToDate(earliestDetailVO.getDemandTime(), DateUtils.COMMON_DATE_STR3))
                    || item.getWorkDay().equals(DateUtils.stringToDate(earliestDetailVO.getDemandTime(), DateUtils.COMMON_DATE_STR3))).collect(Collectors.toList());
                    //装车日历时间范围
                    Map<Date, List<ResourceCalendarVO>> resourceCalendarVOMap =
                            resourceCalendarVOList.stream().collect(Collectors.groupingBy(ResourceCalendarVO::getWorkDay));
                    List<String> oemResourceCalendars = resourceCalendarVOList.stream().map(item -> DateUtils
                            .dateToString(item.getWorkDay())).distinct().collect(Collectors.toList());

                    // 日需求值为null的数量
                    // int nullCount = 0;
                    //Date earliest = null,lastest = null;
                    //取月份剩余天数
                    int valueCount = 0;
                    for (LoadingDemandSubmissionDetailVO loadingDemandSubmissionDetailVO : detailVOList) {
                        Date demandDate = DateUtils.stringToDate(loadingDemandSubmissionDetailVO.getDemandTime(),
                                DateUtils.COMMON_DATE_STR3);
                        //计算需要平摊的日需求数据，需求数量为null并且存在装车日历中(如果装车日历不为空)
                        if (null == loadingDemandSubmissionDetailVO.getDemandQuantity()
                                && (CollectionUtils.isEmpty(oemResourceCalendars)
                                || oemResourceCalendars.contains(loadingDemandSubmissionDetailVO.getDemandTime()))) {
                            avgQuantityDayDetailList.add(loadingDemandSubmissionDetailVO);
                            dayDateList.add(demandDate);
                            if (resourceCalendarVOMap.get(demandDate)!=null){
                                valueCount += resourceCalendarVOMap.get(demandDate).size();
                            }
                        } else {
                            if (null != loadingDemandSubmissionDetailVO.getDemandQuantity()) {
                                daySummaryQuantity = daySummaryQuantity.add(loadingDemandSubmissionDetailVO.getDemandQuantity());
//                                    nullCount--;
                                //valueCount--;
                            }
                        }
                    }
                    // 需要填充本月没有的预测数据
                    for (LocalDate localDate : allDaysInMonth) {
                        // 日期需要在装车需求提报中不存在的并且是往后的数据
                        String dateStr = localDate.format(formatter);
                        if (!demandTimeList.contains(dateStr) && earliestLocalDate.isBefore(localDate)){
                            if (oemResourceCalendars.contains(dateStr)){
                                Date d = DateUtils.stringToDate(dateStr, DateUtils.COMMON_DATE_STR3);
                                dayDateList.add(d);
                                if (resourceCalendarVOMap.get(d)!=null){
                                    valueCount += resourceCalendarVOMap.get(d).size();
                                }
                            }else {
                                CleanDemandDataDetailDTO cleanDemandDataDetailDTO = new CleanDemandDataDetailDTO();
                                cleanDemandDataDetailDTO.setId(UUIDUtil.getUUID() + "-" + (100000 + random.nextInt(900000)));
                                cleanDemandDataDetailDTO.setCleanDemandDataId(submissionId);
                                cleanDemandDataDetailDTO.setDemandTime(DateUtils.stringToDate(dateStr,
                                        DateUtils.COMMON_DATE_STR3));
                                cleanDemandDataDetailDTOS.add(cleanDemandDataDetailDTO);
                            }

                        }
                    }

                    if (CollectionUtils.isNotEmpty(dayDateList)) {
                        //先获取一致性预测需求的数据，如果为空则取装车提报的数据
                        BigDecimal monthQuantity = BigDecimal.ZERO;
                        if (null != consistenceDemandForecastDataDetailPOMap
                                && consistenceDemandForecastDataDetailPOMap.containsKey(month)) {
                            ConsistenceDemandForecastDataDetailPO consistenceDemandForecastDataDetailPO = consistenceDemandForecastDataDetailPOMap.get(month);
                            monthQuantity = consistenceDemandForecastDataDetailPO.getForecastQuantity() != null
                                    ? consistenceDemandForecastDataDetailPO.getForecastQuantity() : BigDecimal.ZERO;
                        } else {
                            List<LoadingDemandSubmissionDetailVO> monthLoadingDemandSubmissionDetailVO =
                                    monthDetailMapOfMonth.get(month);
                            if (CollectionUtils.isNotEmpty(monthLoadingDemandSubmissionDetailVO)) {
                                for (LoadingDemandSubmissionDetailVO vo : monthLoadingDemandSubmissionDetailVO){
                                    monthQuantity = vo.getDemandQuantity() != null
                                            ? vo.getDemandQuantity() : BigDecimal.ZERO;
                                }
                            }
                        }

                        //向上取整
                        if (null == monthQuantity || monthQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                            for (LoadingDemandSubmissionDetailVO demandSubmissionDetailVO : avgQuantityDayDetailList) {
                                CleanDemandDataDetailDTO cleanDemandDataDetailDTO = new CleanDemandDataDetailDTO();
                                cleanDemandDataDetailDTO.setId(UUIDUtil.getUUID() + "-" + (100000 + random.nextInt(900000)));
                                cleanDemandDataDetailDTO.setCleanDemandDataId(submissionId);
                                if (StringUtils.isNotBlank(demandSubmissionDetailVO.getDemandTime())) {
                                    cleanDemandDataDetailDTO.setDemandTime(DateUtils.stringToDate(demandSubmissionDetailVO.getDemandTime(), DateUtils.COMMON_DATE_STR3));
                                }
                                cleanDemandDataDetailDTO.setDemandQuantity(0);
                                cleanDemandDataDetailDTOS.add(cleanDemandDataDetailDTO);
                                addedLoadDemandDetailIdList.add(demandSubmissionDetailVO.getId());
                            }
                        } else {
                            BigDecimal subtract = monthQuantity.subtract(daySummaryQuantity);
                            if (month.equals(currentMonth)) {
                                String uniqueKey = String.join("&", oemCode, vehicleModelCode, productCode, month);
                                //扣减仓库收发货/中转库发货数量
                                String businessType = oemBusinessTypeMap.get(oemCode);
                                if(OemBusinessTypeEnum.MTS.getCode().equals(businessType)
                            			&& ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode().equals(demandCategory)) {
                                    subtract = subtract.subtract(releaseToMap.getOrDefault(uniqueKey, BigDecimal.ZERO));
                                }else {
                                	subtract = subtract.subtract(releaseMap.getOrDefault(uniqueKey, BigDecimal.ZERO));
                                }
                            }
                            List<Integer> shardNumList = new ArrayList<>();
                            if (subtract.compareTo(BigDecimal.ZERO) > 0) {
//                                    log.info("主机厂:{}, 本厂编码;{}, 月份:{}, 预测数量:{}, 平摊天数:{}", oemCode, productCode, month, subtract, valueCount);
                                if (valueCount > 0){
                                    shardNumList = distributeEvenly(subtract.intValue(), valueCount);
                                }

                            }

                            // 循环日期
                            // 排序dayDateList
                            dayDateList.sort(Comparator.comparing(Date::getTime));

                            int index = 0;
                            for (int i = 0; i < dayDateList.size(); i++) {
                                // 新增
                                Date date = dayDateList.get(i);
                                List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOS =
                                        detailVOMapOfDemandTime.get(DateUtils.dateToString(dayDateList.get(i), DateUtils.COMMON_DATE_STR3));
                                CleanDemandDataDetailDTO cleanDemandDataDetailDTO = new CleanDemandDataDetailDTO();
                                cleanDemandDataDetailDTO.setId(UUIDUtil.getUUID() + "-" + (100000 + random.nextInt(900000)));
                                cleanDemandDataDetailDTO.setDemandTime(DateUtils.stringToDate(DateUtils.dateToString(dayDateList.get(i), DateUtils.COMMON_DATE_STR3), DateUtils.COMMON_DATE_STR3));
                                cleanDemandDataDetailDTO.setCleanDemandDataId(submissionId);
                                Integer demandQuantity = 0;
                                if (shardNumList.size() - 1 >= index) {
                                    demandQuantity = shardNumList.get(index);
                                }
                                if (resourceCalendarVOMap.get(date)!=null){
                                    demandQuantity = demandQuantity * resourceCalendarVOMap.get(date).size();
                                    index += resourceCalendarVOMap.get(date).size();
                                }else {
                                    index++;
                                }
                                if (CollectionUtils.isEmpty(loadingDemandSubmissionDetailVOS) && demandQuantity < 0){
                                    continue;
                                }
                                cleanDemandDataDetailDTO.setDemandQuantity(demandQuantity);
                                cleanDemandDataDetailDTOS.add(cleanDemandDataDetailDTO);
                                if (CollectionUtils.isNotEmpty(loadingDemandSubmissionDetailVOS)){
                                    for (LoadingDemandSubmissionDetailVO loadingDemandSubmissionDetailVO : loadingDemandSubmissionDetailVOS) {
                                        addedLoadDemandDetailIdList.add(loadingDemandSubmissionDetailVO.getId());
                                    }
                                    //addedLoadDemandDetailIdList.add(loadingDemandSubmissionDetailVO.getId());
                                }
                            }
                        }
                    }
                }

                for (Map.Entry<String, List<LoadingDemandSubmissionDetailVO>> entry : detailVOMapOfDemandTime.entrySet()) {
                    List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOList = entry.getValue();
                    //避免重复添加
                    boolean exist = false;
                    for (LoadingDemandSubmissionDetailVO loadingDemandSubmissionDetailVO : loadingDemandSubmissionDetailVOList){
                        if (addedLoadDemandDetailIdList.contains(loadingDemandSubmissionDetailVO.getId())) {
                            exist = true;
                            break;
                        }
                    }
                    if (exist){
                        continue;
                    }
                    CleanDemandDataDetailDTO cleanDemandDataDetailDTO = new CleanDemandDataDetailDTO();
                    cleanDemandDataDetailDTO.setId(UUIDUtil.getUUID() + "-" + (100000 + random.nextInt(900000)));
                    cleanDemandDataDetailDTO.setCleanDemandDataId(submissionId);
                    LoadingDemandSubmissionDetailVO loadingDemandSubmissionDetailVO = loadingDemandSubmissionDetailVOList.get(0);
                    if (StringUtils.isNotBlank(loadingDemandSubmissionDetailVO.getDemandTime())) {
                        cleanDemandDataDetailDTO.setDemandTime(DateUtils
                                .stringToDate(loadingDemandSubmissionDetailVO.getDemandTime(), DateUtils.COMMON_DATE_STR3));
                    }
                    int demandQuantity = 0;
                    for (LoadingDemandSubmissionDetailVO detailVO : loadingDemandSubmissionDetailVOList){
                        if (Objects.nonNull(detailVO.getDemandQuantity())) {
                            demandQuantity = demandQuantity + detailVO.getDemandQuantity().intValue();
                        }
                    }
                    cleanDemandDataDetailDTO.setDemandQuantity(demandQuantity);

                    cleanDemandDataDetailDTOS.add(cleanDemandDataDetailDTO);
                }
            }

        });
    }
    
    /**
     * 获取指定月份的所有天数
     *
     * @param yearMonthStr 指定的年份和月份，格式为"yyyy-MM"
     * @return 该月份的所有日期列表
     */
    public static List<LocalDate> getAllDaysInMonth(String yearMonthStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        YearMonth yearMonth = YearMonth.parse(yearMonthStr, formatter);
        int daysInMonth = yearMonth.lengthOfMonth();
        List<LocalDate> dates = new ArrayList<>();
        for (int i = 1; i <= daysInMonth; i++) {
            LocalDate date = yearMonth.atDay(i);
            dates.add(date);
        }
        return dates;
    }
    
    public static List<Integer> distributeEvenly(int total, int parts) {
        List<Integer> distribution = new ArrayList<>();
        int baseValue = total / parts;  // 每个部分的基础值
        int remainder = total % parts;  // 处理余数

        for (int i = 0; i < parts; i++) {
            if (i < remainder) {
                // 前面的部分每个加1，以处理余数
                distribution.add(baseValue + 1);
            } else {
                distribution.add(baseValue);
            }
        }

        return distribution;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return cleanDemandDataDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    @Override
    public void doDeleteByVersionId(String versionId) {
        cleanDemandDataDao.deleteByVersionId(versionId);
    }

    @Override
    public List<CleanDemandDataVO> selectByPrimaryKeys(List<String> ids) {
            List<CleanDemandDataPO> cleanDemandDataPOList = cleanDemandDataDao.selectByPrimaryKeys(ids);
        return CleanDemandDataConvertor.INSTANCE.po2Vos(cleanDemandDataPOList);
    }

    @Override
    public List<LabelValue<String>> selectOemDropdown(String versionId) {
        return cleanDemandDataDao.selectOemDropdown(versionId).stream()
                .map(x -> new LabelValue<>(x.getLabel() + "(" + x.getValue() + ")", x.getValue()))
                .sorted(Comparator.comparing(LabelValue::getLabel)).collect(Collectors.toList());
    }

    @Override
    public List<CleanDemandProductInventoryReportVO> selectProductInventoryReportListByParams(Map<String, Object> params) {
        return cleanDemandDataDao.selectProductInventoryReportListByParams(params);
    }

	@Override
	@SneakyThrows
	public List<CleanDemandProductInventoryReportVO> queryProductInventoryReportPage(Pagination pagination,
			String sortParam, String queryCriteriaParam) {
		DemandVersionVO demandVersionVO = demandVersionService
				.selectLastVersionByVersionTypeAndPlanPeriod(VersionTypeEnum.CLEAN_DEMAND.getCode(), null);
		String demandVersionId = demandVersionVO.getId();
		if(StringUtils.isEmpty(queryCriteriaParam)) {
			queryCriteriaParam = "version_id = '" + demandVersionId + "'";
		}else {
			queryCriteriaParam = queryCriteriaParam + " and version_id = '" + demandVersionId + "'";
		}
		PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
		List<CleanDemandProductInventoryReportVO> dataList = cleanDemandDataDao
				.queryProductInventoryReportPage(sortParam, queryCriteriaParam);
		if(CollectionUtils.isEmpty(dataList)) {
			return dataList;
		}
		List<String> productCodes = dataList.stream().map(CleanDemandProductInventoryReportVO::getProductCode)
				.distinct().collect(Collectors.toList());
		List<String> oemCodes = dataList.stream().map(CleanDemandProductInventoryReportVO::getOemCode)
				.distinct().collect(Collectors.toList());
		ThreadPoolExecutor threadPoolExecutor = CustomThreadPoolFactory.instance();
		//获取日需求（国内/出口）明细
		List<String> cleanDemandDataIds = dataList.stream()
				.filter(e -> OemTradeTypeEnum.IN.getCode().equals(e.getMarketType()))
				.map(CleanDemandProductInventoryReportVO::getId).collect(Collectors.toList());
		Map<String, List<CleanDemandDataDetailVO>> dataDetailMap = new HashMap<>();
		if(CollectionUtils.isNotEmpty(cleanDemandDataIds)) {
			List<CleanDemandDataDetailVO> dataDetails = cleanDemandDataDetailService.selectByParams(ImmutableMap.of(
	    			"enabled", YesOrNoEnum.YES.getCode(), 
	    			"cleanDemandDataIds" , cleanDemandDataIds));
			dataDetailMap = dataDetails.stream()
	                .collect(Collectors.groupingBy(CleanDemandDataDetailVO::getCleanDemandDataId,
	                    Collectors.collectingAndThen(Collectors.toList(), list -> list.stream()
	                        .sorted(Comparator.comparing(CleanDemandDataDetailVO::getDemandTime))
	                        .collect(Collectors.toList()))));
		}
		List<String> cleanDemandDataExitIds = dataList.stream()
				.filter(e -> OemTradeTypeEnum.OUT.getCode().equals(e.getMarketType()))
				.map(CleanDemandProductInventoryReportVO::getId).collect(Collectors.toList());
		Map<String, List<CleanDemandDataDetailExitVO>> dataExitDetailMap =  new HashMap<>();
		if(CollectionUtils.isNotEmpty(cleanDemandDataExitIds)) {
			List<CleanDemandDataDetailExitVO> dataExitDetails = cleanDemandDataDetailExitService.selectByParams(ImmutableMap.of(
	    			"enabled", YesOrNoEnum.YES.getCode(), 
	    			"cleanDemandDataExitIds" , cleanDemandDataExitIds));
			dataExitDetailMap = dataExitDetails.stream()
	                .collect(Collectors.groupingBy(CleanDemandDataDetailExitVO::getCleanDemandDataExitId,
	                    Collectors.collectingAndThen(Collectors.toList(), list -> list.stream()
	                        .sorted(Comparator.comparing(CleanDemandDataDetailExitVO::getDemandTime))
	                        .collect(Collectors.toList()))));
		}
		//获取日均需求值字段配置值
		String averageDaysStr = ipsFeign.getByCollectionCode("AVERAGE_DAILY_DEMAND").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置AVERAGE_DAILY_DEMAND"));
		BigDecimal averageDays = new BigDecimal(averageDaysStr);
		//1.查询物料信息，获取需要维护的计划员
		String scenario = SystemHolder.getScenario();
		BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = 
                ipsNewFeign.getScenarioBusinessRange(scenario, "SALE_ORGANIZATION",
                        "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
		FeignDynamicParam dynamicParams = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("product_code", "order_planner", "item_cost", "po_category"))
                .queryParam(ImmutableMap.of(
            			"enabled", YesOrNoEnum.YES.getCode(), "stockPointCode", rangeData, 
            			"productCodes" , productCodes)).build();
        Map<String, NewProductStockPointVO> productMap = newMdsFeign.selectProductListByParamOnDynamicColumns(scenario, dynamicParams)
        		.stream().filter(e -> !Objects.isNull(e))
        		.collect(Collectors.toMap(NewProductStockPointVO::getProductCode, e-> e,(v1, v2) -> v1));
        Map<String, String> cnNameMap = ipsNewFeign.userList().stream()
        		.collect(Collectors.toMap(User::getId,User::getCnName,(v1, v2) -> v1));
		
        //2.获取成品库（成品库库存）
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRangeSub = ipsNewFeign.getScenarioBusinessRange(scenario,
                "SUB_INVENTORY",
                "INTERNAL", null);
        String rangeDataSub = scenarioBusinessRangeSub.getData().getRangeData();
        List<InventoryBatchDetailVO> inventoryBatchDetailList = inventoryBatchDetailService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(),
    			"stockPointCode", rangeData,
    			"subinventory", rangeDataSub,
    			"productCodes" , productCodes));
        Map<String, BigDecimal> currentQuantityMap = new HashMap<>();
        for (InventoryBatchDetailVO inventoryBatchDetail : inventoryBatchDetailList) {
        	String productCode = inventoryBatchDetail.getProductCode();
        	BigDecimal currentQuantity = StringUtils.isEmpty(inventoryBatchDetail.getCurrentQuantity())
        			? BigDecimal.ZERO : new BigDecimal(inventoryBatchDetail.getCurrentQuantity());
        	currentQuantityMap.put(productCode,
        			currentQuantityMap.getOrDefault(productCode, BigDecimal.ZERO).add(currentQuantity));
        }
        
        //3.获取发货计划的中转库（期初库存），主机厂（主机厂期初库存）
        DeliveryPlanVersionVO deliveryPlanVersionVO = deliveryPlanVersionService
        		.selectLatestVersionByParams(new HashMap<>());
        List<InventoryShiftVO> inventoryShiftList = inventoryShiftService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(),
    			"versionId", deliveryPlanVersionVO.getId(),
    			"plannedDate", DateUtils.getDayFirstTime(new Date()),
    			"oemCodes" , oemCodes));
        Map<String, InventoryShiftVO> inventoryShiftTempMap = inventoryShiftList.stream()
                .collect(Collectors.toMap(e-> e.getOemCode() + "&" + e.getProductCode(), e-> e,(v1, v2) -> v1));
        Map<String, InventoryShiftVO> inventoryShiftMap = new HashMap<>();
        for (Entry<String, InventoryShiftVO> shiftentrySet : inventoryShiftTempMap.entrySet()) {
        	String[] splitKey = shiftentrySet.getKey().split("&");
        	if(splitKey.length == 3) {
        		inventoryShiftMap.put(splitKey[0] + "&" + splitKey[1], shiftentrySet.getValue());
        		inventoryShiftMap.put(splitKey[0] + "&" + splitKey[2], shiftentrySet.getValue());
        	}else {
        		inventoryShiftMap.put(shiftentrySet.getKey(), shiftentrySet.getValue());
        	}
		}
        
        //4.获取在产品
        Map<String, List<StandardStepVO>> standardStepMap = newMdsFeign.selectStandardStepAll(scenario)
        		.stream().collect(Collectors.groupingBy(StandardStepVO::getStockPointCode));
        List<InventoryBatchDetailVO> bcInventoryBatchDetails =
                inventoryBatchDetailService.selectByProductCodes(productCodes, StockPointTypeEnum.BC.getCode());
        Map<String, List<InventoryBatchDetailVO>> operationInventoryMap = bcInventoryBatchDetails.stream()
                .collect(Collectors.groupingBy(p -> String.join("&", p.getStockPointCode(), p.getProductCode(), 
                		p.getOperationCode())));
        Map<String, SubInventoryCargoLocationVO> cargoLocationMap = getCargoLocationMap(bcInventoryBatchDetails, scenario);
        
        //获取最新已发布的一致性需求预测数据
        ConsistenceDemandForecastVersionVO consistenceDemandForecastVersionVO = consistenceDemandForecastVersionService
        		.selectOneMaxVersionByParams(ImmutableMap.of("versionStatus" , PublishStatusEnum.PUBLISHED.getCode()));
        List<ConsistenceDemandForecastDataVO> forecastDataList = consistenceDemandForecastDataService.selectByParams(ImmutableMap.of(
          		 "versionId" , consistenceDemandForecastVersionVO.getId(), "enabled", YesOrNoEnum.YES.getCode(),
           		 "oemCodeList" , oemCodes, "productCodeList" , productCodes));
        Map<String, ConsistenceDemandForecastDataVO> forecastDataMap = forecastDataList.stream()
         		 .collect(Collectors.toMap(e-> String.join("&", e.getDemandCategory(), e.getOemCode(), e.getProductCode()),
         				e->e, (v1, v2) -> v1));

        //获取装车日历数据
        Map<String, List<OemProductLineMapVO>> oemProductLineMapVOMap = new HashMap<>();
        List<OemProductLineMapVO> oemProductLineMapVOS = oemProductLineMapService.selectByOemCodeList(oemCodes);
        CompletableFuture<Map<String, List<ResourceCalendarVO>>> resourceCodeCalendarMapFuture = null;
        if (CollectionUtils.isNotEmpty(oemProductLineMapVOS)) {
        	oemProductLineMapVOMap = oemProductLineMapVOS.stream().collect(Collectors
        			.groupingBy(item -> String.join("&", item.getOemCode(), item.getVehicleModelCode())));
        	List<String> lineCodeList = oemProductLineMapVOS.stream().map(OemProductLineMapVO::getLineCode)
        			.distinct().collect(Collectors.toList());
        	resourceCodeCalendarMapFuture = CompletableFuture.supplyAsync(() -> {
        		// 查询装车日历
        		Map<String, List<ResourceCalendarVO>> resourceCodeCalendarMap = new HashMap<>();
        		List<ResourceCalendarVO> resourceCalendarVOS =
        				dfpResourceCalendarService.selectResourceByStandardResourceIds(lineCodeList);
        		if (CollectionUtils.isNotEmpty(resourceCalendarVOS)) {
        			resourceCodeCalendarMap = resourceCalendarVOS.stream().collect(Collectors
        					.groupingBy(item -> String.join("&", item.getStandardResourceId(), item.getPhysicalResourceId())));
        		}
        		return resourceCodeCalendarMap;
        	}, threadPoolExecutor);
        }
        // 获取装车日历
        Map<String, List<ResourceCalendarVO>> resourceCodeCalendarMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(oemProductLineMapVOS)) {
        	resourceCodeCalendarMap = resourceCodeCalendarMapFuture.get();
        }

        Map<String, BigDecimal> forecastDataDetailMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(forecastDataList)) {
        	List<String> forecastDataIds = forecastDataList.stream().map(ConsistenceDemandForecastDataVO::getId).collect(Collectors.toList());
        	List<ConsistenceDemandForecastDataDetailVO> cdfdDetailList = consistenceDemandForecastDataDetailService.selectByParams(ImmutableMap.of(
           		 "consistenceDemandForecastDataIdList" , forecastDataIds,
           		 "enabled", YesOrNoEnum.YES.getCode()));
        	forecastDataDetailMap = cdfdDetailList.stream()
            		.filter(e-> e.getForecastQuantity() != null)
           		 	.collect(Collectors.toMap(e-> e.getConsistenceDemandForecastDataId() 
           		 			+ "&" + DateUtils.dateToString(e.getForecastTime(), DateUtils.YEAR_MONTH),
           		 			ConsistenceDemandForecastDataDetailVO::getForecastQuantity, (v1, v2) -> v1));
        }
        
        //获取当月待发数据
        SwitchRelationVO switchRelation =
                switchRelationBetweenProductService.getSwitchRelation(oemCodes, productCodes);
        Map<String, BigDecimal> deliveryPlanPublishedCurrentMonthMap = getDeliveryPlanPublishedCurrentMonthly(switchRelation,
        		oemCodes, productCodes);
        List<OemVO> oems = oemService.selectByParams(ImmutableMap.of(
          		 "enabled", YesOrNoEnum.YES.getCode(), "oemCodes", oemCodes));
        List<String> mtsOmeCodes = oems.stream().filter( e-> OemBusinessTypeEnum.MTS.getCode().equals(e.getBusinessType()))
        		.map(OemVO::getOemCode).distinct().collect(Collectors.toList());
        List<String> mtoOmeCodes = oems.stream().filter( e-> !OemBusinessTypeEnum.MTS.getCode().equals(e.getBusinessType()))
        		.map(OemVO::getOemCode).distinct().collect(Collectors.toList());
        //获取当月已发数据
        Map<String, String> newOldMap = switchRelation.getNewOldMap();
    	Map<String, String> oldNewMap = switchRelation.getOldNewMap();
        String beginDate = DateUtils.dateToString(DateUtils.getMonthFirstDay(new Date()));
        String endDate = DateUtils.dateToString(DateUtils.getMonthFirstDay(DateUtils.moveMonth(new Date(), 1)));
        Map<String, Object> warehouseQueryMap = Maps.newHashMap();
        warehouseQueryMap.put("beginDate", beginDate);
        warehouseQueryMap.put("endDate", endDate);
		warehouseQueryMap.put("productCodes", switchRelation.getAllProductCodes());
		//获仓库发货记录(MTO)
		Map<String, BigDecimal> warehouseReleaseRecordMap = new HashMap<>();
		if(CollectionUtils.isNotEmpty(mtoOmeCodes)) {
			warehouseQueryMap.put("oemCodes", mtoOmeCodes);
			List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecordMonthList = warehouseReleaseRecordDao
	                .selectMonthVOByParams(warehouseQueryMap);
			warehouseReleaseRecordMap =
	                getWarehouseReleaseRecordMonthly(warehouseReleaseRecordMonthList, newOldMap, oldNewMap);
		}
        //获取中转库发货记录(MTS)
		Map<String, BigDecimal> warehouseReleaseToRecordMap = new HashMap<>();
		if(CollectionUtils.isNotEmpty(mtsOmeCodes)) {
			warehouseQueryMap.put("oemCodes", mtsOmeCodes);
	        List<WarehouseReleaseToWarehouseMonthVO> warehouseReleaseToWarehouses = warehouseReleaseToWarehouseService
	        		.selectMonthVOByParams(warehouseQueryMap);
	        warehouseReleaseToRecordMap =
	                getWarehouseReleaseToRecordMonthly(warehouseReleaseToWarehouses, newOldMap, oldNewMap);
		}
        
		for (CleanDemandProductInventoryReportVO reportVO : dataList) {
			String demandCategory = reportVO.getDemandCategory();
			String oemCode = reportVO.getOemCode();
			String productCode = reportVO.getProductCode();
			String marketType = reportVO.getMarketType();
			String vehicleModelCode = reportVO.getVehicleModelCode();
			String oemProductKey = oemCode + "&" + productCode;
			
			//1.维护计划员
			NewProductStockPointVO productInfo = productMap.get(productCode);
			if(StringUtils.isNotEmpty(productInfo.getOrderPlanner())){
				reportVO.setOrderPlanner(cnNameMap.get(productInfo.getOrderPlanner()));
			}
			
			//2.维护成品库
			BigDecimal finishedInventory = currentQuantityMap.getOrDefault(productCode, BigDecimal.ZERO);
			reportVO.setFinishedInventory(finishedInventory);
			
			//3.维护中转库，主机厂
			Integer openingInventory = 0;
        	Integer oemOpeningInventory = 0;
			InventoryShiftVO inventoryShift = inventoryShiftMap.get(oemProductKey);
        	if(inventoryShift != null) {
        		openingInventory = inventoryShift.getOpeningInventory() == null 
            			? 0 : inventoryShift.getOpeningInventory();
            	oemOpeningInventory = inventoryShift.getOemOpeningInventory() == null 
            			? 0 : inventoryShift.getOemOpeningInventory();
        	}
        	reportVO.setOpeningInventory(openingInventory);
        	reportVO.setOemOpeningInventory(oemOpeningInventory);
        	
        	//4.维护在产品
        	BigDecimal inProductInventory = BigDecimal.ZERO;
        	String poCategory = productInfo.getPoCategory();
            if (StringUtils.isNotEmpty(poCategory) && poCategory.contains(".")) {
                String factoryCode = poCategory.split("\\.")[1];
                List<StandardStepVO> currStandardSteps = standardStepMap.getOrDefault(factoryCode, new ArrayList<>());
                for (StandardStepVO standardStepVO : currStandardSteps) {
                	String standardStepCode = standardStepVO.getStandardStepCode();
                	inProductInventory = inProductInventory.add(getInventory(standardStepCode, productCode, factoryCode,
                			operationInventoryMap, cargoLocationMap));
				}
            }
            reportVO.setInProductInventory(inProductInventory);
        	
        	//5.统计合计 = 成品库+中转库+主机厂+在产品
        	BigDecimal totalInventory = finishedInventory.add(inProductInventory)
        			.add(BigDecimal.valueOf(openingInventory)).add(BigDecimal.valueOf(oemOpeningInventory));
        	reportVO.setTotalInventory(totalInventory);	
        	
        	//6.库存天数(取当天起到日需求计算中最后一天日期的需求，用”合计”依次冲减，如果仍有库存，根据最后一天的日期，取最新已发布的一致性需求预测，
        	//下个月的业务预测，根据装车日历均摊，用剩余数量依次冲减，得到汇总的库存天数)
        	ConsistenceDemandForecastDataVO consistenceDemandForecastDataVO = forecastDataMap
        			.get(String.join("&", demandCategory, oemCode, productCode));
        	
        	//当月(已发+待发)和下月、下下月的业务预测值
        	reportVO.setNextMonthForecastQuantity(BigDecimal.ZERO);
        	reportVO.setAfterNextMonthForecastQuantity(BigDecimal.ZERO);
        	if(consistenceDemandForecastDataVO != null) {
        		//数据时间
        		reportVO.setDataTime(consistenceDemandForecastDataVO.getModifyTime());
        		//获取下月，下下月的预测数据
        		String forecastDataId = consistenceDemandForecastDataVO.getId();
        		BigDecimal nextMonthForecastQuantity = forecastDataDetailMap.getOrDefault(forecastDataId + "&"
        				+ DateUtils.dateToString(DateUtils.moveMonth(new Date(), 1), DateUtils.YEAR_MONTH), BigDecimal.ZERO);
        		reportVO.setNextMonthForecastQuantity(nextMonthForecastQuantity);
        		BigDecimal afterNextMonthForecastQuantity = forecastDataDetailMap.getOrDefault(forecastDataId + "&"
        				+ DateUtils.dateToString(DateUtils.moveMonth(new Date(), 2), DateUtils.YEAR_MONTH), BigDecimal.ZERO);
        		reportVO.setAfterNextMonthForecastQuantity(afterNextMonthForecastQuantity);
        	}
        	//维护当月已发，待发数据
    		BigDecimal deliveryedQty = BigDecimal.ZERO;
    		if(mtsOmeCodes.contains(oemCode)) {
    			deliveryedQty = warehouseReleaseToRecordMap.getOrDefault(oemProductKey, BigDecimal.ZERO);
    		}else {
    			deliveryedQty = warehouseReleaseRecordMap.getOrDefault(oemProductKey, BigDecimal.ZERO);
    		}
    		// 获取当月待发数量
            BigDecimal waitDeliveryedQty = deliveryPlanPublishedCurrentMonthMap.getOrDefault(
            		oemProductKey, BigDecimal.ZERO);
            reportVO.setCuurMonthDeliveryQty("已发：" + deliveryedQty.stripTrailingZeros().toPlainString() + ","
                    + " 待发：" + waitDeliveryedQty.stripTrailingZeros().toPlainString());

        	//6.库存天数
        	BigDecimal inventoryDays = BigDecimal.ZERO;
        	BigDecimal residueTotalInventory = totalInventory;

        	//7.未来30天的日均需求
        	BigDecimal averageDemandQuantity = BigDecimal.ZERO;
        	Date detailEndDate = null;
        	if(OemTradeTypeEnum.IN.getCode().equals(marketType)) {
        		//国内(日均需求)
        		List<CleanDemandDataDetailVO> detailList = dataDetailMap.get(reportVO.getId());
        		int size = detailList.size();
        		BigDecimal demandQty = detailList.subList(0, Math.min(size, averageDays.intValue())).stream()
        				.filter(e -> e.getDemandQuantity() != null)
                        .map(CleanDemandDataDetailVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        		averageDemandQuantity = demandQty.divide(averageDays, 2, BigDecimal.ROUND_HALF_UP);
        		detailEndDate = detailList.get(detailList.size() -1).getDemandTime();
        		//库存天数
        		for (CleanDemandDataDetailVO cleanDemandDataDetailVO : detailList) {
        			BigDecimal demandQuantity = cleanDemandDataDetailVO.getDemandQuantity();
        			if(demandQuantity == null || demandQuantity.compareTo(BigDecimal.ZERO) == 0) {
        				inventoryDays = inventoryDays.add(BigDecimal.ONE);
        				continue;
        			}
        			if(residueTotalInventory.compareTo(demandQuantity) >= 0) {
        				inventoryDays = inventoryDays.add(BigDecimal.ONE);
        				residueTotalInventory = residueTotalInventory.subtract(demandQuantity);
        			}else {
        				inventoryDays = inventoryDays.add(residueTotalInventory
        						.divide(demandQuantity, 2, BigDecimal.ROUND_HALF_UP));
        				residueTotalInventory = BigDecimal.ZERO;
        				break;
        			}
				}
        	}else {
        		//出口(日均需求)
        		List<CleanDemandDataDetailExitVO> detailExitList = dataExitDetailMap.get(reportVO.getId());
        		int size = detailExitList.size();
        		Integer demandQty = detailExitList.subList(0, Math.min(size, averageDays.intValue()))
        				.stream().map(CleanDemandDataDetailExitVO::getDemandQuantity)
        			    .filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
        		averageDemandQuantity = BigDecimal.valueOf(demandQty).divide(averageDays, 2, BigDecimal.ROUND_HALF_UP);
        		detailEndDate = detailExitList.get(detailExitList.size() -1).getDemandTime();
        		//库存天数
        		for (CleanDemandDataDetailExitVO cleanDemandDataDetailExitVO : detailExitList) {
        			if(cleanDemandDataDetailExitVO.getDemandQuantity() == null || cleanDemandDataDetailExitVO.getDemandQuantity() == 0) {
        				inventoryDays = inventoryDays.add(BigDecimal.ONE);
        				continue;
        			}
        			BigDecimal demandQuantity = BigDecimal.valueOf(cleanDemandDataDetailExitVO.getDemandQuantity());
        			if(residueTotalInventory.compareTo(demandQuantity) >= 0) {
        				inventoryDays = inventoryDays.add(BigDecimal.ONE);
        				residueTotalInventory = residueTotalInventory.subtract(demandQuantity);
        			}else {
        				inventoryDays = inventoryDays.add(residueTotalInventory
        						.divide(demandQuantity, 2, BigDecimal.ROUND_HALF_UP));
        				residueTotalInventory = BigDecimal.ZERO;
        				break;
        			}
				}
        	}
        	reportVO.setAverageDemandQuantity(averageDemandQuantity);
        	//如果仍有库存，根据最后一天的日期，取最新已发布的一致性需求预测，
        	//下个月的业务预测及下下个月，根据装车日历均摊，用剩余数量依次冲减，得到汇总的库存天数
        	inventoryDays = getCancelInventoryDays(oemProductLineMapVOMap, resourceCodeCalendarMap, reportVO, oemCode,
					vehicleModelCode, inventoryDays, residueTotalInventory, detailEndDate);
        	reportVO.setInventoryDays(inventoryDays);

        	//9.成本金额(万元)合计*单价
        	BigDecimal costAmount = BigDecimal.ZERO;
        	//10.日均需求金额(未来30天日均需求*单价)
        	BigDecimal averageDemandAmount = BigDecimal.ZERO;
        	String itemCostStr = productMap.get(productCode).getItemCost();
        	if(StringUtils.isNotEmpty(itemCostStr) && new BigDecimal(itemCostStr).compareTo(BigDecimal.ZERO) > 0) {
        		costAmount = totalInventory.multiply(new BigDecimal(itemCostStr))
        				.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP);
        		averageDemandAmount = averageDemandQuantity.multiply(new BigDecimal(itemCostStr))
        				.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP);
        	}
        	reportVO.setCostAmount(costAmount);
        	reportVO.setAverageDemandAmount(averageDemandAmount);
		}
        return dataList;
	}

	private BigDecimal getCancelInventoryDays(Map<String, List<OemProductLineMapVO>> oemProductLineMapVOMap,
			Map<String, List<ResourceCalendarVO>> resourceCodeCalendarMap, CleanDemandProductInventoryReportVO reportVO,
			String oemCode, String vehicleModelCode, BigDecimal inventoryDays, BigDecimal residueTotalInventory,
			Date detailEndDate) {
		if(residueTotalInventory.compareTo(BigDecimal.ZERO) > 0){
			// 获取对应的厂线数据 主机厂 + 车型
			List<OemProductLineMapVO> oemProductLineMapVOList = oemProductLineMapVOMap.get(String.join("&", oemCode,
					vehicleModelCode));
			//获取主机厂装车日历数据
			Map<String, List<ResourceCalendarVO>> oemResourceCalendarsMapOfMonth = new HashMap<>();
			if (CollectionUtils.isNotEmpty(oemProductLineMapVOList)) {
				List<ResourceCalendarVO> resourceCalendarVOS = new ArrayList<>();
				for (OemProductLineMapVO oemProductLineMapVO : oemProductLineMapVOList) {
					List<ResourceCalendarVO> list = resourceCodeCalendarMap.get(String
							.join("&", oemProductLineMapVO.getLineCode(),vehicleModelCode));
					if (CollectionUtils.isNotEmpty(list)){
						//一天当中有多个日历,进行去重,只留一天
						Map<String,ResourceCalendarVO> map =
								list.stream().collect(Collectors.toMap(
										item -> DateUtils.dateToString(item.getWorkDay()),
								item -> item,(existing, replacement) -> existing));
						resourceCalendarVOS.addAll(new ArrayList<>(map.values()));
					}
				}
				if (CollectionUtils.isNotEmpty(resourceCalendarVOS)) {
					oemResourceCalendarsMapOfMonth = resourceCalendarVOS.stream().collect(Collectors
							.groupingBy(item -> DateUtils.dateToString(item.getWorkDay(), DateUtils.YEAR_MONTH)));
				}
			}
			//获取对应的业务预测数据及装车日历
			BigDecimal nextMonthForecastQuantity = reportVO.getNextMonthForecastQuantity();
			BigDecimal afterNextMonthForecastQuantity = reportVO.getAfterNextMonthForecastQuantity();
			Date nextMonth = DateUtils.moveMonth(detailEndDate, 1);
			Date afterNextMonth = DateUtils.moveMonth(detailEndDate, 2);
			List<ResourceCalendarVO> nextMonthCalendarlist = oemResourceCalendarsMapOfMonth
					.getOrDefault(DateUtils.dateToString(nextMonth, DateUtils.YEAR_MONTH), new ArrayList<>());
			List<String> nextMonthWorkDayStrList = nextMonthCalendarlist.stream().map(e -> DateUtils.dateToString(e.getWorkDay()))
					.distinct().collect(Collectors.toList());
			List<ResourceCalendarVO> afterNextMonthCalendarlist = oemResourceCalendarsMapOfMonth
					.getOrDefault(DateUtils.dateToString(afterNextMonth, DateUtils.YEAR_MONTH), new ArrayList<>());
			List<String> afterNextMonthWorkDayStrList = afterNextMonthCalendarlist.stream().map(e -> DateUtils.dateToString(e.getWorkDay()))
					.distinct().collect(Collectors.toList());
			List<Date> nextMonthAllDays = DateUtils.getIntervalDates(DateUtils.getMonthFirstDay(nextMonth),
					DateUtils.getMonthLastDay(nextMonth));
			List<Date> afterNextMonthAllDays = DateUtils.getIntervalDates(DateUtils.getMonthFirstDay(afterNextMonth),
					DateUtils.getMonthLastDay(afterNextMonth));
			List<BigDecimal> nextInventoryDayList = getInventoryDays(inventoryDays, residueTotalInventory, nextMonthForecastQuantity,
					nextMonthWorkDayStrList, nextMonthAllDays);
			inventoryDays = nextInventoryDayList.get(0);
			residueTotalInventory = nextInventoryDayList.get(1);
			if(residueTotalInventory.compareTo(BigDecimal.ZERO) > 0) {
				List<BigDecimal> afterNextInventoryDayList = getInventoryDays(inventoryDays, residueTotalInventory, afterNextMonthForecastQuantity,
						afterNextMonthWorkDayStrList, afterNextMonthAllDays);
		    	inventoryDays = afterNextInventoryDayList.get(0);
			}
		}
		return inventoryDays;
	}

	/**
	 * 如果仍有库存，根据最后一天的日期，取最新已发布的一致性需求预测，
	 * 下个月的业务预测，根据装车日历均摊，用剩余数量依次冲减，得到汇总的库存天数
	 * @param inventoryDays
	 * @param residueTotalInventory
	 * @param nextMonthForecastQuantity
	 * @param nextMonthWorkDayStrList
	 * @param nextMonthAllDays
	 * @return
	 */
	private List<BigDecimal> getInventoryDays(BigDecimal inventoryDays, BigDecimal residueTotalInventory,
			BigDecimal nextMonthForecastQuantity, List<String> nextMonthWorkDayStrList, List<Date> nextMonthAllDays) {
		Integer nextMonthWorkDayNum = nextMonthWorkDayStrList.size();
		for (Date dayTime : nextMonthAllDays) {
			if(!nextMonthWorkDayStrList.contains(DateUtils.dateToString(dayTime)) && !isWeekend(dayTime)) {
				nextMonthWorkDayNum++;
			}
		}
		if(nextMonthForecastQuantity.compareTo(BigDecimal.ZERO) == 0) {
			inventoryDays = inventoryDays.add(BigDecimal.valueOf(nextMonthAllDays.size()));
		}else {
			//进行均摊
			BigDecimal dayQty = nextMonthForecastQuantity
					.divide(BigDecimal.valueOf(nextMonthWorkDayNum), 2, BigDecimal.ROUND_HALF_UP);
			for (Date dayTime : nextMonthAllDays) {
				//首先判断今天是否是装车时间
				if(nextMonthWorkDayStrList.contains(DateUtils.dateToString(dayTime)) && !isWeekend(dayTime)) {
					if(residueTotalInventory.compareTo(dayQty) >= 0) {
						inventoryDays = inventoryDays.add(BigDecimal.ONE);
						residueTotalInventory = residueTotalInventory.subtract(dayQty);
					}else {
						inventoryDays = inventoryDays.add(residueTotalInventory.divide(dayQty, 2, BigDecimal.ROUND_HALF_UP));
						residueTotalInventory = BigDecimal.ZERO;
						break;
					}
				}else {
					inventoryDays = inventoryDays.add(BigDecimal.ONE);
				}
			}
		}
		return Arrays.asList(inventoryDays, residueTotalInventory);
	}

	private BigDecimal getInventory(String op, String productCode, String stockPointCode,
            Map<String, List<InventoryBatchDetailVO>> inventoryMap,
            Map<String, SubInventoryCargoLocationVO> cargoLocationMap) {
		String opMainKey = String.join("&", stockPointCode, productCode, op);
        List<InventoryBatchDetailVO> inventoryBatchDetails = inventoryMap.entrySet().stream()
                .filter(entry -> entry.getKey().equals(opMainKey))
                .map(Map.Entry::getValue).flatMap(List::stream).filter(p -> {
                    String freightSpace = p.getFreightSpace();
                    SubInventoryCargoLocationVO subInventoryCargoLocation = cargoLocationMap.get(freightSpace);
                    return null != subInventoryCargoLocation;
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(inventoryBatchDetails)) {
            return BigDecimal.ZERO;
        }
        return inventoryBatchDetails.stream().filter(x ->
                StringUtils.isNotBlank(x.getCurrentQuantity())).map(x ->
                new BigDecimal(x.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
	}
	
	public Map<String, SubInventoryCargoLocationVO> getCargoLocationMap(
            List<InventoryBatchDetailVO> inventoryBatchDetails, String scenario) {
        Map<String, SubInventoryCargoLocationVO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(inventoryBatchDetails)) {
            return result;
        }
        List<String> spaceList = inventoryBatchDetails.stream().map(InventoryBatchDetailVO::getFreightSpace)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(spaceList)) {
            return result;
        }
        return mpsFeign.queryByFreightSpaces(scenario,
                spaceList, StockPointTypeEnum.BC.getCode()).stream().collect(Collectors
                .toMap(SubInventoryCargoLocationVO::getFreightSpaceCode,
                        Function.identity(), (v1, v2) -> v1));
    }
	
	/**
	 * 统计当月待发数据
	 * @param switchRelation
	 * @param oemCodes
	 * @param productCodes
	 * @return
	 */
	private Map<String, BigDecimal> getDeliveryPlanPublishedCurrentMonthly(SwitchRelationVO switchRelation, List<String> oemCodes, List<String> productCodes) {
        List<String> allProductCodes = switchRelation.getAllProductCodes();
        Date startDemandTime = DateUtils.getDayFirstTime(new Date());
        Date endDemandTime = DateUtils.getMonthLastDay(startDemandTime);
        List<DeliveryPlanPublishedMonthVO> deliveryPlanPublishedCurrentMonthList = deliveryPlanPublishedDao
        		.selectCurrentMonthVOByItemCodes(oemCodes, allProductCodes, startDemandTime, endDemandTime);
    	Map<String, String> newOldMap = switchRelation.getNewOldMap();
    	Map<String, String> oldNewMap = switchRelation.getOldNewMap();
        Map<String, BigDecimal> qtyMap = deliveryPlanPublishedCurrentMonthList.stream()
                .collect(Collectors.groupingBy(DeliveryPlanPublishedMonthVO::getProductCode,
                        Collectors.reducing(BigDecimal.ZERO, DeliveryPlanPublishedMonthVO::getSumDemandQuantity,
                                BigDecimal::add)));
        for (DeliveryPlanPublishedMonthVO item : deliveryPlanPublishedCurrentMonthList) {
            BigDecimal sumQty = item.getSumDemandQuantity();
            String itemCode = item.getProductCode();
            if (newOldMap.containsKey(itemCode)) {
                String xKey = newOldMap.get(itemCode);
                sumQty = sumQty.add(qtyMap.getOrDefault(xKey, BigDecimal.ZERO));
            }
            if (oldNewMap.containsKey(itemCode)) {
                String yKey = oldNewMap.get(itemCode);
                sumQty = sumQty.add(qtyMap.getOrDefault(yKey, BigDecimal.ZERO));
            }
            item.setSumDemandQuantity(sumQty);
        }
        return deliveryPlanPublishedCurrentMonthList
                .stream().collect(Collectors.groupingBy(e-> e.getOemCode() + "&" + e.getProductCode(),
                        Collectors.reducing(BigDecimal.ZERO, DeliveryPlanPublishedMonthVO::getSumDemandQuantity,
                                BigDecimal::add)));
    }
	
	private Map<String, BigDecimal> getWarehouseReleaseRecordMonthly(List<WarehouseReleaseRecordMonthVO> records,
            Map<String, String> newOldMap,
            Map<String, String> oldNewMap) {
		List<WarehouseReleaseRecordMonthVO> filterList = records.parallelStream()
                .filter(x -> x != null && x.getYearMonth() != null
                        && StringUtils.isNotBlank(x.getItemCode()) && x.getSumQty() != null).collect(Collectors.toList());
        Map<String, BigDecimal> qtyMap = filterList.stream().collect(Collectors.groupingBy(item ->
                item.getItemCode() + Constants.DELIMITER + item.getYearMonth(), Collectors
                .reducing(BigDecimal.ZERO, WarehouseReleaseRecordMonthVO::getSumQty, BigDecimal::add)));
        for (WarehouseReleaseRecordMonthVO item : filterList) {
            BigDecimal sumQty = item.getSumQty();
            String itemCode = item.getItemCode();
            String yearMonth = item.getYearMonth();
            if (newOldMap.containsKey(itemCode)) {
                String xItemCode = newOldMap.get(itemCode);
                String xKey = String.join(Constants.DELIMITER, xItemCode, yearMonth);
                sumQty = sumQty.add(qtyMap.getOrDefault(xKey, BigDecimal.ZERO));
            }
            if (oldNewMap.containsKey(itemCode)) {
                String yItemCode = oldNewMap.get(itemCode);
                String yKey = String.join(Constants.DELIMITER, yItemCode, yearMonth);
                sumQty = sumQty.add(qtyMap.getOrDefault(yKey, BigDecimal.ZERO));
            }
            item.setSumQty(sumQty);
        }
        // 获取每个月的发货量数据
        return filterList.stream().collect(Collectors.groupingBy(e-> e.getOemCode() + "&" + e.getItemCode(),
                Collectors.reducing(BigDecimal.ZERO, WarehouseReleaseRecordMonthVO::getSumQty,
                                BigDecimal::add)));
	}
	
	private Map<String, BigDecimal> getWarehouseReleaseToRecordMonthly(List<WarehouseReleaseToWarehouseMonthVO> records,
            Map<String, String> newOldMap,
            Map<String, String> oldNewMap) {
		List<WarehouseReleaseToWarehouseMonthVO> filterList = records.parallelStream()
				.filter(x -> x != null && x.getYearMonth() != null
					&& StringUtils.isNotBlank(x.getItemCode()) && x.getSumQty() != null).collect(Collectors.toList());
		Map<String, BigDecimal> qtyMap = filterList.stream().collect(Collectors.groupingBy(item ->
				item.getItemCode() + Constants.DELIMITER + item.getYearMonth(), Collectors
				.reducing(BigDecimal.ZERO, WarehouseReleaseToWarehouseMonthVO::getSumQty, BigDecimal::add)));
		for (WarehouseReleaseToWarehouseMonthVO item : filterList) {
			BigDecimal sumQty = item.getSumQty();
			String itemCode = item.getItemCode();
			String yearMonth = item.getYearMonth();
			if (newOldMap.containsKey(itemCode)) {
				String xItemCode = newOldMap.get(itemCode);
				String xKey = String.join(Constants.DELIMITER, xItemCode, yearMonth);
				sumQty = sumQty.add(qtyMap.getOrDefault(xKey, BigDecimal.ZERO));
			}
			if (oldNewMap.containsKey(itemCode)) {
				String yItemCode = oldNewMap.get(itemCode);
				String yKey = String.join(Constants.DELIMITER, yItemCode, yearMonth);
				sumQty = sumQty.add(qtyMap.getOrDefault(yKey, BigDecimal.ZERO));
			}
			item.setSumQty(sumQty);
		}
		return filterList.stream().collect(Collectors.groupingBy(e-> e.getOemCode() + "&" + e.getItemCode(),
				Collectors.reducing(BigDecimal.ZERO, WarehouseReleaseToWarehouseMonthVO::getSumQty,
								BigDecimal::add)));
	}

	@Override
	public List<CleanDemandProductInventoryCollectVO> queryProductInventoryReportCollect(
			CleanDemandProductInventoryReportDTO queryDTO) {
		Pagination pagination = new Pagination();
		pagination.setPageNum(1);
		pagination.setPageSize(999999);;
		String queryCriteriaParam = "";
		String customerAbbreviation = queryDTO.getCustomerAbbreviation();
		String vehicleModelCode = queryDTO.getVehicleModelCode();
		if(StringUtils.isNotEmpty(customerAbbreviation)) {
			queryCriteriaParam = "customer_abbreviation = '" + customerAbbreviation + "'";
		}
		if(StringUtils.isNotEmpty(vehicleModelCode) && StringUtils.isNotEmpty(queryCriteriaParam)) {
			queryCriteriaParam = " and vehicle_model_code = '" + vehicleModelCode + "'";
		} else if(StringUtils.isNotEmpty(customerAbbreviation) && StringUtils.isEmpty(queryCriteriaParam)) {
			queryCriteriaParam = " vehicle_model_code = '" + vehicleModelCode + "'";
		}
		List<CleanDemandProductInventoryReportVO> reportList = this.queryProductInventoryReportPage(pagination,
				null, queryCriteriaParam);
		reportList.forEach( e-> {
			if(StringUtils.isEmpty(e.getCustomerAbbreviation())) {
				e.setCustomerAbbreviation("");
			}
			if(StringUtils.isEmpty(e.getVehicleModelCode())) {
				e.setVehicleModelCode("");
			}
		});
		//先按照客户分组，客户下再按照车型分组
		Map<String, List<CleanDemandProductInventoryReportVO>> customerReportMap = reportList.stream()
				.collect(Collectors.groupingBy(CleanDemandProductInventoryReportVO::getCustomerAbbreviation));
		List<CleanDemandProductInventoryCollectVO> resultList = new ArrayList<>();
		for (List<CleanDemandProductInventoryReportVO> customerGroupList : customerReportMap.values()) {
			CleanDemandProductInventoryCollectVO customerGroup = new CleanDemandProductInventoryCollectVO();
			customerGroup.setCustomerAbbreviation(customerGroupList.get(0).getCustomerAbbreviation());
			customerGroup.setHighlightFlag(YesOrNoEnum.YES.getCode());
			resultList.add(customerGroup);
			//按照车型分组
			Map<String, List<CleanDemandProductInventoryReportVO>> vehicleModelCodeMap = customerGroupList.stream()
					.collect(Collectors.groupingBy(CleanDemandProductInventoryReportVO::getVehicleModelCode));
			BigDecimal totalAverageDemandQuantity = BigDecimal.ZERO;
			BigDecimal totalProductInventory = BigDecimal.ZERO;
			BigDecimal totalProductInventoryDays = BigDecimal.ZERO;
			BigDecimal totalAverageDemandAmount = BigDecimal.ZERO;
			BigDecimal totalCostAmount = BigDecimal.ZERO;
			BigDecimal totalProductInventoryAmount = BigDecimal.ZERO;
			for (List<CleanDemandProductInventoryReportVO> vehicleModelCodeGroupList : vehicleModelCodeMap.values()) {
				CleanDemandProductInventoryCollectVO detail = new CleanDemandProductInventoryCollectVO();
				//未来30天汇总
				BigDecimal averageDemandQuantity = vehicleModelCodeGroupList.stream()
					    .map(CleanDemandProductInventoryReportVO::getAverageDemandQuantity)
					    .filter(Objects::nonNull)
					    .reduce(BigDecimal.ZERO, BigDecimal::add);
				//产品库存数量
				BigDecimal productInventory = vehicleModelCodeGroupList.stream()
					    .map(CleanDemandProductInventoryReportVO::getTotalInventory)
					    .filter(Objects::nonNull)
					    .reduce(BigDecimal.ZERO, BigDecimal::add);
				//产品周转天数
				BigDecimal productInventoryDays = vehicleModelCodeGroupList.stream()
					    .map(CleanDemandProductInventoryReportVO::getInventoryDays)
					    .filter(Objects::nonNull)
					    .reduce(BigDecimal.ZERO, BigDecimal::add);
				//日均需求金额(万元)
				BigDecimal averageDemandAmount = vehicleModelCodeGroupList.stream()
					    .map(CleanDemandProductInventoryReportVO::getAverageDemandAmount)
					    .filter(Objects::nonNull)
					    .reduce(BigDecimal.ZERO, BigDecimal::add);
				//成本金额
				BigDecimal costAmount = vehicleModelCodeGroupList.stream()
					    .map(CleanDemandProductInventoryReportVO::getCostAmount)
					    .filter(Objects::nonNull)
					    .reduce(BigDecimal.ZERO, BigDecimal::add);
				//产品库存天数（金额） = 成本金额 / 日均需求金额
				BigDecimal productInventoryAmount = BigDecimal.ZERO;
				if(averageDemandAmount.compareTo(BigDecimal.ZERO) > 0) {
					productInventoryAmount = costAmount.divide(averageDemandAmount, 2, BigDecimal.ROUND_HALF_UP);
				}
				detail.setHighlightFlag(YesOrNoEnum.NO.getCode());
				detail.setVehicleModelCode(vehicleModelCodeGroupList.get(0).getVehicleModelCode());
				detail.setAverageDemandQuantity(averageDemandQuantity);
				detail.setProductInventory(productInventory);
				detail.setProductInventoryDays(productInventoryDays);
				detail.setAverageDemandAmount(averageDemandAmount);
				detail.setCostAmount(costAmount);
				detail.setProductInventoryAmount(productInventoryAmount);
				resultList.add(detail);

				totalAverageDemandQuantity = totalAverageDemandQuantity.add(averageDemandQuantity);
				totalProductInventory = totalProductInventory.add(productInventory);
				totalProductInventoryDays = totalProductInventoryDays.add(productInventoryDays);
				totalAverageDemandAmount = totalAverageDemandAmount.add(averageDemandAmount);
				totalCostAmount = totalCostAmount.add(costAmount);
				totalProductInventoryAmount = totalProductInventoryAmount.add(productInventoryAmount);
			}
			customerGroup.setAverageDemandQuantity(totalAverageDemandQuantity);
			customerGroup.setProductInventory(totalProductInventory);
			customerGroup.setProductInventoryDays(totalProductInventoryDays);
			customerGroup.setAverageDemandAmount(totalAverageDemandAmount);
			customerGroup.setCostAmount(totalCostAmount);
			customerGroup.setProductInventoryAmount(totalProductInventoryAmount);
		}

        // 处理材料数据
        assembleMaterialData(resultList);
		return resultList;
	}

    private void assembleMaterialData(List<CleanDemandProductInventoryCollectVO> resultList) {
        String scenario = SystemHolder.getScenario();
        // 查询B类专用
        List<VehicleInventoryClassBDedicatedReportVO> vehicleInventoryClassBDedicatedReportVOList =
                mrpFeign.selectVehicleInventoryClassBDedicatedReportByParams(scenario, new HashMap<>());
        Map<String, List<VehicleInventoryClassBDedicatedReportVO>> vehicleInventoryClassBDedicatedReportVOMap =
                vehicleInventoryClassBDedicatedReportVOList.stream()
                        .collect(Collectors.groupingBy(data -> String.join(",", data.getCustomer(), data.getVehicleModelCode())));
        // 查询A类专用
        List<VehicleInventoryClassAReportVO> vehicleInventoryClassAReportVOList =
                mrpFeign.selectVehicleInventoryClassAReportByParams(scenario, new HashMap<>());
        Map<String, List<VehicleInventoryClassAReportVO>> vehicleInventoryClassAReportVOMap =
                vehicleInventoryClassAReportVOList.stream()
                        .collect(Collectors.groupingBy(data -> String.join(",", data.getCustomer(), data.getVehicleModelCode())));

        for (CleanDemandProductInventoryCollectVO vo : resultList) {
            String key = String.join(",", vo.getCustomerAbbreviation(), vo.getVehicleModelCode());
            // 获取B类专用
            List<VehicleInventoryClassBDedicatedReportVO> vehicleInventoryClassBDedicatedReportVOS = vehicleInventoryClassBDedicatedReportVOMap.get(key);
            if (CollectionUtils.isNotEmpty(vehicleInventoryClassBDedicatedReportVOS)) {
                // 本厂库存
                BigDecimal factoryInventory = vehicleInventoryClassBDedicatedReportVOS.stream()
                        .map(VehicleInventoryClassBDedicatedReportVO::getFactoryInventory)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 本厂在库金额
                BigDecimal factoryInventoryAmountThousand = vehicleInventoryClassBDedicatedReportVOS.stream()
                        .map(VehicleInventoryClassBDedicatedReportVO::getFactoryInventoryAmountThousand)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 供应商库存
                BigDecimal supplierInventory = vehicleInventoryClassBDedicatedReportVOS.stream()
                        .map(VehicleInventoryClassBDedicatedReportVO::getSupplierInventory)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 供应商在库金额
                BigDecimal supplierInventoryAmountThousand = vehicleInventoryClassBDedicatedReportVOS.stream()
                        .map(VehicleInventoryClassBDedicatedReportVO::getSupplierInventoryAmountThousand)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 未来30天日均需求
                BigDecimal next30DaysDailyAverageDemand = vehicleInventoryClassBDedicatedReportVOS.stream()
                        .map(VehicleInventoryClassBDedicatedReportVO::getNext30DaysDailyAverageDemand)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 日均需求金额
                BigDecimal dailyAverageDemandAmount = vehicleInventoryClassBDedicatedReportVOS.stream()
                        .map(VehicleInventoryClassBDedicatedReportVO::getDailyAverageDemandAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 成本金额
                BigDecimal totalCostAmountThousand = vehicleInventoryClassBDedicatedReportVOS.stream()
                        .map(VehicleInventoryClassBDedicatedReportVO::getTotalCostAmountThousand)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 本厂库存 ÷ 未来30天日均需求
                if (factoryInventory.compareTo(BigDecimal.ZERO) > 0 && next30DaysDailyAverageDemand.compareTo(BigDecimal.ZERO) > 0) {
                    vo.setMaterialBTypeFactoryInventoryDays(factoryInventory.divide(next30DaysDailyAverageDemand, 0, RoundingMode.HALF_UP).intValue());
                }

                // 供应商库存 ÷ 未来30天日均需求
                if (supplierInventory.compareTo(BigDecimal.ZERO) > 0 && next30DaysDailyAverageDemand.compareTo(BigDecimal.ZERO) > 0) {
                    vo.setMaterialBTypeSupplierInventoryDays(supplierInventory.divide(next30DaysDailyAverageDemand, 0, RoundingMode.HALF_UP).intValue());
                }

                // 本厂库存天数 + 供应商库存天数
                vo.setMaterialBTypeInventoryDays(vo.getMaterialBTypeFactoryInventoryDays() + vo.getMaterialBTypeSupplierInventoryDays());

                // 成本金额
                vo.setMaterialBTypeTotalCostAmountThousand(totalCostAmountThousand);
                // 本厂金额
                vo.setMaterialBTypeFactoryInventoryAmountThousand(factoryInventoryAmountThousand);
                // 供应商金额
                vo.setMaterialBTypeSupplierInventoryAmountThousand(supplierInventoryAmountThousand);
                // 日均需求金额
                vo.setMaterialBTypeDayDemandCostAmount(dailyAverageDemandAmount);

                if (vo.getMaterialBTypeDayDemandCostAmount().compareTo(BigDecimal.ZERO) > 0 &&
                        vo.getMaterialBTypeTotalCostAmountThousand().compareTo(BigDecimal.ZERO) > 0) {
                    // 成本金额 ÷ 日均需求金额
                    vo.setMaterialBTypeInventoryCost(vo.getMaterialBTypeTotalCostAmountThousand().divide(vo.getMaterialBTypeDayDemandCostAmount(), 2, RoundingMode.DOWN));
                }
            }

            // 获取A类专用
            List<VehicleInventoryClassAReportVO> vehicleInventoryClassAReportVOS = vehicleInventoryClassAReportVOMap.get(key);
            if (CollectionUtils.isNotEmpty(vehicleInventoryClassAReportVOS)) {
                // 本厂库存
                BigDecimal factoryInventory = vehicleInventoryClassAReportVOS.stream()
                        .map(VehicleInventoryClassAReportVO::getFuyaoInventoryQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 本厂在库金额
                BigDecimal factoryInventoryAmountThousand = vehicleInventoryClassAReportVOS.stream()
                        .map(VehicleInventoryClassAReportVO::getFuyaoInStockAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 供应商库存
                BigDecimal supplierInventory = vehicleInventoryClassAReportVOS.stream()
                        .map(VehicleInventoryClassAReportVO::getSupplierInventoryQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 供应商在库金额
                BigDecimal supplierInventoryAmountThousand = vehicleInventoryClassAReportVOS.stream()
                        .map(VehicleInventoryClassAReportVO::getSupplierInStockAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 未来30天日均需求
                BigDecimal next30DaysDailyAverageDemand = vehicleInventoryClassAReportVOS.stream()
                        .map(VehicleInventoryClassAReportVO::getFutureThirtyDayDemand)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 日均需求金额
                BigDecimal dailyAverageDemandAmount = vehicleInventoryClassAReportVOS.stream()
                        .map(VehicleInventoryClassAReportVO::getDayDemandAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 成本金额
                BigDecimal totalCostAmountThousand = vehicleInventoryClassAReportVOS.stream()
                        .map(VehicleInventoryClassAReportVO::getCostAmountTotal)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 本厂库存 ÷ 未来30天日均需求
                if (factoryInventory.compareTo(BigDecimal.ZERO) > 0 && next30DaysDailyAverageDemand.compareTo(BigDecimal.ZERO) > 0) {
                    vo.setMaterialATypeFactoryInventoryDays(factoryInventory.divide(next30DaysDailyAverageDemand, 0, RoundingMode.HALF_UP).intValue());
                }

                // 供应商库存 ÷ 未来30天日均需求
                if (supplierInventory.compareTo(BigDecimal.ZERO) > 0 && next30DaysDailyAverageDemand.compareTo(BigDecimal.ZERO) > 0) {
                    vo.setMaterialATypeSupplierInventoryDays(supplierInventory.divide(next30DaysDailyAverageDemand, 0, RoundingMode.HALF_UP).intValue());
                }

                // 本厂库存天数 + 供应商库存天数
                vo.setMaterialATypeInventoryDays(vo.getMaterialATypeFactoryInventoryDays() + vo.getMaterialATypeSupplierInventoryDays());

                // 成本金额
                vo.setMaterialATypeTotalCostAmountThousand(totalCostAmountThousand);
                // 本厂金额
                vo.setMaterialATypeFactoryInventoryAmountThousand(factoryInventoryAmountThousand);
                // 供应商金额
                vo.setMaterialATypeSupplierInventoryAmountThousand(supplierInventoryAmountThousand);
                // 日均需求金额
                vo.setMaterialATypeDayDemandCostAmount(dailyAverageDemandAmount);

                if (vo.getMaterialATypeDayDemandCostAmount().compareTo(BigDecimal.ZERO) > 0 &&
                        vo.getMaterialATypeTotalCostAmountThousand().compareTo(BigDecimal.ZERO) > 0) {
                    // 成本金额 ÷ 日均需求金额
                    vo.setMaterialATypeInventoryCost(vo.getMaterialATypeTotalCostAmountThousand().divide(vo.getMaterialATypeDayDemandCostAmount(), 2, RoundingMode.DOWN));
                }

                // A + B 成本总金额
                vo.setMaterialTotalCostAmountThousand(vo.getMaterialATypeTotalCostAmountThousand().add(vo.getMaterialBTypeTotalCostAmountThousand()));
                // A + B 库存天数
                vo.setMaterialInventoryDays(vo.getMaterialATypeInventoryDays() + vo.getMaterialBTypeInventoryDays());
            }
        }
    }
}