package com.yhl.scp.dfp.report.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.google.common.collect.ImmutableMap;
import org.apache.poi.ss.util.CellRangeAddress;
import com.yhl.scp.biz.common.excel.RangeMergeStrategy;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringConvertUtils;
import com.yhl.scp.biz.common.excel.SheetStyleHandler;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.dfp.utils.EasyExcelUtil;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedDao;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.oem.service.OemInventorySubmissionService;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemStockPointMapService;
import com.yhl.scp.dfp.oem.service.OemTransportTimeService;
import com.yhl.scp.dfp.origin.service.OriginDemandVersionService;
import com.yhl.scp.dfp.report.dto.DemandDeliveryProductionDetailDTO;
import com.yhl.scp.dfp.report.service.DemandDeliveryProductionService;
import com.yhl.scp.dfp.report.vo.DemandDeliveryProductionDetailVO;
import com.yhl.scp.dfp.report.vo.DemandDeliveryProductionVO;
import com.yhl.scp.dfp.stock.infrastructure.dao.InventoryBatchDetailDao;
import com.yhl.scp.dfp.stock.service.InventoryShiftService;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.basic.routing.vo.RoutingBasicVO;
import com.yhl.scp.mds.basic.routing.vo.StandardStepBasicVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import com.yhl.scp.mps.plan.vo.MasterPlanTaskVO;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.servlet.http.HttpServletResponse;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <code>DemandDeliveryProductionServiceImpl</code>
 * <p>
 * 需求发货生产报表服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-15 14:27:02
 */
@Service
@Slf4j
public class DemandDeliveryProductionServiceImpl implements DemandDeliveryProductionService {

    private static final ExecutorService SHARED_EXECUTOR = Executors.newFixedThreadPool(
            Math.min(10, Runtime.getRuntime().availableProcessors() * 2),
            new ThreadFactoryBuilder().setNameFormat("demand-report-pool-%d").build()
    );

    @Resource
    DeliveryPlanPublishedDao deliveryPlanPublishedDao;
    @Resource
    OriginDemandVersionService originDemandVersionService;
    @Resource
    LoadingDemandSubmissionDetailService loadingDemandSubmissionDetailService;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private MpsFeign mpsFeign;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private InventoryBatchDetailDao inventoryBatchDetailDao;
    @Resource
    private OemTransportTimeService oemTransportTimeService;
    @Resource
    private OemStockPointMapService oemStockPointMapService;
    @Resource
    private OemInventorySubmissionService oemInventorySubmissionService;
    @Resource
    private OemService oemService;
    @Resource
    private WarehouseReleaseRecordService warehouseReleaseRecordService;
    @Resource
    private WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;
    @Resource
    private IpsFeign ipsFeign;
    @Resource
    private InventoryShiftService inventoryShiftService;
    // 常量定义
    public static final String PARAM_STOCK_POINT_TYPE = "stockPointType";
    public static final String PAINTING_OPERATION = "印刷";
    public static final String FORMING_OPERATION = "成型";
    public static final String MERGING_OPERATION = "合片";
    public static final String PACKAGING_OPERATION = "包装";

    // 工序操作常量
    public static final String OPERATION_PACKAGING = "PACKAGING";
    public static final String OPERATION_AFTERPACKING = "AFTERPACKING";
    public static final String OPERATION_HIGHVOLTAGEPACKAGING = "HIGHVOLTAGEPACKAGING";
    public static final String OPERATION_LAMINATION = "LAMINATION";
    public static final String OPERATION_SHAPELAMINATION = "SHAPELAMINATION";
    public static final String OPERATION_COATING = "COATING";
    public static final String OPERATION_PRINTING = "PRINTING";
    public static final String OPERATION_PRETREATMENT = "PRETREATMENT";
    public static final String OPERATION_NONASSETWAREHOUSE = "NONASSETWAREHOUSE";

    @Override
    public List<DemandDeliveryProductionVO> queryDemandDeliveryProductionReport(DemandDeliveryProductionDetailDTO dto) {
        String scenario = SystemHolder.getScenario();

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("总耗时");
        // 1. 提前计算时间范围
        stopWatch.stop(); stopWatch.start("计算时间范围");
        Date planStartTime = DateUtils.getCurrentDateTruncateTime();
        Date planEndTime = DateUtils.moveDay(planStartTime, 14);
        List<Date> intervalDates = DateUtils.getIntervalDates(planStartTime, planEndTime);
        stopWatch.stop();
        stopWatch.start("查询产品编码");
        List<String> queryProductCodes = getQueryProductCodes(dto);
        stopWatch.stop();
        if (CollectionUtils.isEmpty(queryProductCodes)) {
            log.info("StopWatch统计: {}", stopWatch.prettyPrint());
            return new ArrayList<>();
        }

        final Map<String, Long> asyncTimings = new ConcurrentHashMap<>();
        try {
            CompletableFuture<List<DeliveryPlanPublishedVO>> deliveryPlanFuture = CompletableFuture.supplyAsync(() -> {
                long s = System.nanoTime();
                try { return getDeliveryPlanData(queryProductCodes, planStartTime, planEndTime); }
                finally { asyncTimings.put("异步查询发货计划", TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - s)); }
            }, SHARED_EXECUTOR);

            CompletableFuture<List<MasterPlanTaskVO>> masterPlanFuture = CompletableFuture.supplyAsync(() -> {
                long s = System.nanoTime();
                try { return getMasterPlanData(scenario, queryProductCodes, planStartTime, planEndTime); }
                finally { asyncTimings.put("异步查询主生产计划", TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - s)); }
            }, SHARED_EXECUTOR);

            CompletableFuture<Map<String, List<InventoryBatchDetailVO>>> inventoryFuture = CompletableFuture.supplyAsync(() -> {
                long s = System.nanoTime();
                try { return getInventoryData(queryProductCodes); }
                finally { asyncTimings.put("异步查询库存数据", TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - s)); }
            }, SHARED_EXECUTOR);

            CompletableFuture<Map<String, List<LoadingDemandSubmissionDetailVO>>> loadingDemandFuture = CompletableFuture.supplyAsync(() -> {
                long s = System.nanoTime();
                try { return getLoadingDemandData(planStartTime, planEndTime); }
                finally { asyncTimings.put("异步查询装车需求", TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - s)); }
            }, SHARED_EXECUTOR);

            // 二: 依赖较少的数据

            CompletableFuture<Map<String, String>> productStockFuture = CompletableFuture.supplyAsync(() -> {
                long s = System.nanoTime();
                try { return getProductStockMap(scenario, queryProductCodes); }
                finally { asyncTimings.put("查询产品库存点映射", TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - s)); }
            }, SHARED_EXECUTOR);

            CompletableFuture<Map<String, String>> standardStepFuture = CompletableFuture.supplyAsync(() -> {
                long s = System.nanoTime();
                try { return getStandardStepMap(scenario); }
                finally { asyncTimings.put("查询标准步骤映射", TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - s)); }
            }, SHARED_EXECUTOR);

            // 从发货计划中提取主机厂和产品信息
            CompletableFuture<Map<String, List<String>>> oemAndProductCodesFuture = deliveryPlanFuture.thenApplyAsync(deliveryPlanVOS -> {
                Map<String, List<String>> oemProductMap = createOemProductMap(deliveryPlanVOS);
                List<String> oemCodes = new ArrayList<>(oemProductMap.keySet());
                List<String> productCodes = oemProductMap.values().stream()
                        .flatMap(Collection::stream)
                        .distinct()
                        .collect(Collectors.toList());
                Map<String, List<String>> result = new HashMap<>();
                result.put("oemCodes", oemCodes);
                result.put("productCodes", productCodes);
                return result;
            }, SHARED_EXECUTOR);

            // 三: 依赖一或二的任务
            CompletableFuture<Map<String, List<SubInventoryCargoLocationVO>>> cargoLocationFuture = CompletableFuture.supplyAsync(() -> {
                long s = System.nanoTime();
                try { return getCargoLocationMap(scenario); }
                finally { asyncTimings.put("查询货位映射", TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - s)); }
            }, SHARED_EXECUTOR);

            CompletableFuture<Map<String, List<InventoryBatchDetailVO>>> finishInventoryFuture = inventoryFuture.thenApplyAsync(inventoryMap -> {
                long s = System.nanoTime();
                try { return getFinishInventoryMap(scenario, inventoryMap); }
                finally { asyncTimings.put("获取成品库存", TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - s)); }
            }, SHARED_EXECUTOR);

            CompletableFuture<Map<String, List<InventoryBatchDetailVO>>> operationInventoryFuture = inventoryFuture.thenApplyAsync(inventoryMap -> {
                long s = System.nanoTime();
                try { return getOperationInventoryMap(scenario, inventoryMap); }
                finally { asyncTimings.put("获取工序库存", TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - s)); }
            }, SHARED_EXECUTOR);

            CompletableFuture<Map<String, BigDecimal>> transitStockFuture = oemAndProductCodesFuture.thenApplyAsync(codesMap -> {
                long s = System.nanoTime();
                try {
                    return getTransitStockMap(codesMap.get("oemCodes"), codesMap.get("productCodes"));
                } finally {
                    asyncTimings.put("组装中转库存", TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - s));
                }
            }, SHARED_EXECUTOR);

            CompletableFuture<Map<String, BigDecimal>> transportingQtyFuture = oemAndProductCodesFuture.thenApplyAsync(codesMap -> {
                long s = System.nanoTime();
                try {
                    return getTransportingQtyMap(codesMap.get("oemCodes"), codesMap.get("productCodes"));
                } finally {
                    asyncTimings.put("获取在途库存", TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - s));
                }
            }, SHARED_EXECUTOR);

            CompletableFuture<Map<String, BigDecimal>> shippedTodayFuture = CompletableFuture.supplyAsync(() -> {
                long s = System.nanoTime();
                try { return getShippedTodayData(scenario); }
                finally { asyncTimings.put("异步查询当天已发货", TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - s)); }
            }, SHARED_EXECUTOR);


            // ==================== 等待所有任务完成 ====================
            stopWatch.start("等待所有异步任务完成");
            CompletableFuture.allOf(
                    masterPlanFuture, loadingDemandFuture, productStockFuture, standardStepFuture,
                    cargoLocationFuture, finishInventoryFuture, operationInventoryFuture,
                    transitStockFuture, transportingQtyFuture, shippedTodayFuture
            ).join();
            stopWatch.stop();

            // ==================== 获取所有结果 (join()之后, get()是非阻塞的) ====================
            stopWatch.start("获取异步结果");
            List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = deliveryPlanFuture.get();
            List<MasterPlanTaskVO> operationTasks = masterPlanFuture.get();
            Map<String, List<LoadingDemandSubmissionDetailVO>> submissionDetailMap = loadingDemandFuture.get();
            Map<String, BigDecimal> shippedTodayMap = shippedTodayFuture.get();
            stopWatch.stop();

            if(CollectionUtils.isEmpty(deliveryPlanPublishedVOS)){
                throw new BusinessException("找不到发货计划");
            }

            // 调用CPU密集型处理方法
            return processReportData(deliveryPlanPublishedVOS, operationTasks, submissionDetailMap, intervalDates,
                    transportingQtyFuture.get(), productStockFuture.get(), cargoLocationFuture.get(),
                    finishInventoryFuture.get(), operationInventoryFuture.get(), transitStockFuture.get(), shippedTodayMap, dto);

        } catch (InterruptedException | ExecutionException e) {
            log.error("异步查询数据失败", e);
            Thread.currentThread().interrupt();
            throw new BusinessException("查询数据失败: " + e.getMessage());
        } catch (CompletionException e) {
            log.error("异步查询数据失败(CompletionException)", e.getCause());
            throw new BusinessException("查询数据失败: " + e.getCause().getMessage());
        } finally {
            log.info("StopWatch统计:\n{}", stopWatch.prettyPrint());

            if (asyncTimings != null && !asyncTimings.isEmpty()) {
                long totalMillis = stopWatch.getTotalTimeMillis();
                if (totalMillis == 0) {
                    totalMillis = 1;
                }

                StringBuilder report = new StringBuilder();
                report.append("异步任务耗时报告:\n");
                report.append("-------------------------------------------------\n");
                report.append(String.format("%-12s %-7s %s\n", "ms", "%", "Task name"));
                report.append("-------------------------------------------------\n");

                List<Map.Entry<String, Long>> sortedTimings = new ArrayList<>(asyncTimings.entrySet());
                sortedTimings.sort(Map.Entry.comparingByKey());

                for (Map.Entry<String, Long> entry : sortedTimings) {
                    long taskMillis = entry.getValue();
                    long percentage = Math.round(100.0 * taskMillis / totalMillis);
                    report.append(String.format("%-12d %-7s %s\n", taskMillis, percentage + "%", entry.getKey()));
                }
                report.append("-------------------------------------------------\n");
                log.info(report.toString());
            }
        }
    }
    /**
     * 获取产品编码 - 全量查询
     */
    private List<String> getQueryProductCodes(DemandDeliveryProductionDetailDTO dto) {

        // 1. 先从发货计划中获取特定时间范围内的所有产品编码
        Date planStartTime = DateUtils.getCurrentDateTruncateTime();
        Date planEndTime = DateUtils.moveDay(planStartTime, 14);

        Map<String, Object> queryParams = new HashMap<>();

        if (StringUtils.isNotEmpty(dto.getProductCode())) {
            queryParams.put("productCodeLike", StringConvertUtils.convertToLike(dto.getProductCode()));
        }

        if (StringUtils.isNotEmpty(dto.getVehicleModelCode())) {
            queryParams.put("vehicleModelCodeLike", StringConvertUtils.convertToLike(dto.getVehicleModelCode()));
        }
        queryParams.put("startTimeStrYMD", DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR3));
        queryParams.put("endTimeStrYMD", DateUtils.dateToString(planEndTime, DateUtils.COMMON_DATE_STR3));
        return deliveryPlanPublishedDao.selectDistinctProductCodeByParams(queryParams);
    }


    /**
     * 获取发货计划数据 - 只查询当前页面需要的产品
     */
    private List<DeliveryPlanPublishedVO> getDeliveryPlanData(List<String> queryProductCodes, Date planStartTime, Date planEndTime) {
        if (CollectionUtils.isEmpty(queryProductCodes)) {
            return new ArrayList<>();
        }

        return deliveryPlanPublishedDao.selectVOByParams(ImmutableMap.of(
                "productCodes", queryProductCodes,
                "startTimeStrYMD", DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR3),
                "endTimeStrYMD", DateUtils.dateToString(planEndTime, DateUtils.COMMON_DATE_STR3)
        ));
    }

    /**
     * 获取主生产计划数据 - 只查询当前页面需要的产品
     */
    private List<MasterPlanTaskVO> getMasterPlanData(String scenario, List<String> queryProductCodes, Date planStartTime, Date planEndTime) {
        if (CollectionUtils.isEmpty(queryProductCodes)) {
            return new ArrayList<>();
        }

        PlanningHorizonVO planningHorizon = newMdsFeign.selectPlanningHorizon(scenario);
        MasterPlanReq masterPlanReq = new MasterPlanReq();

        Date historyRetrospectStartTime = planningHorizon.getHistoryRetrospectStartTime();
        masterPlanReq.setHistoryRetrospectStartTime(historyRetrospectStartTime);
        masterPlanReq.setProductCodes(queryProductCodes);

        Date startTime = DateUtils.stringToDate(DateUtils.dateToString(planStartTime)
                        + " " + DateUtils.dateToString(planningHorizon.getPlanStartTime(), "HH:mm:ss"),
                DateUtils.COMMON_DATE_STR1);
        Date endTime = DateUtils.stringToDate(DateUtils.dateToString(planEndTime)
                        + " " + DateUtils.dateToString(planningHorizon.getPlanEndTime(), "HH:mm:ss"),
                DateUtils.COMMON_DATE_STR1);
        masterPlanReq.setDeliverStartTime(startTime);
        masterPlanReq.setDeliverEndTime(endTime);

        return mpsFeign.selectByMasterReq(scenario, masterPlanReq);
    }

    /**
     * 获取库存数据 - 只查询当前页面需要的产品
     */
    private Map<String, List<InventoryBatchDetailVO>> getInventoryData(List<String> queryProductCodes) {
        if (CollectionUtils.isEmpty(queryProductCodes)) {
            return new HashMap<>();
        }
        List<InventoryBatchDetailVO> inventoryBatchDetails =inventoryBatchDetailDao.selectByProductCodes(queryProductCodes, StockPointTypeEnum.BC.getCode());

        return inventoryBatchDetails.stream().collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));
    }
    /**
     * 获取并组装中转库存
     *
     * @param oemCodes 主机厂列表
     * @param productCodes 物料列表
     * @return java.util.Map<java.lang.String, java.math.BigDecimal>
     */
    public Map<String, BigDecimal> getTransitStockMap(List<String> oemCodes, List<String> productCodes) {
        /*
         * 新逻辑：根据主机厂编码和物料编码取fdp_inventory_shift表modify_time最新且planned_date=当前日期（只需要到天的维度就可以了）的第一条数据的opening_inventory
         */
        // 查询最新的期初库存数据
        List<InventoryShiftVO> latestOpeningInventoryList = inventoryShiftService.selectLatestOpeningInventoryByOemAndProduct(
                ImmutableMap.of("oemCodes", oemCodes, "productCodes", productCodes));

        Map<String, BigDecimal> transitStockMap = new HashMap<>();

        // 按产品编码分组并汇总期初库存
        Map<String, List<InventoryShiftVO>> inventoryByProduct = latestOpeningInventoryList.stream()
                .collect(Collectors.groupingBy(InventoryShiftVO::getProductCode));

        for (String productCode : productCodes) {
            List<InventoryShiftVO> inventoryList = inventoryByProduct.get(productCode);
            if (CollectionUtils.isNotEmpty(inventoryList)) {
                BigDecimal totalOpeningInventory = inventoryList.stream()
                        .map(vo -> vo.getOpeningInventory() != null ? new BigDecimal(vo.getOpeningInventory()) : BigDecimal.ZERO)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                transitStockMap.put(productCode, totalOpeningInventory);
            } else {
                transitStockMap.put(productCode, BigDecimal.ZERO);
            }
        }

        return transitStockMap;
    }

    /**
     * 获取并组装在途库存
     *
     * @param oemCodes 主机厂列表
     * @param productCodes 物料列表
     * @return java.util.Map<java.lang.String,java.math.BigDecimal> key为产品编码, value为在途库存数量
     */
    public Map<String, BigDecimal> getTransportingQtyMap(List<String> oemCodes, List<String> productCodes) {
        /*
         * 新逻辑：根据主机厂编码和物料编码取fdp_inventory_shift表最新修改的同一个版本（version_id）的receive汇总
         */
        // 查询最新版本的receive汇总数据
        List<InventoryShiftVO> latestReceiveSummaryList = inventoryShiftService.selectLatestReceiveSummaryByVersion(
                ImmutableMap.of("oemCodes", oemCodes, "productCodes", productCodes));

        Map<String, BigDecimal> transportingQtyMap = new HashMap<>();

        // 按产品编码分组并汇总receive数量
        Map<String, List<InventoryShiftVO>> receiveByProduct = latestReceiveSummaryList.stream()
                .collect(Collectors.groupingBy(InventoryShiftVO::getProductCode));

        for (String productCode : productCodes) {
            List<InventoryShiftVO> receiveList = receiveByProduct.get(productCode);
            if (CollectionUtils.isNotEmpty(receiveList)) {
                BigDecimal totalReceive = receiveList.stream()
                        .map(vo -> vo.getReceive() != null ? new BigDecimal(vo.getReceive()) : BigDecimal.ZERO)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                transportingQtyMap.put(productCode, totalReceive);
            } else {
                transportingQtyMap.put(productCode, BigDecimal.ZERO);
            }
        }

        return transportingQtyMap;
    }

    /**
     * 获取装载需求数据
     */
    private Map<String, List<LoadingDemandSubmissionDetailVO>> getLoadingDemandData(Date planStartTime, Date planEndTime) {
        String versionId = originDemandVersionService.selectLatestVersionId();
        if (Objects.isNull(versionId)) {
            log.info("原始需求版本不存在");
            return new HashMap<>();
        }

        List<LoadingDemandSubmissionDetailVO> submissionDetailVOS = loadingDemandSubmissionDetailService
                .selectVOByParams(ImmutableMap.of(
                        "versionId", versionId,
                        "startTimeStr", DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR1),
                        "endTimeStr", DateUtils.dateToString(planEndTime, DateUtils.COMMON_DATE_STR1)
                ));

        return CollectionUtils.isEmpty(submissionDetailVOS) ? new HashMap<>() :
                submissionDetailVOS.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getProductCode));
    }

    /**
     * 处理报表数据
     */
    private List<DemandDeliveryProductionVO> processReportData(List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS,
                                                               List<MasterPlanTaskVO> operationTasks,
                                                               Map<String, List<LoadingDemandSubmissionDetailVO>> submissionDetailMap,
                                                               List<Date> intervalDates,
                                                               Map<String, BigDecimal> transportingQtyMap,
                                                               Map<String, String> productStockMap,
                                                               Map<String, List<SubInventoryCargoLocationVO>> cargoLocationMap,
                                                               Map<String, List<InventoryBatchDetailVO>> finishInventoryMap,
                                                               Map<String, List<InventoryBatchDetailVO>> operationInventoryMap,
                                                               Map<String, BigDecimal> transitStockMap,
                                                               Map<String, BigDecimal> shippedTodayMap,
                                                               DemandDeliveryProductionDetailDTO dto) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("processReportData");

        // 1. 预处理基本数据
        Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanMap = deliveryPlanPublishedVOS.stream()
                .collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));
        Set<String> productCodeSets = deliveryPlanMap.keySet();
        List<DemandDeliveryProductionVO> dataList = productCodeSets.parallelStream()
                .map(productCode -> processProductData(
                        productCode, deliveryPlanMap, submissionDetailMap, finishInventoryMap, operationTasks, productStockMap,
                        cargoLocationMap, operationInventoryMap, intervalDates, transitStockMap, transportingQtyMap))
                .filter(vo -> {
                    // 检查是否有发货计划数量
                    boolean hasDeliveryPlan = vo.getDynamicData().stream()
                            .flatMap(map -> map.values().stream())
                            .anyMatch(qty -> new BigDecimal(String.valueOf(qty)).compareTo(BigDecimal.ZERO) > 0);

                    // 检查是否有装车计划数量
                    boolean hasLoadingPlan = vo.getLoadingDynamicData().stream()
                            .flatMap(map -> map.values().stream())
                            .anyMatch(qty -> new BigDecimal(String.valueOf(qty)).compareTo(BigDecimal.ZERO) > 0);

                    // 检查是否有库存
                    boolean hasInventory =
                            (vo.getBohStock() != null && vo.getBohStock().compareTo(BigDecimal.ZERO) > 0) ||
                                    (vo.getTransportingQty() != null && vo.getTransportingQty().compareTo(BigDecimal.ZERO) > 0) ||
                                    (vo.getFgStock() != null && vo.getFgStock().compareTo(BigDecimal.ZERO) > 0) ||
                                    (vo.getAfterShape() != null && vo.getAfterShape().compareTo(BigDecimal.ZERO) > 0) ||
                                    (vo.getAfterPacking() != null && vo.getAfterPacking().compareTo(BigDecimal.ZERO) > 0) ||
                                    (vo.getAfterLamination() != null && vo.getAfterLamination().compareTo(BigDecimal.ZERO) > 0);

                    // 如果一个产品既没有发货计划，也没有装车计划，没有库存，则过滤掉
                    return hasDeliveryPlan || hasLoadingPlan || hasInventory;
                })
                .collect(Collectors.toList());

        dataList.forEach(vo -> {
            String productCode = vo.getProductCode();

            // 获取库存
            BigDecimal fgStock = Optional.ofNullable(vo.getFgStock()).orElse(BigDecimal.ZERO);
            BigDecimal shippedToday = shippedTodayMap.getOrDefault(productCode, BigDecimal.ZERO);
            // 工厂包装后库存
            BigDecimal afterPackingStock = Optional.ofNullable(vo.getAfterPacking()).orElse(BigDecimal.ZERO);
            // 工厂成型后半品库存
            BigDecimal formingSemiFinishedStock =
                    Optional.ofNullable(vo.getAfterPacking()).orElse(BigDecimal.ZERO)
                            .add(Optional.ofNullable(vo.getPacking()).orElse(BigDecimal.ZERO))
                            .add(Optional.ofNullable(vo.getHighVoltagePackaging()).orElse(BigDecimal.ZERO))
                            .add(Optional.ofNullable(vo.getLamination()).orElse(BigDecimal.ZERO))
                            .add(Optional.ofNullable(vo.getShapeLamination()).orElse(BigDecimal.ZERO));

            // 获取发货计划数量
            List<DeliveryPlanPublishedVO> plans = deliveryPlanMap.get(productCode);
            Map<String, Integer> dailyPlanMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(plans)) {
                dailyPlanMap = plans.stream().collect(Collectors.toMap(
                        p -> DateUtils.dateToString(p.getDemandTime(), DateUtils.COMMON_DATE_STR3),
                        DeliveryPlanPublishedVO::getDemandQuantity,
                        Integer::sum
                ));
            }

            String todayStr = DateUtils.dateToString(intervalDates.get(0), DateUtils.COMMON_DATE_STR3);
            String tomorrowStr = DateUtils.dateToString(intervalDates.get(1), DateUtils.COMMON_DATE_STR3);
            String dayAfterTomorrowStr = DateUtils.dateToString(intervalDates.get(2), DateUtils.COMMON_DATE_STR3);

            BigDecimal planToday = new BigDecimal(dailyPlanMap.getOrDefault(todayStr, 0));
            BigDecimal planTodayAndTomorrow = planToday.add(new BigDecimal(dailyPlanMap.getOrDefault(tomorrowStr, 0)));
            BigDecimal plan3Days = planTodayAndTomorrow.add(new BigDecimal(dailyPlanMap.getOrDefault(dayAfterTomorrowStr, 0)));

            // 1. 成品库存+当天已发货 < 当天发货计划数量
            boolean cond1 = fgStock.add(shippedToday).compareTo(planToday) < 0;
            // 2. 成品库存+工厂包装后库存+当天已发货 < 当天+次日发货计划数量
            boolean cond2 = fgStock.add(afterPackingStock).add(shippedToday).compareTo(planTodayAndTomorrow) < 0;
            // 3. 成品库存+工厂成型后半品库存+当天已发货 < 3天累计发货计划数量
            boolean cond3 = fgStock.add(formingSemiFinishedStock).add(shippedToday).compareTo(plan3Days) < 0;

            vo.setInWarehouseWarning(cond1 || cond2 || cond3);
        });

        if (Boolean.TRUE.equals(dto.getInWarehouseWarning())) {
            dataList = dataList.stream()
                    .filter(DemandDeliveryProductionVO::getInWarehouseWarning)
                    .collect(Collectors.toList());
        }

        dataList.sort(Comparator.comparing(DemandDeliveryProductionVO::getVehicleModelCode, Comparator.nullsLast(String::compareTo))
                .thenComparing(DemandDeliveryProductionVO::getProductCode, Comparator.nullsLast(String::compareTo)));

        if (!dataList.isEmpty() && !dataList.get(0).getDetails().isEmpty()) {
            List<DemandDeliveryProductionDetailVO> details = dataList.get(0).getDetails();
            List<String> headerList = details.stream().map(DemandDeliveryProductionDetailVO::getTitleDate).collect(Collectors.toList());
            dataList.get(0).setHeader(headerList);
        }

        stopWatch.stop();
        log.info("processReportData StopWatch统计: {}", stopWatch.prettyPrint());
        return dataList;
    }

    private Map<String, List<InventoryBatchDetailVO>> getFinishInventoryMap(String scenario, Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap) {

        List<String> saleOrganizations = newMdsFeign.selectStockPointByParams(scenario, ImmutableMap
                        .of("stockPointType", StockPointTypeEnum.BC.getCode())).stream()
                .filter(e -> StringUtils.isNotEmpty(e.getOrganizeType())
                        && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(
                scenario, "SUB_INVENTORY", "INTERNAL", null);
        String subInventory = scenarioBusinessRange.getData().getRangeData();

        if (StringUtils.isBlank(subInventory)) {
            log.error("未找到对应的成品子库存信息！");
            throw new BusinessException("未找到对应的成品子库存信息！");
        }
        // 成品
        return inventoryBatchDetailMap.values().stream().flatMap(List::stream)
                .filter(t -> StringUtils.isEmpty(t.getOperationCode())
                        && saleOrganizations.contains(t.getStockPointCode())
                        && subInventory.equals(t.getSubinventory()))
                .collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));
    }

    /**
     * 获取销售组织信息
     */
    private List<String> getSaleOrganizations(String scenario) {
        List<String> saleOrganizations = newMdsFeign.selectStockPointByParams(scenario, ImmutableMap
                        .of("stockPointType", StockPointTypeEnum.BC.getCode())).stream()
                .filter(e -> StringUtils.isNotEmpty(e.getOrganizeType())
                        && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(saleOrganizations)){
            throw new BusinessException("销售组织信息为空");
        }
        return saleOrganizations;
    }

    /**
     * 获取产品库存点映射
     */
    private Map<String, String> getProductStockMap(String scenario, List<String> productCodes) {
        List<RoutingVO> routingVOS = newMdsFeign.selectRoutingByParams(scenario,
                ImmutableMap.of("routingCodes", productCodes));
        return CollectionUtils.isEmpty(routingVOS) ? new HashMap<>() :
                routingVOS.stream()
                        .filter(vo -> vo.getRoutingCode() != null && vo.getStockPointId() != null)
                        .collect(Collectors.toMap(RoutingBasicVO::getRoutingCode,
                                RoutingBasicVO::getStockPointId, (v1, v2) -> v1));
    }
    /**
     * 获取货位映射
     */
    private Map<String, List<SubInventoryCargoLocationVO>> getCargoLocationMap(String scenario) {


        List<SubInventoryCargoLocationVO> subInventoryCargoLocations =
                mpsFeign.queryByParams1(scenario, ImmutableMap.of("operations", Lists.newArrayList(
                        OPERATION_PACKAGING, OPERATION_AFTERPACKING, OPERATION_HIGHVOLTAGEPACKAGING,
                        OPERATION_LAMINATION, OPERATION_SHAPELAMINATION, OPERATION_COATING,
                        OPERATION_PRINTING, OPERATION_PRETREATMENT, OPERATION_NONASSETWAREHOUSE
                )));

        return CollectionUtils.isEmpty(subInventoryCargoLocations) ? new HashMap<>() :
                subInventoryCargoLocations.stream()
                        .filter(vo -> vo.getFreightSpaceCode() != null)
                        .collect(Collectors.groupingBy(SubInventoryCargoLocationVO::getOperation));
    }
    /**
     * 获取标准步骤映射
     */
    private Map<String, String> getStandardStepMap(String scenario) {
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(scenario);
        return standardStepVOS.stream()
                .filter(vo -> vo.getStockPointCode() != null && vo.getStandardStepName() != null && vo.getStandardStepCode() != null)
                .collect(Collectors.toMap(
                        p -> p.getStockPointCode() + p.getStandardStepName(),
                        StandardStepBasicVO::getStandardStepCode, (v1, v2) -> v1));
    }

    /**
     * 获取工序库存映射
     */
    private Map<String, List<InventoryBatchDetailVO>> getOperationInventoryMap(String scenario,
                                                                               Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap) {
        // 查询库存点数据，用于过滤非本厂库存
        List<NewStockPointVO> newStockPoints = newMdsFeign.selectStockPointByParams(scenario,
                ImmutableMap.of(PARAM_STOCK_POINT_TYPE, StockPointTypeEnum.BC.getCode()));

        // 获取本厂生产组织类型的仓库(半成品库存点)
        List<String> productOrganizations = newStockPoints.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getOrganizeType())
                        && StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

        // 工序在制量
        return inventoryBatchDetailMap.values().stream()
                .flatMap(List::stream)
                .filter(t -> productOrganizations.contains(t.getStockPointCode()))
                .collect(Collectors.groupingBy(p -> String.join("-", p.getStockPointCode(),
                        p.getProductCode(), p.getSubinventory(),p.getFreightSpace())));
    }
    /**
     * 处理单个产品的数据
     */
    private DemandDeliveryProductionVO processProductData(String productCode,
                                                          Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanMap,
                                                          Map<String, List<LoadingDemandSubmissionDetailVO>> submissionDetailMap,
                                                          Map<String, List<InventoryBatchDetailVO>> fgInventoryBatchDetailMap,
                                                          List<MasterPlanTaskVO> operationTasks,
                                                          Map<String, String> productStockMap,
                                                          Map<String, List<SubInventoryCargoLocationVO>> cargoLocationMap,
                                                          Map<String, List<InventoryBatchDetailVO>> operationInventoryMap,
                                                          List<Date> intervalDates,
                                                          Map<String, BigDecimal> transitStockMap,
                                                          Map<String, BigDecimal> transportingQtyMap) {

        DemandDeliveryProductionVO demandDeliveryProductionVO = new DemandDeliveryProductionVO();
        List<DemandDeliveryProductionDetailVO> details = new ArrayList<>();

        // 处理发货计划动态数据
        List<Map<String, Object>> dynamicData = processDynamicData(deliveryPlanMap.get(productCode), intervalDates);

        // 处理装车需求动态数据
        List<Map<String, Object>> loadingDynamicData = processLoadingDynamicData(submissionDetailMap.get(productCode), intervalDates);

        // 处理库存数据
        processInventoryData(demandDeliveryProductionVO,productCode, transitStockMap,transportingQtyMap);

        // 处理工序库存数据
        processOperationInventoryData(demandDeliveryProductionVO, productCode, productStockMap,
                operationInventoryMap, cargoLocationMap);

        // 处理成品库存数据
        processFinishedGoodsInventory(demandDeliveryProductionVO, productCode, fgInventoryBatchDetailMap);

        // 处理生产计划数据
        processProductionPlanData(demandDeliveryProductionVO, productCode, operationTasks);

        // 设置基本信息
        demandDeliveryProductionVO.setDynamicData(dynamicData);
        demandDeliveryProductionVO.setLoadingDynamicData(loadingDynamicData);
        demandDeliveryProductionVO.setProductCode(productCode);
        demandDeliveryProductionVO.setVehicleModelCode(deliveryPlanMap.get(productCode).get(0).getVehicleModelCode());
        // 处理详情数据
        processDetailData(details, dynamicData, loadingDynamicData, intervalDates, demandDeliveryProductionVO);
        demandDeliveryProductionVO.setDetails(details);

        return demandDeliveryProductionVO;
    }

    /**
     * 处理发货计划动态数据
     */
    private List<Map<String, Object>> processDynamicData(List<DeliveryPlanPublishedVO> deliveryPlanList, List<Date> intervalDates) {
        List<Map<String, Object>> dynamicData = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(deliveryPlanList)) {
            Map<String, Integer> deliveryMap = deliveryPlanList.stream()
                    .collect(Collectors.toMap(
                            o -> DateUtils.dateToString(o.getDemandTime(), DateUtils.COMMON_DATE_STR3),
                            DeliveryPlanPublishedVO::getDemandQuantity,
                            Integer::sum
                    ));

            for (Date intervalDate : intervalDates) {
                Map<String, Object> dataMap = new HashMap<>();
                String deliveryDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                dataMap.put(deliveryDate, deliveryMap.getOrDefault(deliveryDate, 0));
                dynamicData.add(dataMap);
            }
        } else {
            for (Date intervalDate : intervalDates) {
                Map<String, Object> dataMap = new HashMap<>();
                String deliveryDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                dataMap.put(deliveryDate, 0);
                dynamicData.add(dataMap);
            }
        }

        return dynamicData;
    }

    /**
     * 处理装车需求动态数据
     */
    private List<Map<String, Object>> processLoadingDynamicData(List<LoadingDemandSubmissionDetailVO> loadingDemandList, List<Date> intervalDates) {
        List<Map<String, Object>> loadingDynamicData = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(loadingDemandList)) {
            Map<String, BigDecimal> submissionMap = loadingDemandList.stream()
                    .filter(o -> o != null && o.getDemandTime() != null)
                    .collect(Collectors.toMap(
                            o -> {
                                Date date = DateUtils.stringToDate(o.getDemandTime());
                                if (date == null) {
                                    throw new IllegalArgumentException("非法 demandTime：" + o.getDemandTime());
                                }
                                return DateUtils.dateToString(date, DateUtils.COMMON_DATE_STR3);
                            },
                            o -> o.getDemandQuantity() != null ? o.getDemandQuantity() : BigDecimal.ZERO,
                            BigDecimal::add
                    ));

            for (Date intervalDate : intervalDates) {
                Map<String, Object> dataMap = new HashMap<>();
                String deliveryDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                dataMap.put(deliveryDate, submissionMap.getOrDefault(deliveryDate, BigDecimal.ZERO));
                loadingDynamicData.add(dataMap);
            }
        }

        return loadingDynamicData;
    }

    /**
     * 处理库存数据
     */
    private void processInventoryData(DemandDeliveryProductionVO demandDeliveryProductionVO,String productCode,Map<String, BigDecimal> transitStockMap,
                                      Map<String, BigDecimal> transportingQtyMap) {
        if(transitStockMap.containsKey(productCode)){
            demandDeliveryProductionVO.setBohStock(transitStockMap.get(productCode));
        }else{
            demandDeliveryProductionVO.setBohStock(BigDecimal.ZERO);
        }
        if(transportingQtyMap.containsKey(productCode)){
            demandDeliveryProductionVO.setTransportingQty(transportingQtyMap.get(productCode));
        }else{
            demandDeliveryProductionVO.setTransportingQty(BigDecimal.ZERO);
        }
    }

    /**
     * 处理工序库存数据
     */
    private void processOperationInventoryData(DemandDeliveryProductionVO demandDeliveryProductionVO, String productCode,
                                               Map<String, String> productStockMap,
                                               Map<String, List<InventoryBatchDetailVO>> operationInventoryMap,
                                               Map<String, List<SubInventoryCargoLocationVO>> cargoLocationMap) {
        if (productStockMap.containsKey(productCode)) {
            String stockPointCode = productStockMap.get(productCode);
            String packing = getInventory(stockPointCode, productCode, OPERATION_PACKAGING, operationInventoryMap, cargoLocationMap);
            String afterPacking = getInventory(stockPointCode, productCode, OPERATION_AFTERPACKING, operationInventoryMap, cargoLocationMap);
            String highVoltagePackaging = getInventory(stockPointCode, productCode, OPERATION_HIGHVOLTAGEPACKAGING, operationInventoryMap, cargoLocationMap);
            String lamination = getInventory(stockPointCode, productCode, OPERATION_LAMINATION, operationInventoryMap, cargoLocationMap);
            String shapeLaination = getInventory(stockPointCode, productCode, OPERATION_SHAPELAMINATION, operationInventoryMap, cargoLocationMap);
            String coating = getInventory(stockPointCode, productCode, OPERATION_COATING, operationInventoryMap, cargoLocationMap);
            String printing = getInventory(stockPointCode, productCode, OPERATION_PRINTING, operationInventoryMap, cargoLocationMap);
            String pretreatment = getInventory(stockPointCode, productCode, OPERATION_PRETREATMENT, operationInventoryMap, cargoLocationMap);
            String nonAssetWarehouse = getInventory(stockPointCode, productCode, OPERATION_NONASSETWAREHOUSE, operationInventoryMap, cargoLocationMap);
            demandDeliveryProductionVO.setPacking(new BigDecimal(packing));
            demandDeliveryProductionVO.setAfterPacking(new BigDecimal(afterPacking));
            demandDeliveryProductionVO.setHighVoltagePackaging(new BigDecimal(highVoltagePackaging));
            demandDeliveryProductionVO.setLamination(new BigDecimal(lamination));
            demandDeliveryProductionVO.setShapeLamination(new BigDecimal(shapeLaination));
            demandDeliveryProductionVO.setCoating(new BigDecimal(coating));
            demandDeliveryProductionVO.setPrinting(new BigDecimal(printing));
            demandDeliveryProductionVO.setPretreatment(new BigDecimal(pretreatment));
            demandDeliveryProductionVO.setNonAssetWarehouse(new BigDecimal(nonAssetWarehouse));
        } else {
            demandDeliveryProductionVO.setPacking(BigDecimal.ZERO);
            demandDeliveryProductionVO.setAfterPacking(BigDecimal.ZERO);
            demandDeliveryProductionVO.setHighVoltagePackaging(BigDecimal.ZERO);
            demandDeliveryProductionVO.setLamination(BigDecimal.ZERO);
            demandDeliveryProductionVO.setShapeLamination(BigDecimal.ZERO);
            demandDeliveryProductionVO.setCoating(BigDecimal.ZERO);
            demandDeliveryProductionVO.setPrinting(BigDecimal.ZERO);
            demandDeliveryProductionVO.setPretreatment(BigDecimal.ZERO);
            demandDeliveryProductionVO.setNonAssetWarehouse(BigDecimal.ZERO);

        }
    }

    /**
     * 处理成品库存数据
     */
    private void processFinishedGoodsInventory(DemandDeliveryProductionVO demandDeliveryProductionVO, String productCode,
                                               Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap) {
        List<InventoryBatchDetailVO> inventoryBatchDetailVOList = inventoryBatchDetailMap.get(productCode);
        if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOList)) {
            BigDecimal inventoryQuantity = inventoryBatchDetailVOList.stream()
                    .map(t -> Optional.ofNullable(t.getCurrentQuantity())
                            .map(BigDecimal::new)
                            .orElse(BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            demandDeliveryProductionVO.setFgStock(Objects.isNull(inventoryQuantity)?BigDecimal.ZERO:inventoryQuantity);
        }else{
            demandDeliveryProductionVO.setFgStock(BigDecimal.ZERO);
        }
    }

    /**
     * 处理生产计划数据
     */
    private void processProductionPlanData(DemandDeliveryProductionVO demandDeliveryProductionVO, String productCode,
                                           List<MasterPlanTaskVO> operationTasks) {
        Map<String, List<MasterPlanTaskVO>> productTaskGroup = operationTasks.stream()
                .filter(x -> StringUtils.isNotBlank(x.getProductStockPointCode()))
                .collect(Collectors.groupingBy(MasterPlanTaskVO::getProductStockPointCode));

        List<MasterPlanTaskVO> masterPlanTasks = productTaskGroup.getOrDefault(productCode, new ArrayList<>())
                .stream().filter(x -> StringUtils.isNotBlank(x.getStandardStepName())
                        && PACKAGING_OPERATION.equals(x.getStandardStepName()))
                .sorted(Comparator.comparing(MasterPlanTaskVO::getStartTime))
                .collect(Collectors.toList());

        int totalPlanQuantity = masterPlanTasks.stream()
                .filter(x -> Objects.nonNull(x.getOperationVO()) && Objects.nonNull(x.getOperationVO().getQuantity()))
                .map(x -> x.getOperationVO().getQuantity())
                .reduce(BigDecimal.ZERO, BigDecimal::add).intValue();

        demandDeliveryProductionVO.setScheduleQty(BigDecimal.valueOf(totalPlanQuantity));
    }

    /**
     * 处理详情数据 - 累积扣减库存，装车和需求分别处理
     */
    private void processDetailData(List<DemandDeliveryProductionDetailVO> details,
                                   List<Map<String, Object>> dynamicData, List<Map<String, Object>> loadingDynamicData,
                                   List<Date> intervalDates, DemandDeliveryProductionVO demandDeliveryProductionVO) {

        BigDecimal fgStock = Objects.isNull(demandDeliveryProductionVO.getFgStock()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getFgStock();
        BigDecimal bohStock = Objects.isNull(demandDeliveryProductionVO.getBohStock()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getBohStock();
        BigDecimal transportingQty = Objects.isNull(demandDeliveryProductionVO.getTransportingQty()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getTransportingQty();
        BigDecimal packing = Objects.isNull(demandDeliveryProductionVO.getPacking()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getPacking();
        BigDecimal highVoltagePackaging = Objects.isNull(demandDeliveryProductionVO.getHighVoltagePackaging()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getHighVoltagePackaging();
        BigDecimal lamination = Objects.isNull(demandDeliveryProductionVO.getLamination()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getLamination();
        BigDecimal shapeLamination = Objects.isNull(demandDeliveryProductionVO.getShapeLamination()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getShapeLamination();
        BigDecimal afterPacking = Objects.isNull(demandDeliveryProductionVO.getAfterPacking()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getAfterPacking();
        BigDecimal afterLamination = Objects.isNull(demandDeliveryProductionVO.getAfterLamination()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getAfterLamination();
        BigDecimal afterShape = Objects.isNull(demandDeliveryProductionVO.getAfterShape()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getAfterShape();
        BigDecimal scheduleQty = Objects.isNull(demandDeliveryProductionVO.getScheduleQty()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getScheduleQty();

        BigDecimal producingQty = afterPacking.add(afterLamination).add(afterShape).add(scheduleQty);
        BigDecimal transferAndTransportQty = bohStock.add(transportingQty);

        // 累积扣减变量：用于计算每日累积消耗后的剩余库存
        final BigDecimal[] beforeLoadingQty = {BigDecimal.ZERO};
        final BigDecimal[] beforeDemandQty = {BigDecimal.ZERO};

        for (Date intervalDate : intervalDates) {
            String titleDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
            DemandDeliveryProductionDetailVO detail = new DemandDeliveryProductionDetailVO();

            // 处理装车数量和颜色
            loadingDynamicData.stream().filter(o -> o.containsKey(titleDate)).findFirst().ifPresent(t -> {
                BigDecimal loadingQty = new BigDecimal(String.valueOf(t.get(titleDate)));
                detail.setLoadingQty(loadingQty);

                // 装车数量颜色判断（按照新的颜色方案）
                // 计算累积扣减后的可用库存
                BigDecimal availableTransferStock = bohStock.subtract(beforeLoadingQty[0]); // 剩余中转库存
                BigDecimal availableTransferAndTransport = bohStock.add(transportingQty).subtract(beforeLoadingQty[0]); // 剩余中转库+在途

                if (availableTransferStock.compareTo(loadingQty) >= 0) {
                    detail.setLoadingColor("1"); // 绿色：中转库>=装车计划
                } else if (availableTransferAndTransport.compareTo(loadingQty) >= 0) {
                    detail.setLoadingColor("2"); // 浅绿色：(中转库+在途)>=装车计划
                } else if(availableTransferAndTransport.compareTo(loadingQty) < 0){
                    detail.setLoadingColor("3"); // 橙色：(中转库+在途)<装车计划
                }
                // 装车数量的累积扣减
                if (loadingQty.compareTo(BigDecimal.ZERO) > 0) {
                    beforeLoadingQty[0] = beforeLoadingQty[0].add(loadingQty);

                }
            });

            // 处理需求数量和颜色
            dynamicData.stream().filter(o -> o.containsKey(titleDate)).findFirst().ifPresent(t -> {
                BigDecimal demandQty = new BigDecimal(String.valueOf(t.get(titleDate)));
                detail.setDemandQty(demandQty);

                // 需求数量颜色判断（按照新的颜色方案）
                // 计算累积扣减后的可用库存
                BigDecimal availableFgStock = fgStock.subtract(beforeDemandQty[0]); // 剩余仓库库存
                // 成型后半品 = 包装后+包装+高压~包装+合片+成形~合片)
                BigDecimal semiFinishedStock = afterPacking.add(packing).add(highVoltagePackaging).add(lamination).add(shapeLamination);
                BigDecimal availableFgAndSemiFinished = fgStock.add(semiFinishedStock).subtract(beforeDemandQty[0]); // 剩余仓库+成型后半品

                if (availableFgStock.compareTo(demandQty) >= 0) {
                    detail.setDemandColor("1"); // 青色：仓库>=发货计划
                } else if (availableFgAndSemiFinished.compareTo(demandQty) >= 0) {
                    detail.setDemandColor("2"); // 蓝色：仓库+成型后半品>=发货计划
                } else if(availableFgAndSemiFinished.compareTo(demandQty) < 0){
                    detail.setDemandColor("3"); // 橙色：仓库+成型后半品<发货计划
                }

                // 需求数量的累积扣减
                if (demandQty.compareTo(BigDecimal.ZERO) > 0) {
                    beforeDemandQty[0] = beforeDemandQty[0].add(demandQty);
                }
            });

            detail.setTitleDate(titleDate);
            details.add(detail);
        }
    }

    /**
     * 获取指定工序的库存数量
     *
     * @param stockPointCode 库存点编码
     * @param productCode 产品编码
     * @param operationCode 工序编码
     * @param inventoryMap 库存映射
     * @param cargoLocationMap 货位映射
     * @return 库存数量字符串
     */
    private String getInventory(String stockPointCode, String productCode, String operationCode,
                                Map<String, List<InventoryBatchDetailVO>> inventoryMap,
                                Map<String, List<SubInventoryCargoLocationVO>> cargoLocationMap) {
        if (StringUtils.isBlank(productCode) || StringUtils.isBlank(operationCode)) {
            return "0";
        }

        // 从inventoryMap中获取指定产品的库存数据
        List<InventoryBatchDetailVO> productInventoryList = inventoryMap.values().stream()
                .flatMap(List::stream)
                .filter(inventory -> productCode.equals(inventory.getProductCode())
                        && stockPointCode.equals(inventory.getStockPointCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(productInventoryList)) {
            return "0";
        }

        // 根据工序编码和货位信息过滤库存
        List<InventoryBatchDetailVO> filteredInventoryList = productInventoryList.stream()
                .filter(inventory -> {
                    String freightSpace = inventory.getFreightSpace();
                    String subinventory = inventory.getSubinventory();
                    List<SubInventoryCargoLocationVO> subInventoryCargoLocationVOs = cargoLocationMap.get(operationCode);
                    if (CollectionUtils.isEmpty(subInventoryCargoLocationVOs)) {
                        return false;
                    }

                    // 检查是否匹配指定的工序
                    return subInventoryCargoLocationVOs.stream().anyMatch(vo ->
                            vo.getEnabled().equals(YesOrNoEnum.YES.getCode()) &&
                                    vo.getCorporationCode().equals(stockPointCode) &&
                                    vo.getStashCode().equals(subinventory) &&
                                    vo.getFreightSpaceCode().equals(freightSpace));
                })
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredInventoryList)) {
            return "0";
        }

        // 计算总库存数量
        OptionalDouble sum = filteredInventoryList.stream()
                .mapToDouble(inventory -> {
                    try {
                        return Double.parseDouble(inventory.getCurrentQuantity());
                    } catch (NumberFormatException e) {
                        log.warn("库存数量格式错误: productCode={}, currentQuantity={}",
                                inventory.getProductCode(), inventory.getCurrentQuantity());
                        return 0.0;
                    }
                })
                .reduce(Double::sum);

        double totalQuantity = sum.orElse(0);
        return String.valueOf(totalQuantity);
    }

    @Override
    public List<DemandDeliveryProductionVO> queryDemandDeliveryProductionReportWithoutPagination(DemandDeliveryProductionDetailDTO dto) {
        // 全量查询
        return queryDemandDeliveryProductionReport(dto);
    }
    private Map<String, List<String>> createOemProductMap(List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS) {
        if (CollectionUtils.isEmpty(deliveryPlanPublishedVOS)) {
            return new HashMap<>();
        }
        return deliveryPlanPublishedVOS.stream()
                .filter(vo -> vo.getOemCode() != null && vo.getProductCode() != null)
                .collect(Collectors.groupingBy(
                        DeliveryPlanPublishedVO::getOemCode,
                        Collectors.mapping(
                                DeliveryPlanPublishedVO::getProductCode,
                                Collectors.collectingAndThen(Collectors.toSet(), ArrayList::new)
                        )
                ));
    }

    /**
     * 获取当天已发货数据
     * @return Map<String, BigDecimal> key:产品编码 value:已发货数量
     */
    private Map<String, BigDecimal> getShippedTodayData(String scenario) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 7);
        cal.set(Calendar.MINUTE, 30);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date startTime = cal.getTime();

        BaseResponse<ScenarioBusinessRangeVO> plantCodeResponse = ipsNewFeign.getScenarioBusinessRange(scenario, "SALE_ORGANIZATION", "INTERNAL", null);
        if (plantCodeResponse == null || plantCodeResponse.getData() == null || StringUtils.isBlank(plantCodeResponse.getData().getRangeData())) {
            throw new BusinessException("未找到对应的销售组织信息！");
        }
        String saleOrganization = plantCodeResponse.getData().getRangeData();

        BaseResponse<ScenarioBusinessRangeVO> warehouseResponse = ipsNewFeign.getScenarioBusinessRange(scenario, "SUB_INVENTORY", "INTERNAL", null);
        if (warehouseResponse == null || warehouseResponse.getData() == null || StringUtils.isBlank(warehouseResponse.getData().getRangeData())) {
            throw new BusinessException("未找到对应的成品库存信息！");
        }
        String subInventory = warehouseResponse.getData().getRangeData();

        // 查询 fdp_warehouse_release_record 表
        CompletableFuture<List<WarehouseReleaseRecordVO>> releaseRecordFuture = CompletableFuture.supplyAsync(() ->
                warehouseReleaseRecordService.selectByParams(ImmutableMap.of(
                        "plantCode", saleOrganization,
                        "attribute1", subInventory,
                        "creationDateAfter", startTime
                )), SHARED_EXECUTOR);

        // 查询 fdp_warehouse_release_to_warehouse 表
        CompletableFuture<List<WarehouseReleaseToWarehouseVO>> releaseToWarehouseFuture = CompletableFuture.supplyAsync(() ->
                warehouseReleaseToWarehouseService.selectByParams(ImmutableMap.of(
                        "plantCode", saleOrganization,
                        "attribute1", subInventory,
                        "creationDateAfter", startTime
                )), SHARED_EXECUTOR);

        CompletableFuture.allOf(releaseRecordFuture, releaseToWarehouseFuture).join();

        try {
            Map<String, BigDecimal> shippedTodayMap = new ConcurrentHashMap<>();

            // 处理第一张表的结果
            List<WarehouseReleaseRecordVO> records = releaseRecordFuture.get();
            if (CollectionUtils.isNotEmpty(records)) {
                records.parallelStream()
                        .filter(r -> r.getItemCode() != null && r.getSumQty() != null)
                        .forEach(r -> shippedTodayMap.merge(r.getItemCode(), r.getSumQty(), BigDecimal::add));
            }

            // 处理第二张表的结果
            List<WarehouseReleaseToWarehouseVO> toWarehouseRecords = releaseToWarehouseFuture.get();
            if (CollectionUtils.isNotEmpty(toWarehouseRecords)) {
                toWarehouseRecords.parallelStream()
                        .filter(r -> r.getItemCode() != null && r.getSumQty() != null)
                        .forEach(r -> shippedTodayMap.merge(r.getItemCode(), r.getSumQty(), BigDecimal::add));
            }

            return shippedTodayMap;
        } catch (InterruptedException | ExecutionException e) {
            log.error("异步获取当天已发货数据时发生异常", e);
            Thread.currentThread().interrupt();
            throw new BusinessException("获取当天已发货数据失败", e);
        }
    }

    @SneakyThrows
    @Override
    public void exportData(DemandDeliveryProductionDetailDTO dto, HttpServletResponse response) {
        // 查询数据
        List<DemandDeliveryProductionVO> dataList = queryDemandDeliveryProductionReportWithoutPagination(dto);

        if (CollectionUtils.isEmpty(dataList)) {
            // 如果没有数据，导出空表格
            exportEmptyData(response);
            return;
        }

        // 获取动态表头
        List<String> dynamicHeaders = dataList.get(0).getHeader();

        // 初始化表头
        List<List<String>> headers = initExportHeaders(dynamicHeaders);

        // 组装数据和颜色映射
        List<List<Object>> excelDataList = new ArrayList<>();
        Map<String, Short> cellColorMap = new HashMap<>();
        List<CellRangeAddress> mergeRegions = new ArrayList<>();

        assembleExportData(dataList, dynamicHeaders, excelDataList, cellColorMap, mergeRegions);

        // 设置响应头
        EasyExcelUtil.initResponse(response, "需求发货生产报表");

        // 创建样式处理器
        SheetStyleHandler handler = new SheetStyleHandler(null, null, cellColorMap, null);

        // 添加条件样式
        handler.addCellColorCondition((cell, rowIdx, colIdx) ->
                cellColorMap.containsKey(rowIdx + Constants.DELIMITER + colIdx)
        );

        // 写入Excel，添加合并单元格处理器
        EasyExcelFactory.write(response.getOutputStream())
                .head(headers)
                .registerWriteHandler(handler)
                .registerWriteHandler(new RangeMergeStrategy(mergeRegions))
                .sheet("需求发货生产报表")
                .doWrite(excelDataList);
    }

    /**
     * 初始化导出表头
     */
    private List<List<String>> initExportHeaders(List<String> dynamicHeaders) {
        List<List<String>> headers = new ArrayList<>();

        // 固定列
        headers.add(Lists.newArrayList("内部车型"));
        headers.add(Lists.newArrayList("产品编码"));
        headers.add(Lists.newArrayList("中转库存"));
        headers.add(Lists.newArrayList("在途"));
        headers.add(Lists.newArrayList("仓库成品"));
        headers.add(Lists.newArrayList("包装"));
        headers.add(Lists.newArrayList("包装后"));
        headers.add(Lists.newArrayList("高压-包装"));
        headers.add(Lists.newArrayList("合片"));
        headers.add(Lists.newArrayList("成形-合片"));
        headers.add(Lists.newArrayList("镀膜"));
        headers.add(Lists.newArrayList("印刷"));
        headers.add(Lists.newArrayList("预处理"));
        headers.add(Lists.newArrayList("非资产库"));
        headers.add(Lists.newArrayList("质量封存"));
        headers.add(Lists.newArrayList("已排产量"));
        headers.add(Lists.newArrayList("类别"));

        // 动态列
        if (CollectionUtils.isNotEmpty(dynamicHeaders)) {
            for (String header : dynamicHeaders) {
                headers.add(Lists.newArrayList(header));
            }
        }

        return headers;
    }

    /**
     * 组装导出数据和颜色映射
     */
    private void assembleExportData(List<DemandDeliveryProductionVO> dataList, List<String> dynamicHeaders,
                                    List<List<Object>> excelDataList, Map<String, Short> cellColorMap,
                                    List<CellRangeAddress> mergeRegions) {

        // 颜色映射
        // 装车计划颜色
        Map<String, Short> loadingColorMapping = ImmutableMap.<String, Short>builder()
                .put("1", IndexedColors.GREEN.getIndex())           // 绿色
                .put("2", IndexedColors.LIGHT_GREEN.getIndex())     // 浅绿色
                .put("3", IndexedColors.ORANGE.getIndex())          // 橙色 (对应 #FFC000)
                .build();

        // 发货计划颜色
        Map<String, Short> demandColorMapping = ImmutableMap.<String, Short>builder()
                .put("1", IndexedColors.GREEN.getIndex())           // 绿色
                .put("2", IndexedColors.AQUA.getIndex())            // 青色(对应 #01A9DE)
                .put("3", IndexedColors.ORANGE.getIndex())          // 橙色 (对应 #FF8000)
                .build();

        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        int rowIndex = 1; // Excel行从1开始（0是表头）
        int vehicleModelMergeStartRow = 1; // 合并“内部车型”的起始行

        for (int i = 0; i < dataList.size(); i++) {
            DemandDeliveryProductionVO vo = dataList.get(i);

            // 处理装车计划行
            List<Object> loadingRow = createRowData(vo, "装车计划", dynamicHeaders, true);
            excelDataList.add(loadingRow);
            putLoadingCellColors(cellColorMap, vo, dynamicHeaders, rowIndex, loadingColorMapping);

            // 处理发货计划行
            List<Object> demandRow = createRowData(vo, "发货计划", dynamicHeaders, false);
            excelDataList.add(demandRow);
            putDemandCellColors(cellColorMap, vo, dynamicHeaders, rowIndex + 1, demandColorMapping);

            // 每个产品自身的产品编码列合并两行
            mergeRegions.add(new CellRangeAddress(rowIndex, rowIndex + 1, 1, 1));

            // 判断是否需要合并“内部车型”列
            // 如果是最后一条数据，或者下一条数据的内部车型不同，则进行合并
            boolean isLastItem = (i == dataList.size() - 1);
            if (isLastItem || !Objects.equals(vo.getVehicleModelCode(), dataList.get(i + 1).getVehicleModelCode())) {
                // 如果合并区域跨越多行，则合并
                if ((rowIndex + 1) > vehicleModelMergeStartRow) {
                    mergeRegions.add(new CellRangeAddress(vehicleModelMergeStartRow, rowIndex + 1, 0, 0));
                }
                // 为下一个不同的内部车型重置起始行
                vehicleModelMergeStartRow = rowIndex + 2;
            }

            rowIndex += 2; // 每个产品占用两行，行索引增加2
        }
    }

    /**
     * 创建行数据
     */
    private List<Object> createRowData(DemandDeliveryProductionVO vo, String category,
                                       List<String> dynamicHeaders, boolean isLoading) {
        List<Object> rowData = new ArrayList<>();

        // 固定列数据
        rowData.add(vo.getVehicleModelCode());
        rowData.add(vo.getProductCode());

        // 根据类别显示不同的库存数据
        if ("装车计划".equals(category)) {
            rowData.add(vo.getBohStock() != null ? vo.getBohStock() : BigDecimal.ZERO);
            rowData.add(vo.getTransportingQty() != null ? vo.getTransportingQty() : BigDecimal.ZERO);
            rowData.add(""); // 仓库成品 - 装车计划不显示
        } else {
            rowData.add(""); // 中转库存 - 发货计划不显示
            rowData.add(""); // 在途 - 发货计划不显示
            rowData.add(vo.getFgStock() != null ? vo.getFgStock() : BigDecimal.ZERO);
        }

        // 工序库存数据 - 只有发货计划显示
        if ("发货计划".equals(category)) {
            rowData.add(vo.getPacking() != null ? vo.getPacking() : BigDecimal.ZERO);
            rowData.add(vo.getAfterPacking() != null ? vo.getAfterPacking() : BigDecimal.ZERO);
            rowData.add(vo.getHighVoltagePackaging() != null ? vo.getHighVoltagePackaging() : BigDecimal.ZERO);
            rowData.add(vo.getLamination() != null ? vo.getLamination() : BigDecimal.ZERO);
            rowData.add(vo.getShapeLamination() != null ? vo.getShapeLamination() : BigDecimal.ZERO);
            rowData.add(vo.getCoating() != null ? vo.getCoating() : BigDecimal.ZERO);
            rowData.add(vo.getPrinting() != null ? vo.getPrinting() : BigDecimal.ZERO);
            rowData.add(vo.getPretreatment() != null ? vo.getPretreatment() : BigDecimal.ZERO);
            rowData.add(vo.getNonAssetWarehouse() != null ? vo.getNonAssetWarehouse() : BigDecimal.ZERO);
            rowData.add(vo.getQualityPreservation() != null ? vo.getQualityPreservation() : BigDecimal.ZERO);
            rowData.add(vo.getScheduleQty() != null ? vo.getScheduleQty() : BigDecimal.ZERO);
        } else {
            // 装车计划这些列为空
            for (int i = 0; i < 11; i++) {
                rowData.add("");
            }
        }

        rowData.add(category);

        // 动态列数据
        if (CollectionUtils.isNotEmpty(dynamicHeaders)) {
            List<DemandDeliveryProductionDetailVO> details = vo.getDetails();
            Map<String, DemandDeliveryProductionDetailVO> detailMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(details)) {
                detailMap = details.stream().collect(Collectors.toMap(DemandDeliveryProductionDetailVO::getTitleDate, d -> d, (d1, d2) -> d1));
            }

            for (String header : dynamicHeaders) {
                DemandDeliveryProductionDetailVO detail = detailMap.get(header);
                if (detail != null) {
                    BigDecimal value = isLoading ? detail.getLoadingQty() : detail.getDemandQty();
                    rowData.add(value != null ? value : BigDecimal.ZERO);
                } else {
                    rowData.add(BigDecimal.ZERO);
                }
            }
        }

        return rowData;
    }

    /**
     * 设置装车计划行的颜色
     */
    private void putLoadingCellColors(Map<String, Short> cellColorMap, DemandDeliveryProductionVO vo,
                                      List<String> dynamicHeaders, int rowIndex, Map<String, Short> colorMapping) {
        if (CollectionUtils.isEmpty(vo.getDetails()) || CollectionUtils.isEmpty(dynamicHeaders)) {
            return;
        }

        Map<String, DemandDeliveryProductionDetailVO> detailMap = vo.getDetails().stream()
                .collect(Collectors.toMap(DemandDeliveryProductionDetailVO::getTitleDate, d -> d, (d1, d2) -> d1));

        int startColIndex = 17; // 动态列开始的位置（固定列数量）

        for (int i = 0; i < dynamicHeaders.size(); i++) {
            String header = dynamicHeaders.get(i);
            DemandDeliveryProductionDetailVO detail = detailMap.get(header);

            if (detail != null) {
                String colorStr = detail.getLoadingColor();
                if (colorStr != null) {
                    Short colorIndex = colorMapping.get(colorStr);
                    if (colorIndex != null) {
                        cellColorMap.put(rowIndex + Constants.DELIMITER + (startColIndex + i), colorIndex);
                    }
                }
            }
        }
    }

    /**
     * 设置发货计划行的颜色
     */
    private void putDemandCellColors(Map<String, Short> cellColorMap, DemandDeliveryProductionVO vo,
                                     List<String> dynamicHeaders, int rowIndex, Map<String, Short> colorMapping) {
        if (CollectionUtils.isEmpty(vo.getDetails()) || CollectionUtils.isEmpty(dynamicHeaders)) {
            return;
        }

        Map<String, DemandDeliveryProductionDetailVO> detailMap = vo.getDetails().stream()
                .collect(Collectors.toMap(DemandDeliveryProductionDetailVO::getTitleDate, d -> d, (d1, d2) -> d1));

        int startColIndex = 17; // 动态列开始的位置（固定列数量）

        for (int i = 0; i < dynamicHeaders.size(); i++) {
            String header = dynamicHeaders.get(i);
            DemandDeliveryProductionDetailVO detail = detailMap.get(header);

            if (detail != null) {
                String colorStr = detail.getDemandColor();
                if (colorStr != null) {
                    Short colorIndex = colorMapping.get(colorStr);
                    if (colorIndex != null) {
                        cellColorMap.put(rowIndex + Constants.DELIMITER + (startColIndex + i), colorIndex);
                    }
                }
            }
        }
    }

    /**
     * 导出空数据表格
     */
    @SneakyThrows
    private void exportEmptyData(HttpServletResponse response) {
        // 创建基本表头（没有动态列）
        List<List<String>> headers = initExportHeaders(new ArrayList<>());

        // 空数据列表
        List<List<Object>> emptyDataList = new ArrayList<>();

        // 设置响应头
        EasyExcelUtil.initResponse(response, "需求发货生产报表");

        // 写入Excel
        EasyExcelFactory.write(response.getOutputStream())
                .head(headers)
                .sheet("需求发货生产报表")
                .doWrite(emptyDataList);
    }
}
