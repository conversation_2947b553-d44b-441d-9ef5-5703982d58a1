package com.yhl.scp.dfp.clean.controller;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.dfp.clean.dto.CleanDemandDataDTO;
import com.yhl.scp.dfp.clean.dto.CleanDemandProductInventoryReportDTO;
import com.yhl.scp.dfp.clean.service.CleanDemandDataService;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataVO;
import com.yhl.scp.dfp.clean.vo.CleanDemandProductInventoryCollectVO;
import com.yhl.scp.dfp.clean.vo.CleanDemandProductInventoryReportVO;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.demand.service.DemandVersionService;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>CleanDemandDataController</code>
 * <p>
 * 日需求数据控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:57
 */
@Slf4j
@Api(tags = "日需求数据控制器")
@RestController
@RequestMapping("cleanDemandData")
public class CleanDemandDataController extends BaseController {

    @Resource
    private CleanDemandDataService cleanDemandDataService;

    @Resource
    private DemandVersionService demandVersionService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<CleanDemandDataVO>> page() {
        List<CleanDemandDataVO> cleanDemandDataList = cleanDemandDataService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<CleanDemandDataVO> pageInfo = new PageInfo<>(cleanDemandDataList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody CleanDemandDataDTO cleanDemandDataDTO) {
        return cleanDemandDataService.doCreate(cleanDemandDataDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody CleanDemandDataDTO cleanDemandDataDTO) {
        return cleanDemandDataService.doUpdate(cleanDemandDataDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        cleanDemandDataService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<CleanDemandDataVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, cleanDemandDataService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "重新计算")
    @GetMapping(value = "recalculate")
    @BusinessMonitorLog(businessCode = "日需求计算", moduleCode = "DFP", businessFrequency = "DAY")
    public BaseResponse<String> recalculate(@RequestParam(name = "versionCode", required = false) String versionCode) {
        //获取最新版本
        DemandVersionVO demandVersionVO =
                demandVersionService.selectLastVersionByVersionTypeAndPlanPeriod(VersionTypeEnum.CLEAN_DEMAND.getCode(), null);
        List<String> analysisResults = new ArrayList<>();
        if (null != demandVersionVO){
            analysisResults = cleanDemandDataService.doRecalculate(demandVersionVO.getVersionCode());
        }
        if (CollectionUtils.isNotEmpty(analysisResults)) {
            analysisResults.sort(String::compareTo);
            String join = String.join("<br/>", analysisResults);
            return BaseResponse.success(BaseResponse.OP_SUCCESS, join);
        } else {
            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        }
    }

    @ApiOperation(value = "自动计算")
    @GetMapping(value = "autoCalculate")
    public BaseResponse<String> autoCalculate() {
        try {
            cleanDemandDataService.autoCalculate();
        } catch (Exception e) {
            return BaseResponse.error(BaseResponse.OP_FAILURE, e.getMessage());
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "根据版本删除")
    @PostMapping(value = "deleteByVersion")
    public BaseResponse<Void> deleteByVersion(@RequestBody List<RemoveVersionDTO> versionDTOList) {
        cleanDemandDataService.doDeleteByVersion(versionDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "主机厂下拉")
    @PostMapping(value = "oemDropdown")
    public BaseResponse<List<LabelValue<String>>> oemDropdown(@RequestParam(value = "versionId", required = false) String versionId) {
        List<LabelValue<String>> labelValues = cleanDemandDataService.selectOemDropdown(versionId);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, labelValues);
    }
    
    @ApiOperation(value = "风险车型管控-产品库存报表")
    @GetMapping(value = "queryProductInventoryReportPage")
    public BaseResponse<PageInfo<CleanDemandProductInventoryReportVO>> queryProductInventoryReportPage() {
        List<CleanDemandProductInventoryReportVO> list = cleanDemandDataService.queryProductInventoryReportPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<CleanDemandProductInventoryReportVO> pageInfo = new PageInfo<>(list);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }
    
    @ApiOperation(value = "风险车型管控-产品库存汇总报表")
    @PostMapping(value = "queryProductInventoryReportCollect")
    public BaseResponse<List<CleanDemandProductInventoryCollectVO>> queryProductInventoryReportCollect(
    		@RequestBody CleanDemandProductInventoryReportDTO queryDTO) {
        List<CleanDemandProductInventoryCollectVO> list = cleanDemandDataService.queryProductInventoryReportCollect(queryDTO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, list);
    }

}
