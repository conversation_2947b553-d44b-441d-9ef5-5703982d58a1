<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastDataDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataPO">
        <!--@Table fdp_consistence_demand_forecast_data-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_id" jdbcType="VARCHAR" property="versionId"/>
        <result column="demand_category" jdbcType="VARCHAR" property="demandCategory"/>
        <result column="forecast_type" jdbcType="VARCHAR" property="forecastType"/>
        <result column="origin_id" jdbcType="VARCHAR" property="originId"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="version_status" jdbcType="VARCHAR" property="versionStatus"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO">
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="forecast_quantity" jdbcType="DECIMAL" property="forecastQuantity"/>
        <result column="forecast_time_str" jdbcType="DECIMAL" property="forecastTimeStr"/>
    </resultMap>
    <resultMap id="DeliveryPlanVO2ResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2">
        <result column="demand_category" jdbcType="VARCHAR" property="demandCategory"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="forecast_time" jdbcType="TIMESTAMP" property="demandTime"/>
        <result column="version_id" jdbcType="TIMESTAMP" property="versionId"/>
        <result column="forecast_quantity" jdbcType="INTEGER" property="demandQuantity"/>
        <result column="customer_forecasts_quantity" jdbcType="INTEGER" property="customerForecastsQuantity"/>
        <result column="version_code" jdbcType="VARCHAR" property="versionCode"/>
        <result column="plan_period" jdbcType="VARCHAR" property="planPeriod"/>
    </resultMap>
    <resultMap id="LineChartMap" type="com.yhl.scp.dfp.release.dto.ReleaseLineChartDTO">
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="forecast_time" jdbcType="VARCHAR" property="forecastTime"/>
        <result column="demand_forecast" jdbcType="INTEGER" property="demandForecast"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,version_id,demand_category,forecast_type,origin_id,oem_code,vehicle_model_code,product_code,version_status,
        remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,oem_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.versionId != null and params.versionId != ''">
                and version_id = #{params.versionId,jdbcType=VARCHAR}
            </if>
            <if test="params.versionIdList != null and params.versionIdList.size() > 0">
                and version_id in
                <foreach collection="params.versionIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.demandCategory != null and params.demandCategory != ''">
                and demand_category = #{params.demandCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.forecastType != null and params.forecastType != ''">
                and forecast_type = #{params.forecastType,jdbcType=VARCHAR}
            </if>
            <if test="params.originId != null and params.originId != ''">
                and origin_id = #{params.originId,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCodeList != null and params.oemCodeList.size() > 0">
                and oem_code in
                <foreach collection="params.oemCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.oemNameList != null and params.oemNameList.size() > 0">
                and oem_name in
                <foreach collection="params.oemNameList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleCodeList != null and params.vehicleCodeList.size() > 0">
                and vehicle_model_code in
                <foreach collection="params.vehicleCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodeList != null and params.productCodeList.size() > 0">
                and product_code in
                <foreach collection="params.productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.versionStatus != null and params.versionStatus != ''">
                and version_status = #{params.versionStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.productCodeLike != null and params.productCodeLike != ''">
                and product_code like concat('%', #{params.productCodeLike,jdbcType=VARCHAR}, '%')
            </if>
            <if test="params.oemNameLike != null and params.oemNameLike != ''">
                and oem_name like concat('%', #{params.oemNameLike,jdbcType=VARCHAR}, '%')
            </if>
            <if test="params.vehicleModelCodeLike != null and params.vehicleModelCodeLike != ''">
                and vehicle_model_code like concat('%', #{params.vehicleModelCodeLike,jdbcType=VARCHAR}, '%')
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_consistence_demand_forecast_data
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_consistence_demand_forecast_data
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from fdp_consistence_demand_forecast_data
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_consistence_demand_forecast_data
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_fdp_consistence_demand_forecast_data
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_consistence_demand_forecast_data(
        id,
        version_id,
        demand_category,
        forecast_type,
        origin_id,
        oem_code,
        vehicle_model_code,
        product_code,
        version_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{demandCategory,jdbcType=VARCHAR},
        #{forecastType,jdbcType=VARCHAR},
        #{originId,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{versionStatus,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataPO">
        insert into fdp_consistence_demand_forecast_data(id,
                                                         version_id,
                                                         demand_category,
                                                         forecast_type,
                                                         origin_id,
                                                         oem_code,
                                                         vehicle_model_code,
                                                         product_code,
                                                         version_status,
                                                         remark,
                                                         enabled,
                                                         creator,
                                                         create_time,
                                                         modifier,
                                                         modify_time,
                                                         version_value)
        values (#{id,jdbcType=VARCHAR},
                #{versionId,jdbcType=VARCHAR},
                #{demandCategory,jdbcType=VARCHAR},
                #{forecastType,jdbcType=VARCHAR},
                #{originId,jdbcType=VARCHAR},
                #{oemCode,jdbcType=VARCHAR},
                #{vehicleModelCode,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{versionStatus,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_consistence_demand_forecast_data(
        id,
        version_id,
        demand_category,
        forecast_type,
        origin_id,
        oem_code,
        vehicle_model_code,
        product_code,
        version_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.versionId,jdbcType=VARCHAR},
            #{entity.demandCategory,jdbcType=VARCHAR},
            #{entity.forecastType,jdbcType=VARCHAR},
            #{entity.originId,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.versionStatus,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_consistence_demand_forecast_data(
        id,
        version_id,
        demand_category,
        forecast_type,
        origin_id,
        oem_code,
        vehicle_model_code,
        product_code,
        version_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.versionId,jdbcType=VARCHAR},
            #{entity.demandCategory,jdbcType=VARCHAR},
            #{entity.forecastType,jdbcType=VARCHAR},
            #{entity.originId,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.versionStatus,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataPO">
        update fdp_consistence_demand_forecast_data
        set version_id         = #{versionId,jdbcType=VARCHAR},
            demand_category    = #{demandCategory,jdbcType=VARCHAR},
            forecast_type      = #{forecastType,jdbcType=VARCHAR},
            origin_id          = #{originId,jdbcType=VARCHAR},
            oem_code           = #{oemCode,jdbcType=VARCHAR},
            vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
            product_code       = #{productCode,jdbcType=VARCHAR},
            version_status     = #{versionStatus,jdbcType=VARCHAR},
            remark             = #{remark,jdbcType=VARCHAR},
            enabled            = #{enabled,jdbcType=VARCHAR},
            modifier           = #{modifier,jdbcType=VARCHAR},
            modify_time        = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER};
        update fdp_consistence_demand_forecast_data
        set version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataPO">
        update fdp_consistence_demand_forecast_data
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandCategory != null and item.demandCategory != ''">
                demand_category = #{item.demandCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.forecastType != null and item.forecastType != ''">
                forecast_type = #{item.forecastType,jdbcType=VARCHAR},
            </if>
            <if test="item.originId != null and item.originId != ''">
                origin_id = #{item.originId,jdbcType=VARCHAR},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.versionStatus != null and item.versionStatus != ''">
                version_status = #{item.versionStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER};
        update fdp_consistence_demand_forecast_data set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_consistence_demand_forecast_data
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="forecast_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.forecastType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="origin_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.originId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>;
        update fdp_consistence_demand_forecast_data set
        version_value = version_value + 1
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_consistence_demand_forecast_data
            <set>
                <if test="item.versionId != null and item.versionId != ''">
                    version_id = #{item.versionId,jdbcType=VARCHAR},
                </if>
                <if test="item.demandCategory != null and item.demandCategory != ''">
                    demand_category = #{item.demandCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.forecastType != null and item.forecastType != ''">
                    forecast_type = #{item.forecastType,jdbcType=VARCHAR},
                </if>
                <if test="item.originId != null and item.originId != ''">
                    origin_id = #{item.originId,jdbcType=VARCHAR},
                </if>
                <if test="item.oemCode != null and item.oemCode != ''">
                    oem_code = #{item.oemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                    vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.versionStatus != null and item.versionStatus != ''">
                    version_status = #{item.versionStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER};
            update fdp_consistence_demand_forecast_data set
            version_value = version_value + 1
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="updateForecastValueById" parameterType="com.yhl.scp.dfp.release.dto.ReleaseProductItemDTO">
        update fdp_consistence_demand_forecast_data_detail
        set forecast_quantity = #{quantity,jdbcType=INTEGER},
            version_value     = version_value + 1
        where id = #{dataId,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_consistence_demand_forecast_data
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_consistence_demand_forecast_data where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!-- 批量id+版本删除 -->
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from fdp_consistence_demand_forecast_data where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>

    <select id="selectVO2ByPlanPeriod" resultMap="DeliveryPlanVO2ResultMap">
        SELECT b.oem_code,
               b.product_code,
               b.vehicle_model_code,
               b.version_id,
               b.plan_period,
               b.version_code,
               fcfdd.forecast_time,
               sum(fcfdd.forecast_quantity) AS forecast_quantity
        FROM fdp_consistence_demand_forecast_data_detail fcfdd,
             (SELECT fcfd.id,
                     fcfd.oem_code,
                     fcfd.vehicle_model_code,
                     fcfd.product_code,
                     fcfd.version_id,
                     c.plan_period,
                     c.version_code
              FROM fdp_consistence_demand_forecast_data fcfd,
                   (SELECT id,
                           plan_period,
                           version_code
                    FROM `fdp_consistence_demand_forecast_version`
                    WHERE  oem_code IS NULL
                      AND version_status = 'PUBLISHED'
                    ORDER BY modify_time
                    DESC LIMIT 1) c
              WHERE fcfd.version_id = c.id) b
        WHERE fcfdd.consistence_demand_forecast_data_id = b.id
        <if test="startTime != null">
            and fcfdd.forecast_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and fcfdd.forecast_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        GROUP BY b.oem_code,
                 b.product_code,
                 b.version_id,
                 b.vehicle_model_code,
                 b.plan_period,
                 b.version_code,
                 fcfdd.forecast_time
    </select>

    <select id="selectVO2ByVersionId" resultMap="DeliveryPlanVO2ResultMap">
        SELECT
        fcdfd.demand_category,
        fcdfv.plan_period,
        fcdfv.version_code,
        fcdfd.product_code,
        fcdfd.oem_code,
        fcdfd.vehicle_model_code,
        detail.forecast_time,
        detail.forecast_quantity,
        detail.customer_forecasts_quantity
        FROM
        fdp_consistence_demand_forecast_version fcdfv,
        fdp_consistence_demand_forecast_data fcdfd,
        fdp_consistence_demand_forecast_data_detail detail
        WHERE
        fcdfd.version_id = fcdfv.id
        AND detail.consistence_demand_forecast_data_id = fcdfd.id
        AND fcdfv.id in
        <foreach collection="versionIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectOemInfoByVersionId" parameterType="com.yhl.scp.dfp.release.dto.ReleaseOemDTO"
            resultType="com.yhl.scp.dfp.release.vo.ReleaseOemVO">
        select oem_code as oemCode, oem_name as oemName, risk_level as oemRiskLevel, demand_category as demandType
        from v_fdp_consistence_demand_forecast_version_oem_risk
        where 1 = 1
        and version_id = #{versionId,jdbcType=VARCHAR}
        <if test="demandType != null and demandType != ''">
            and demand_category = #{demandType,jdbcType=VARCHAR}
        </if>
        <if test="oemCodes != null and oemCodes.size() > 0">
            and oem_code in
            <foreach collection="oemCodes" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="oemNames != null and oemNames.size() > 0">
            and oem_name in
            <foreach collection="oemNames" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="oemRiskLevels != null and oemRiskLevels.size() > 0">
            and risk_level in
            <foreach collection="oemRiskLevels" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="selectByVersionAndOem" resultType="com.yhl.scp.dfp.release.vo.ReleaseVO">
        select fcdfdd.id,
               fcdfd.product_code       as productCode,
               mpsp.core_process        as coreProcess,
               mpsp.product_special     as productSpecial,
               fcdfd.vehicle_model_code as vehicleModelCode,
               fmrl.material_risk_level as materialRiskLevel,
               fcdfdd.forecast_time     as forecastTime,
               fcdfdd.forecast_quantity as quantity,
               fcdfdd.version_value     as versionValue
        from fdp_consistence_demand_forecast_data fcdfd
                 left join fdp_consistence_demand_forecast_data_detail fcdfdd
                           on fcdfdd.consistence_demand_forecast_data_id = fcdfd.id
                 left join (select distinct product_code, core_process, product_special
                            from mds_product_stock_point
                            where 1=1 
                            <if test="stockPointCode != null and stockPointCode != ''">
					            and stock_point_code = #{stockPointCode,jdbcType=VARCHAR}
							</if>
                            ) mpsp
                           on mpsp.product_code = fcdfd.product_code
                 left join (select distinct product_code, material_risk_level, oem_code from fdp_part_risk_level) fmrl
                           on fmrl.product_code = fcdfd.product_code and fcdfd.oem_code = fmrl.oem_code
        where 1 = 1
          and fcdfd.version_id = #{versionId,jdbcType=VARCHAR}
          and fcdfd.demand_category = #{demandCategory,jdbcType=VARCHAR}
      	<if test="oemCode != null and oemCode != ''">
            and fcdfd.oem_code = #{oemCode,jdbcType=VARCHAR}
		</if>
    </select>
    <select id="selectLineChartByYearAndOem" resultMap="LineChartMap">
        select *
        from v_fdp_consistence_demand_forecast_line_chart
        where forecast_time like concat(#{calcYear}, '%')
          and oem_code = #{oemCode,jdbcType=VARCHAR}
          and demand_type = #{demandType,jdbcType=VARCHAR}
    </select>
    <select id="selectDistinctVersionCode" resultType="java.util.Map">
        select distinct version_code as versionCode, id
        from fdp_consistence_demand_forecast_version
        where 1 = 1
          and version_status = #{versionStatus,jdbcType=VARCHAR}
          and version_code is not null
          and oem_code is null
        order by version_code desc
    </select>
    <select id="selectDistinctNewVersionCode" resultType="java.util.Map">
        select 
        	distinct
        	version_code as versionCode, 
        	id, 
        	version_status as versionStatus
        from fdp_consistence_demand_forecast_version
        where 1 = 1
          and version_code is not null
          and oem_code is null
        order by version_code desc
    </select>
    <select id="selectReleaseExportVO" parameterType="java.util.Map" resultType="com.yhl.scp.dfp.release.vo.ReleaseExportVO">
        SELECT
            t1.demand_category AS demandCategory,
            t1.oem_code AS oemCode,
            t1.vehicle_model_code AS vehicleModelCode,
            t1.product_code AS productCode,
            date_format( t2.forecast_time, '%Y-%m' ) AS planPeriod,
            t3.vehicle_model_name AS vehicleModelName,
            sum( t2.forecast_quantity ) AS sumQty
        FROM
            ( SELECT * FROM `fdp_consistence_demand_forecast_data` WHERE version_id = #{params.versionId,jdbcType=VARCHAR} ) t1
                LEFT JOIN `fdp_consistence_demand_forecast_data_detail` t2 ON t1.id = t2.consistence_demand_forecast_data_id
                left join (
                    select distinct oem_code,vehicle_model_code,vehicle_model_name
                    from mds_oem_vehicle_model_map
                    group by oem_code,vehicle_model_code,vehicle_model_name
                ) t3 on t1.oem_code = t3.oem_code  and t1.vehicle_model_code = t3.vehicle_model_code
        GROUP BY
            t1.demand_category,
            t1.oem_code,
            t1.vehicle_model_code,
            t1.product_code,
            t3.vehicle_model_name ,
            date_format( t2.forecast_time, '%Y-%m' )
    </select>
    
    <select id="selectForecastQuantityByVersionId" resultMap="VOResultMap">
        SELECT
			a.demand_category,
			a.oem_code,
			a.vehicle_model_code,
			a.product_code,
			DATE_FORMAT(b.forecast_time, '%Y-%m') forecast_time_str,
			sum(b.forecast_quantity) forecast_quantity
		FROM
			fdp_consistence_demand_forecast_data a
			LEFT JOIN fdp_consistence_demand_forecast_data_detail b ON a.id = b.consistence_demand_forecast_data_id 
		WHERE
			a.version_id = #{versionId,jdbcType=VARCHAR}
			<if test="yearMonthStr != null and yearMonthStr != ''">
                and DATE_FORMAT(b.forecast_time, '%Y-%m') = #{yearMonthStr,jdbcType=VARCHAR}
            </if>
			GROUP BY
			a.demand_category,
			a.oem_code,
			a.vehicle_model_code,
			a.product_code,
			DATE_FORMAT(b.forecast_time, '%Y-%m')
    </select>
    
    <select id="selectCollectByVersionId" resultType="com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO">
        SELECT
			a.oem_code oemCode,
			a.vehicle_model_code vehicleModelCode,
			a.product_code productCode,
			DATE_FORMAT( b.forecast_time, '%Y%m' ) forecastTimeStr,
			sum( b.customer_forecasts_quantity ) customerForecastQuantity,
			sum( b.forecast_quantity ) forecastQuantity
		FROM
			fdp_consistence_demand_forecast_data a
			LEFT JOIN fdp_consistence_demand_forecast_data_detail b ON a.id = b.consistence_demand_forecast_data_id 
		WHERE
			a.version_id = #{params.versionId,jdbcType=VARCHAR}
			<if test="params.oemCode != null and params.oemCode != ''">
                and a.oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and a.vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
		GROUP BY
			a.oem_code,
			a.vehicle_model_code,
			a.product_code,
			DATE_FORMAT( b.forecast_time, '%Y%m' )
    </select>
    <select id="selectCollectByVersionIds" resultType="com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO">
        SELECT
        	c.plan_period planPeriod,
			a.oem_code oemCode,
			a.vehicle_model_code vehicleModelCode,
			DATE_FORMAT( b.forecast_time, '%Y%m' ) forecastTimeStr,
			sum( b.forecast_quantity ) forecastQuantity
		FROM
			fdp_consistence_demand_forecast_data a
			LEFT JOIN fdp_consistence_demand_forecast_data_detail b ON a.id = b.consistence_demand_forecast_data_id 
			LEFT JOIN fdp_consistence_demand_forecast_version c ON c.id = a.version_id 
		WHERE
			a.version_id in 
			<foreach collection="params.versionIds" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
			<if test="params.oemCode != null and params.oemCode != ''">
                and a.oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and a.vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
		GROUP BY
			c.plan_period,
			a.oem_code,
			a.vehicle_model_code,
			DATE_FORMAT( b.forecast_time, '%Y%m' )
    </select>
    <select id="selectIgnorePlannerDataList" parameterType="java.lang.String" resultMap="VOResultMap">
        select distinct cdfd.*
        from fdp_consistence_demand_forecast_data cdfd
                 left join fdp_consistence_demand_forecast_data_detail cdfdd on cdfd.id = cdfdd.consistence_demand_forecast_data_id
                 left join mds_product_stock_point psp on cdfd.product_code=psp.product_code
                 left join mds_stock_point sp on psp.stock_point_code=sp.stock_point_code
        where cdfd.version_id = #{versionId,jdbcType=VARCHAR}
          and (trim(psp.order_planner) != '' and psp.order_planner is not null and psp.order_planner != #{orderPlanner,jdbcType=VARCHAR})
          and date_format(cdfdd.forecast_time,'%Y%m') >= #{planPeriod,jdbcType=VARCHAR}
          and sp.organize_type ='SALE_ORGANIZATION'
          and cdfdd.id is not null
    </select>
</mapper>
