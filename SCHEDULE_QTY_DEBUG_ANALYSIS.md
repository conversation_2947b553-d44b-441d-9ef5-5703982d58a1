# 已排产量计算问题深度调试分析

## 🎯 问题描述

产品`01009LDR06001`在需求发货生产报表中的已排产量(scheduleQty)计算结果为0，但在主生产计划（masterPlanWorkOrder）中有对应的排产量。

## 🔍 根本原因分析

基于对MPS服务`masterPlanWorkOrder`方法和DFP服务已排产量计算逻辑的深入分析，发现以下关键问题：

### 1. 数据流向不匹配
```
需求发货计划产品编码 → DFP报表分组 → 已排产量计算 → 工单产品编码匹配
```

### 2. MPS服务中的parentProductCode逻辑
```java
// MPS服务关键逻辑
String parentProductCode = newProductStockPoint.getProductCode(); // 默认当前产品
if (Objects.nonNull(parentOrder)) {
    NewProductStockPointVO parentWorkOrderProduct = productMap.get(productId);
    parentProductCode = parentWorkOrderProduct.getProductCode(); // 使用顶层工单产品编码
}
```

### 3. 可能的匹配失败原因
1. **产品编码不存在**：`01009LDR06001`在数据库中可能不存在
2. **工单层级问题**：当前工单与顶层工单的产品编码不一致
3. **数据查询范围问题**：成型工序任务查询可能有遗漏
4. **匹配逻辑缺陷**：三层匹配逻辑可能仍有盲点

## 🛠️ 增强调试方案

### 1. 目标产品特殊调试
为产品`01009LDR06001`增加了专门的调试日志，标记为`🎯 [目标产品调试]`：

```java
boolean isTargetProduct = "01009LDR06001".equals(productCode);
if (isTargetProduct) {
    log.info("🎯 [目标产品调试] 开始计算产品{}的已排产量", productCode);
}
```

### 2. 详细的数据流跟踪
- **任务遍历**：记录每个主生产计划任务的处理过程
- **工单详情**：显示工单的产品编码、顶层工单ID、数量等关键信息
- **匹配过程**：详细记录三层匹配逻辑的执行结果
- **数量累加**：跟踪每次数量累加的过程

### 3. 匹配逻辑增强调试
在`matchesProduct`方法中增加了详细的调试信息：

```java
// 顶层匹配调试
if (isTargetProduct) {
    log.info("🎯 [目标产品匹配调试] 顶层工单{}产品编码: {}", 
            workOrder.getTopOrderId(), topWorkOrderProductCode);
}

// 直接匹配调试
if (isTargetProduct) {
    log.info("🎯 [目标产品匹配调试] 检查直接匹配: 工单产品编码={}, 目标产品编码={}", 
            workOrderProductCode, productCode);
}

// 产品族匹配调试
if (isTargetProduct) {
    log.info("🎯 [目标产品匹配调试] 开始产品族匹配检查");
}
```

## 📊 调试信息输出示例

运行修复后的代码，将看到类似以下的调试信息：

```
🎯 [目标产品调试] 开始计算产品01009LDR06001的已排产量，任务数量: X
🎯 [目标产品调试] 工单映射大小: X, 顶层工单映射大小: X
🎯 [目标产品调试] 开始遍历X个主生产计划任务
🎯 [目标产品调试] 处理第1个任务，ID: xxx, 工序类型: 成型
🎯 [目标产品调试] 找到成型工序任务，工单ID: xxx
🎯 [目标产品调试] 工单xxx详情: 产品编码=xxx, 顶层工单ID=xxx, 数量=xxx
🎯 [目标产品调试] 顶层工单xxx详情: 产品编码=xxx, 数量=xxx
🎯 [目标产品调试] 开始匹配工单xxx的产品编码，目标产品编码: 01009LDR06001
🎯 [目标产品匹配调试] 开始匹配工单xxx, 目标产品编码: 01009LDR06001
🎯 [目标产品匹配调试] 工单产品编码: xxx, 顶层工单ID: xxx
🎯 [目标产品匹配调试] 顶层工单xxx产品编码: xxx
🎯 [目标产品匹配调试] ✅ 顶层工单产品编码匹配成功！ 或 ❌ 顶层工单产品编码不匹配
🎯 [目标产品调试] 工单xxx匹配结果: 匹配成功/匹配失败
🎯 [目标产品调试] 工单xxx匹配成功！累加数量: xxx, 当前总量: xxx
🎯 [目标产品调试] 产品01009LDR06001最终计算结果:
🎯 [目标产品调试] - 总已排产量: xxx
🎯 [目标产品调试] - 成功处理任务数: xxx
🎯 [目标产品调试] - 跳过任务数: xxx
```

## 🔧 问题诊断指南

### 如果已排产量仍为0，检查以下信息：

1. **任务数量检查**
   ```
   🎯 [目标产品调试] 成型工序任务总数: X
   ```
   如果为0，说明没有成型工序任务

2. **工单存在性检查**
   ```
   🎯 [目标产品调试] 工单xxx在工单映射中不存在
   ```
   如果出现此信息，说明工单数据查询有问题

3. **产品编码匹配检查**
   ```
   🎯 [目标产品匹配调试] ❌ 所有匹配方式都失败了！
   ```
   如果出现此信息，说明产品编码匹配逻辑有问题

4. **数量检查**
   ```
   🎯 [目标产品调试] 顶层工单xxx详情: 产品编码=xxx, 数量=0
   ```
   如果数量为0，说明顶层工单数量有问题

## 🎯 下一步行动

1. **部署修复代码**：将增强调试的代码部署到测试环境
2. **执行测试**：调用需求发货生产报表接口，查询产品`01009LDR06001`
3. **分析日志**：根据调试信息定位具体问题
4. **数据库验证**：如果需要，直接查询数据库验证数据完整性
5. **针对性修复**：根据调试结果进行针对性修复

## 📝 修改文件清单

- `scp-dfp-service/src/main/java/com/yhl/scp/dfp/report/service/impl/DemandDeliveryProductionServiceImpl.java`
  - 增强了`calculateScheduleQty`方法的调试信息
  - 增强了`matchesProduct`方法的调试信息
  - 添加了目标产品的特殊调试逻辑

---

**修复日期**: 2025-01-09  
**修复类型**: 调试增强 + 问题诊断  
**目标**: 定位产品`01009LDR06001`已排产量为0的根本原因
