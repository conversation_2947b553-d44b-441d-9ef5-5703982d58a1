package com.yhl.scp.mps.capacityBalance.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.excel.CustomColumnWidthHandler;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.MessageTypeEnum;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.dto.UserMessageDTO;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.untils.UserMessageUtils;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.vo.BomRoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepResourceVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.capacityBalance.dao.CapacityBalanceDao;
import com.yhl.scp.mps.capacityBalance.dto.CapacityLoadDTO;
import com.yhl.scp.mps.capacityBalance.dto.CapacitySupplyRelationshipWeekEditorDTO;
import com.yhl.scp.mps.capacityBalance.enums.CapacityBalanceRule;
import com.yhl.scp.mps.capacityBalance.enums.CapacityBalanceTypeEnum;
import com.yhl.scp.mps.capacityBalance.enums.LockStatusEnum;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacityLoadDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacitySupplyRelationshipPO;
import com.yhl.scp.mps.capacityBalance.service.CapacityLoadService;
import com.yhl.scp.mps.capacityBalance.service.CapacitySupplyRelationshipService;
import com.yhl.scp.mps.capacityBalance.service.CapacityWeekBalanceService;
import com.yhl.scp.mps.capacityBalance.tool.EasyExcelUtil;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO2;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.enums.SupplyModelEnum;
import com.yhl.scp.mps.plan.service.impl.MasterPlanServiceImpl;
import com.yhl.scp.mps.plan.support.MasterPlanInventorySupport;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CapacityWeekBalanceServiceImpl</code>
 * <p>
 * 周产能平衡页面查询相关接口实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-25 13:57:04
 */
@Slf4j
@Service
public class CapacityWeekBalanceServiceImpl implements CapacityWeekBalanceService {

    @Resource
    private CapacityLoadDao capacityLoadDao;
    @Resource
    private CapacitySupplyRelationshipDao capacitySupplyRelationshipDao;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private CapacityBalanceDao capacityBalanceDao;
    @Resource
    private MasterPlanInventorySupport masterPlanInventorySupport;
    @Resource
    private UserMessageUtils userMessageUtils;
    @Resource
    private CapacitySupplyRelationshipService capacitySupplyRelationshipService;
    @Resource
    private CapacityLoadService capacityLoadService;

    @Override
    public List<CapacityLoadVO> selectResourceByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {

        if (StringUtils.isBlank(queryCriteriaParam)){
            queryCriteriaParam = "and version_id = 'WEEK'";
        }
        List<CapacityLoadVO> dataList = capacityLoadDao.selectByCondition(sortParam, queryCriteriaParam);
        if (CollectionUtils.isEmpty(dataList)){
            return new ArrayList<>();
        }
        String pattern = "yyyy-MM-dd";
        Map<String, Object> params = new HashMap<>();
        params.put("versionId", CapacityBalanceTypeEnum.WEEK.getCode());
        List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOList = capacitySupplyRelationshipDao.selectByParams(params);
        log.info("产能负荷数据条数{}，产能供应数据条数{}", dataList.size(), capacitySupplyRelationshipPOList.size());
        Date startDate = getMin(dataList, CapacityLoadVO::getForecastTime, Comparator.comparing(Date::getTime));
        Map<String, List<CapacitySupplyRelationshipPO>> suppluRelationMap = new HashMap<>();
        Set<String> weekList = new HashSet<>();
        for (CapacitySupplyRelationshipPO po : capacitySupplyRelationshipPOList) {

            String key = getKey(po.getSupplyTime(), startDate, pattern);
            if (!suppluRelationMap.containsKey(key)){
                List<CapacitySupplyRelationshipPO> list = new ArrayList<>();
                list.add(po);
                suppluRelationMap.put(key, list);
            }else {
                suppluRelationMap.get(key).add(po);
            }
            weekList.add(key);
        }

        Map<String, BigDecimal> averageBeatMap = new HashMap<>();
        for (Map.Entry<String, List<CapacitySupplyRelationshipPO>> entry : suppluRelationMap.entrySet()) {
            String key = entry.getKey();
            List<CapacitySupplyRelationshipPO> value = entry.getValue();
            //平均节拍=sum(需求量*节拍)/sum(需求量)
            BigDecimal sumBeat = value.stream().map(t -> t.getDemandQuantity().multiply(new BigDecimal(t.getBeat()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal sumDemandQuantity = value.stream().map(CapacitySupplyRelationshipPO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal averageBeat = sumBeat.divide(sumDemandQuantity, 2, RoundingMode.HALF_UP);
            averageBeatMap.put(key, averageBeat);
        }
        List<CapacityLoadVO> newDataList = new ArrayList<>();
        Map<String, List<CapacityLoadVO>> map = dataList.stream().collect(Collectors.groupingBy(t -> t.getPlantCode()+"-"+t.getResourceGroupCode()+"-"+ t.getResourceCode()));
        for (Map.Entry<String, List<CapacityLoadVO>> entry : map.entrySet()) {
            CapacityLoadVO vo = new CapacityLoadVO();
            vo.setPlantCode(entry.getKey().split("-")[0]);
            vo.setResourceGroupCode(entry.getKey().split("-")[1]);
            vo.setResourceCode(entry.getKey().split("-")[2]);
            vo.setOperationCode(entry.getValue().get(0).getOperationCode());
            vo.setOperationName(entry.getValue().get(0).getOperationName());
            List<CapacityLoadVO2> vo2List = new ArrayList<>();
            Map<String, List<CapacityLoadVO>> listMap = entry.getValue().stream().collect(Collectors.groupingBy(t -> getKey(t.getForecastTime(), startDate, pattern)));
            for (Map.Entry<String, List<CapacityLoadVO>> listEntry : listMap.entrySet()) {
                String key = listEntry.getKey();
                CapacityLoadVO2 capacityLoadVO2 = new CapacityLoadVO2();
                capacityLoadVO2.setForecastWeekTime(key);
                //周总产能
                BigDecimal weekDemandQuantity = getSum(listEntry.getValue(), CapacityLoadVO::getDemandQuantity);
                // 周总产能
                BigDecimal weekAvailableCapacity = getSum(listEntry.getValue(), CapacityLoadVO::getAvailableCapacity);
                // 周总消耗产能
                BigDecimal weekProductionCapacity = getSum(listEntry.getValue(), CapacityLoadVO::getProductionCapacity);
                BigDecimal divide = weekProductionCapacity.divide(weekAvailableCapacity, 2, RoundingMode.HALF_UP);
                capacityLoadVO2.setDemandQuantity(weekDemandQuantity);
                capacityLoadVO2.setProductionCapacity(weekProductionCapacity);
                capacityLoadVO2.setAvailableCapacity(weekAvailableCapacity);
                capacityLoadVO2.setCapacityUtilization(divide);
                capacityLoadVO2.setAverageBeat(averageBeatMap.getOrDefault(key, BigDecimal.ZERO));
                capacityLoadVO2.setTotalCapacity(capacityLoadVO2.getAverageBeat().compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : capacityLoadVO2.getAvailableCapacity().divide(capacityLoadVO2.getAverageBeat(), 0, RoundingMode.CEILING));
                capacityLoadVO2.setColor(getColor(capacityLoadVO2.getCapacityUtilization()));
                vo2List.add(capacityLoadVO2);
            }
            Map<String, CapacityLoadVO2> vo2Map = vo2List.stream().collect(Collectors.toMap(CapacityLoadVO2::getForecastWeekTime, Function.identity(), (v1, v2) -> v1));
            for (String week : weekList) {
                if (!vo2Map.containsKey(week)){
                    CapacityLoadVO2 capacityLoadVO2 = new CapacityLoadVO2(null, week, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "0", 0);
                    vo2List.add(capacityLoadVO2);
                }
            }
            //按月份升序排序
            vo2List.sort(Comparator.comparing(CapacityLoadVO2::getForecastWeekTime));
            vo.setCapacityLoadVO2List(vo2List);
            newDataList.add(vo);
        }

        return newDataList.stream().sorted(Comparator.comparing(t->t.getOperationCode()+t.getOperationName()+t.getResourceCode())).collect(Collectors.toList());

    }
    private String getColor(BigDecimal capacityUtilization){
        if (capacityUtilization.compareTo(new BigDecimal("0.8")) >= 0 && capacityUtilization.compareTo(BigDecimal.ONE) < 0){
            //返回黄色
            return "1";
        }else if (capacityUtilization.compareTo(BigDecimal.ONE) >= 0){
            //返回红色
            return "2";
        }else {
            //返回白的
            return "0";
        }
    }

    public static String getKey(Date date, Date startDate, String pattern) {
        //该周第一天
        DateTime start = DateUtil.beginOfWeek(date);
        //该周最后一天
        DateTime end = DateUtil.endOfWeek(date);

        if (start.before(startDate)){
            start.setTime(startDate.getTime());
        }
        return DateUtils.dateToString(start, pattern) + "~" + DateUtils.dateToString(end, pattern);
    }

    @Override
    public List<CapacityLoadVO> selectOperationByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        List<CapacityLoadVO> result = new ArrayList<>();
        List<CapacityLoadVO> capacityLoadVOS = this.selectResourceByPage(pagination, sortParam, queryCriteriaParam);
        if (CollectionUtils.isEmpty(capacityLoadVOS)){
            return result;
        }
        List<String> weekList = capacityLoadVOS.get(0).getCapacityLoadVO2List().stream().map(CapacityLoadVO2::getForecastWeekTime).collect(Collectors.toList());
        Map<String, List<CapacityLoadVO>> operationMap = capacityLoadVOS.stream().collect(Collectors.groupingBy(t -> t.getOperationCode() + "-" + t.getOperationName()));

        for (Map.Entry<String, List<CapacityLoadVO>> entry : operationMap.entrySet()) {
            CapacityLoadVO capacityLoadVO = entry.getValue().get(0);
            List<CapacityLoadVO2> capacityLoadVO2List = new ArrayList<>();
            for (String week : weekList) {
                CapacityLoadVO2 vo2 = new CapacityLoadVO2();
                vo2.setForecastWeekTime(week);
                List<CapacityLoadVO2> vo2List = entry.getValue().stream().flatMap(t -> t.getCapacityLoadVO2List().stream()).filter(t->week.equals(t.getForecastWeekTime())).collect(Collectors.toList());
                //工序总需求量
                BigDecimal operationDemandQuantity = vo2List.stream().map(CapacityLoadVO2::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                //工序月度总产量
                BigDecimal operationTotalCapacity = vo2List.stream().map(CapacityLoadVO2::getTotalCapacity).reduce(BigDecimal.ZERO, BigDecimal::add);
                //工序负荷率
                BigDecimal operationCapacityUtilization = operationTotalCapacity.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : operationDemandQuantity.divide(operationTotalCapacity, 4, RoundingMode.HALF_UP);
                vo2.setDemandQuantity(operationDemandQuantity);
                vo2.setTotalCapacity(operationTotalCapacity);
                vo2.setCapacityUtilization(operationCapacityUtilization);
                vo2.setColor(getColor(vo2.getCapacityUtilization()));
                capacityLoadVO2List.add(vo2);
            }

            CapacityLoadVO vo = new CapacityLoadVO();
            BeanUtils.copyProperties(capacityLoadVO, vo);
            vo.setResourceCode(null);
            vo.setResourceName(null);
            vo.setCapacityLoadVO2List(capacityLoadVO2List);
            result.add(vo);
        }
        return result.stream().sorted(Comparator.comparing(t->t.getOperationCode()+t.getOperationName())).collect(Collectors.toList());
    }

    @Override
    public List<CapacitySupplyRelationshipVO> selectSupplyByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        if (StringUtils.isBlank(queryCriteriaParam)){
            queryCriteriaParam = "and version_id = 'WEEK'";
        }
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return capacitySupplyRelationshipDao.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Resource
    private IpsFeign ipsFeign;

    @Override
    public List<CapacityLoadVO> selectOverloadByPage(String resourceCode, Integer utilization) {
        //负荷率标准
        BigDecimal divide1 = new BigDecimal(utilization).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        //是否准确查询产线数据
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(SystemHolder.getScenario());
        standardStepVOS = standardStepVOS.stream()
                .filter(item->"FORMING_PROCESS".equals(item.getStandardStepType()))
                .collect(Collectors.toList());
        if (standardStepVOS.isEmpty()){
            throw new RuntimeException("未找到该租户下的关键工序数据，请进行维护");
        }
        String queryCriteriaParam = this.concatKeyStandardStep(standardStepVOS);
//        if (StringUtils.isNotEmpty(resourceCode)){
//            queryCriteriaParam += "and resource_code = '" + resourceCode + "'";
//        }
        // 获取关键工序的产能负荷表数据
        List<CapacityLoadVO> capacityLoadVOS = capacityLoadDao.selectByCondition(null, queryCriteriaParam);
        //没有数据就返回空集合
        if (CollectionUtils.isEmpty(capacityLoadVOS)){
            return new ArrayList<>();
        }
        //获取每个关键工序对应的资源编码
        Set<String> resourceCodes = capacityLoadVOS.stream().map(CapacityLoadVO::getResourceCode).collect(Collectors.toSet());
        //查询关键工序产能供应数据
        Map<String, Object> params = new HashMap<>();
        params.put("versionId", CapacityBalanceTypeEnum.WEEK.getCode());
        params.put("resourceCodeList",resourceCodes);
        List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOList = capacitySupplyRelationshipDao.selectByParams(params);
        log.info("产能负荷数据条数{}，产能供应数据条数{}", capacityLoadVOS.size(), capacitySupplyRelationshipPOList.size());
        // 获取所有对应的成品物料编码集合 sourceProductCode
        List<String> capacityProductCodes = null;
        if (StringUtils.isNotBlank(resourceCode)){
            capacityProductCodes = capacitySupplyRelationshipPOList.stream().filter(t -> resourceCode.equals(t.getResourceCode())).map(CapacitySupplyRelationshipPO::getSourceProductCode).distinct().collect(Collectors.toList());
        } else {
            capacityProductCodes = capacitySupplyRelationshipPOList.stream().map(CapacitySupplyRelationshipPO::getSourceProductCode).distinct().collect(Collectors.toList());
        }
        //获取当前租户对应的销售组织 SystemHolder.getTenantCode()
        List<Scenario> scenarios = ipsNewFeign.selectDefaultByTenantId(SystemHolder.getTenantCode());
        //获取销售组织的物料对象集合 SystemHolder.getScenario()
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), ImmutableMap.of("productCodes", capacityProductCodes));
        List<NewProductStockPointVO> saleNewProductStockPointVOS = newProductStockPointVOS.stream().filter(t -> t.getOrganizationId().equals(scenarios.get(0).getAlternativeColumn())).collect(Collectors.toList());
        List<NewProductStockPointVO> productNewProductStockPointVOS = newProductStockPointVOS.stream().filter(t -> !t.getOrganizationId().equals(scenarios.get(0).getAlternativeColumn())).collect(Collectors.toList());
        // 获取产能负荷中最近的时间
        Date startDate = capacityLoadVOS.stream().min(Comparator.comparing(CapacityLoadVO::getForecastTime)).map(CapacityLoadVO::getForecastTime).orElse(new Date());
        // 将产能供应数据按照时间周分类
        Map<String, List<CapacitySupplyRelationshipPO>> suppluRelationMap = new HashMap<>();
        // 获取每个时间周的数据 比如2025-04-14~2025-04-20
        Set<String> weekSet = new HashSet<>();
        for (CapacitySupplyRelationshipPO po : capacitySupplyRelationshipPOList) {
            String key = getKey(po.getSupplyTime(), startDate, DateUtils.COMMON_DATE_STR3);
            if (!suppluRelationMap.containsKey(key)){
                List<CapacitySupplyRelationshipPO> list = new ArrayList<>();
                list.add(po);
                suppluRelationMap.put(key, list);
            } else {
                suppluRelationMap.get(key).add(po);
            }
            weekSet.add(key);
        }
        //获取时间周的最近和最远的时间
        String minDate = weekSet.stream().min(String::compareTo).get().split("~")[0];
        String maxDate =weekSet.stream().max(String::compareTo).get().split("~")[1] + " 23:59:59";
        //获取发货计划表数据 其中demandQuantity为发货计划数量（还未区分产线）
        Map<String, Object> currentQueryMap = ImmutableMap.of("productCodes", capacityProductCodes,"startTime",minDate,"endTime",maxDate);
        List<DeliveryPlanVO2> deliveryPlanVO2s = dfpFeign.selectDeliveryPlanPublishedByParams(SystemHolder.getScenario(), currentQueryMap);
        //返回至前端的集合数据
        List<CapacityLoadVO> newDataList = new ArrayList<>();

        String specialStockPoint = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT"));
        String specialStockPoint2 = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT_S2").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT_S2"));
        //todo 获取该产线的详情数据
        if (StringUtils.isNotEmpty(resourceCode)){
            List<String> productCodeList = deliveryPlanVO2s.stream().map(DeliveryPlanVO2::getProductCode).distinct().collect(Collectors.toList());
            List<NewStockPointVO> newStockPoints = masterPlanInventorySupport.getStockPoint();
            List<String> bcStockPointList = newStockPoints.stream().map(NewStockPointVO::getStockPointCode)
                    .collect(Collectors.toList());

            Map<String, List<BomRoutingStepInputVO>> semiBomMap = masterPlanInventorySupport.getSemiBomMap(productCodeList);
            List<InventoryBatchDetailVO> inventoryBatchDetail = masterPlanInventorySupport.getInventoryBatchDetail(bcStockPointList, productCodeList);
            Map<String, List<InventoryBatchDetailVO>> finishInventoryMap = masterPlanInventorySupport.getFinishInventoryMap(inventoryBatchDetail, newStockPoints);
            Map<String, List<InventoryBatchDetailVO>> operationInventoryMap = masterPlanInventorySupport.getOperationInventoryMap(inventoryBatchDetail, newStockPoints);
            Map<String, List<InventoryBatchDetailVO>> semiFinishInventoryMap = masterPlanInventorySupport.getSemiFinishInventoryMap(inventoryBatchDetail, newStockPoints);
            Map<String, SubInventoryCargoLocationVO> cargoLocationMap = masterPlanInventorySupport.getSubInventoryCargoLocationMap(inventoryBatchDetail);
            Map<String, String> stepMap = masterPlanInventorySupport.getStepMap();
            List<String> productIds = semiBomMap.values().stream().flatMap(Collection::stream).map(BomRoutingStepInputVO::getInputProductId).distinct()
                    .collect(Collectors.toList());

            List<NewProductStockPointVO> inputProductStockPoints = newMdsFeign.selectProductStockPointByIds(SystemHolder.getScenario(),
                    productIds);
            productNewProductStockPointVOS.addAll(inputProductStockPoints);
            Map<String, NewProductStockPointVO> productionProductCodeMap = productNewProductStockPointVOS.stream()
                    .distinct().collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(),
                            (u, v) -> u));
            List<InventoryBatchDetailVO> externalInventoryBatchDetailVOS = capacityBalanceDao.selectExternalInventoryByProductCodeList(productCodeList);
            Map<String, String> externalInventoryMap = externalInventoryBatchDetailVOS
                    .stream().collect(Collectors.toMap(InventoryBatchDetailVO::getProductCode,
                            InventoryBatchDetailVO::getCurrentQuantity, (u, v) -> u));

            //对时间进行排序，从小至大
            List<String> weekList = new ArrayList(weekSet);
            Collections.sort(weekList);
            //周 个数
            int size = weekList.size();

            //按照编码进行分类
            Map<String,List<DeliveryPlanVO2>> collect1 = deliveryPlanVO2s.stream().collect(Collectors.groupingBy(DeliveryPlanVO2::getProductCode));
            Map<String,List<String>> productMap = new HashMap();
            //遍历多少条编码，且发货量默认设置为0
            for (String itemProductCode : collect1.keySet()) {
                productMap.put(itemProductCode, Collections.nCopies(size, 0 + ""));
            }
            for (int i = 0; i < size; i++) {
                String weekDateStr = weekList.get(i);
                String[] parts = weekDateStr.split("~");
                Date beginDate = DateUtils.stringToDate(parts[0],DateUtils.COMMON_DATE_STR3);
                Date endDate = DateUtils.stringToDate(parts[1] + " 23:59:59",DateUtils.COMMON_DATE_STR1);
                //weekDateStr代表这一周两个时间
                List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOS = suppluRelationMap.get(weekDateStr);
                if (!capacitySupplyRelationshipPOS.isEmpty()){
                    List<String> productCodes = capacitySupplyRelationshipPOS.stream()
                            .map(CapacitySupplyRelationshipPO::getSourceProductCode).distinct().collect(Collectors.toList());
                    //1、先按时间分组 这一周的发货计划
                    List<DeliveryPlanVO2> weekData = deliveryPlanVO2s.stream().filter(item -> productCodes.contains(item.getProductCode()))
                            .filter(item -> item.getDemandTime().compareTo(beginDate) >= 0)
                            .filter(item -> item.getDemandTime().compareTo(endDate) <= 0).collect(Collectors.toList());
                    //2、再按编码分组
                    Map<String, List<DeliveryPlanVO2>> collect = weekData.stream().collect(Collectors.groupingBy(DeliveryPlanVO2::getProductCode));
                    //遍历数据
                    for (Map.Entry<String, List<DeliveryPlanVO2>> entry : collect.entrySet()) {
                        String key = entry.getKey(); //key 为物料编码
                        List<String> originalList = productMap.get(key); //获取原始列表 此时发货计划量都是0
                        //创建原始列表的可变副本
                        List<String> newList = new ArrayList<>(originalList);
                        //每个物料编码在产线中根据供应数量比列获取发货计划量
                        List<DeliveryPlanVO2> value = entry.getValue();
                        Integer totalSupply = getSupplyProportionData(value, key, capacitySupplyRelationshipPOS, resourceCode);
                        newList.set(i, totalSupply.toString());
                        productMap.put(key, newList);
                    }
                }
            }
            // 删除值全为0的键
            productMap.entrySet().removeIf(entry -> {
                List<String> list = entry.getValue();
                return list != null && !list.isEmpty() && list.stream().allMatch(i -> "0".equals(i));
            });
            List<Map<String,Object>> productDatalist = new ArrayList<>();
            //按照编码分组
            for (Map.Entry<String,List<String>> entry :productMap.entrySet()) {
                String productCode = entry.getKey();
                List<String> originalList = entry.getValue();
                Map<String,Object> sonMap = new HashMap<>();
                //物料编码
                sonMap.put("productCode",productCode);
                //物料编码中每周的发货计划量
                for (int i = 0; i < size; i++) {
                    sonMap.put(weekList.get(i),originalList.get(i));
                }
                //设置计划员
                Optional<NewProductStockPointVO> first = saleNewProductStockPointVOS.stream().filter(item -> productCode.equals(item.getProductCode())).findFirst();
                sonMap.put("orderPlanner",first.isPresent() ? first.get().getOrderPlanner(): "");

                inventoryData(productionProductCodeMap, productCode, semiBomMap, semiFinishInventoryMap, cargoLocationMap,
                        stepMap, operationInventoryMap, finishInventoryMap, externalInventoryMap, sonMap,specialStockPoint,specialStockPoint2);
                productDatalist.add(sonMap);
            }
            CapacityLoadVO capacityLoadVO = new CapacityLoadVO();
            capacityLoadVO.setProductDatalist(productDatalist);
            newDataList.add(capacityLoadVO);
            return newDataList;
        }
        // todo 获取负荷的总和数据
        //产能负荷按照产线分类
        Map<String, List<CapacityLoadVO>> map = capacityLoadVOS.stream().collect(Collectors.groupingBy(CapacityLoadVO::getResourceCode));
        //获取（主资源）产线数据
        Set<String> keys = map.keySet();
        List<PhysicalResourceVO> physicalResourceVOS =
                newMdsFeign.selectPhysicalResourceByParams(SystemHolder.getScenario(), ImmutableMap.of("physicalResourceCodes", keys));
        //根据产线获取产线效率
        Map<String, BigDecimal> productionEfficiencyMap = physicalResourceVOS.stream()
                .collect(Collectors.toMap(PhysicalResourceVO::getPhysicalResourceCode, PhysicalResourceVO::getProductionEfficiency));
        for (Map.Entry<String, List<CapacityLoadVO>> entry : map.entrySet()) {
            //判断负荷率是否合格
            boolean isOk = false;
            CapacityLoadVO vo = new CapacityLoadVO();
            String currentResourceCode = entry.getKey();//为产线编码
            vo.setResourceCode(currentResourceCode);
            List<CapacityLoadVO2> vo2List = new ArrayList<>();
            //产能负荷中再按照时间分组
            Map<String, List<CapacityLoadVO>> listMap = entry.getValue().stream().collect(Collectors.groupingBy(t -> getKey(t.getForecastTime(), startDate, DateUtils.COMMON_DATE_STR3)));
            for (Map.Entry<String, List<CapacityLoadVO>> listEntry : listMap.entrySet()) {
                String weekDateStr = listEntry.getKey(); //为时间周
                CapacityLoadVO2 capacityLoadVO2 = new CapacityLoadVO2();
                //标题 时间范围
                capacityLoadVO2.setForecastWeekTime(weekDateStr);
                // 周总产能
                BigDecimal weekDemandQuantity = getSum(listEntry.getValue(), CapacityLoadVO::getDemandQuantity);
                // 周总产能
                BigDecimal weekAvailableCapacity = getSum(listEntry.getValue(), CapacityLoadVO::getAvailableCapacity);
                // 周总消耗产能
                BigDecimal weekProductionCapacity = getSum(listEntry.getValue(), CapacityLoadVO::getProductionCapacity);

                //周负荷率= 周占用产能 / 周可用产能
                BigDecimal divide = weekProductionCapacity.divide(weekAvailableCapacity, 2, RoundingMode.HALF_UP);
                if (divide.compareTo(divide1) >0 && !isOk){
                    isOk = true;
                }
                //赋值-周排产量
                capacityLoadVO2.setDemandQuantity(weekDemandQuantity);
                //赋值-周负荷率
                capacityLoadVO2.setCapacityUtilization(divide);
                //赋值-周均产能
                // 获取每周的平均节拍
                List<CapacitySupplyRelationshipPO> value = suppluRelationMap.get(weekDateStr).stream().filter(item -> currentResourceCode.equals(item.getResourceCode())).collect(Collectors.toList());
                //平均节拍=sum(需求量*节拍)/sum(需求量)
                BigDecimal sumBeat = value.stream().map(t -> t.getDemandQuantity().multiply(new BigDecimal(t.getBeat()))).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal sumDemandQuantity = value.stream().map(CapacitySupplyRelationshipPO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal averageBeat = sumBeat.divide(sumDemandQuantity, 2, RoundingMode.HALF_UP);
                capacityLoadVO2.setAverageBeat(averageBeat);
                //根据负荷率返回颜色
                capacityLoadVO2.setColor(getColor(capacityLoadVO2.getCapacityUtilization()));

                //获取当前周的供应关系数据 多产线
                List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOS = suppluRelationMap.get(weekDateStr);
                //获取物料以及对应的数据和发布人
                if (!capacitySupplyRelationshipPOS.isEmpty()){
                    // 该产线中这一周的物料编码 通过编码和时间查询发货计划表数据
                    List<String> productCodes = capacitySupplyRelationshipPOS.stream()
                            .filter(item -> currentResourceCode.equals(item.getResourceCode()))
                            .map(CapacitySupplyRelationshipPO::getSourceProductCode).distinct().collect(Collectors.toList());
                    String[] split = weekDateStr.split("~");
                    Date beforeDate = DateUtils.stringToDate(split[0], DateUtils.COMMON_DATE_STR3);
                    Date afterDate = DateUtils.stringToDate(split[1] + " 23:59:59", DateUtils.COMMON_DATE_STR1);
                    //获取该时间段中这些物料的发货计划
                    List<DeliveryPlanVO2> deliveryPlanVO2List = deliveryPlanVO2s.stream()
                            .filter(item -> productCodes.contains(item.getProductCode()))
                            .filter(item -> item.getDemandTime().compareTo(beforeDate) >= 0)
                            .filter(item -> item.getDemandTime().compareTo(afterDate) <= 0).collect(Collectors.toList());
                    //按照物料编码分组
                    Map<String, List<DeliveryPlanVO2>> collect = deliveryPlanVO2List.stream().collect(Collectors.groupingBy(DeliveryPlanVO2::getProductCode));
                    //获取物料因供货数量比列得到的发货计划数
                    Integer sum = 0;
                    for (Map.Entry<String, List<DeliveryPlanVO2>> productEntry : collect.entrySet()) {
                        String productCode = productEntry.getKey(); //key 为物料编码
                        //每个编码在产线中比列根据供应来计算
                        List<DeliveryPlanVO2> productValue = productEntry.getValue();
                        Integer totalSupply = getSupplyProportionData(productValue, productCode, capacitySupplyRelationshipPOS, currentResourceCode);
                        sum = sum + totalSupply;
                    }
                    capacityLoadVO2.setTotalProductCount(sum);
                }
                vo2List.add(capacityLoadVO2);
            }
            if (isOk){
                //周均产能
                BigDecimal divide = vo2List.stream().map(CapacityLoadVO2::getAverageBeat).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(vo2List.size()), 2, RoundingMode.HALF_UP);
                if (productionEfficiencyMap.containsKey(currentResourceCode)){
                    BigDecimal divide2 = new BigDecimal(7 * 24 * 3600).multiply(productionEfficiencyMap.get(currentResourceCode)).divide(divide,0, RoundingMode.CEILING);
                    vo.setWeekAverageBeat(divide2);
                } else {
                    BigDecimal divide2 = new BigDecimal(7 * 24 * 3600 * 0.8).divide(divide,0, RoundingMode.CEILING);
                    vo.setWeekAverageBeat(divide2);
                }
                //按月份升序排序
                vo2List.sort(Comparator.comparing(CapacityLoadVO2::getForecastWeekTime));
                vo.setCapacityLoadVO2List(vo2List);
                newDataList.add(vo);
            }
        }

        return newDataList.stream().sorted(Comparator.comparing(t->t.getOperationCode()+t.getResourceCode())).collect(Collectors.toList());

    }

    /**
     * 通过供应比例获取发货计划量
     *
     * @param value 发货计划集合对象
     * @param productCode 物料编码
     * @param capacitySupplyRelationshipPOS 供应关系集合对象
     * @param resourceCode 产线编码
     * @return
     */
    private Integer getSupplyProportionData(List<DeliveryPlanVO2> value,
                                            String productCode,
                                            List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOS,
                                            String resourceCode) {
        //获取这一周的所有发货计划量
        int sum = value.stream().mapToInt(DeliveryPlanVO2::getDemandQuantity).sum();
        //获取当前编码的产能供应关系
        List<CapacitySupplyRelationshipPO> currentCapacitySupplyRelationshipPOS =
                capacitySupplyRelationshipPOS.stream().filter(item ->
                        productCode.equals(item.getSourceProductCode())).collect(Collectors.toList());
        //获取整体的供应比例（分母）
        BigDecimal totalSum = currentCapacitySupplyRelationshipPOS.stream()
                .map(CapacitySupplyRelationshipPO::getSupplyQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 防止除以零
        if (totalSum.compareTo(BigDecimal.ZERO) <= 0) {
            return 0;
        }
        //获取这一周的供应比例（分子）
        BigDecimal sum2 = currentCapacitySupplyRelationshipPOS.stream()
                .filter(item -> resourceCode.equals(item.getResourceCode()))
                .map(CapacitySupplyRelationshipPO::getSupplyQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal multiply = sum2.divide(totalSum, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(sum));
        return multiply.setScale(0, RoundingMode.HALF_UP).intValue();
    }

    /**
     * 拼接关键工序
     * @param standardStepVOS
     * @return
     */
    private String concatKeyStandardStep(List<StandardStepVO> standardStepVOS) {
        StringBuilder stringBuilder = new StringBuilder();
        //动态拼接字段
        for (int i = 0; i < standardStepVOS.size(); i++) {
            StandardStepVO standardStepVO = standardStepVOS.get(i);
            if (i > 0){
                stringBuilder.append(" OR ");
            }
            stringBuilder.append("(plant_code = '")
                    .append(standardStepVO.getStockPointCode())
                    .append("' AND operation_code = '")
                    .append(standardStepVO.getStandardStepCode())
                    .append("')");
        }
        // 拼接完整查询条件
        String queryCriteriaParam = "";
        if (!standardStepVOS.isEmpty()) {
            queryCriteriaParam = "AND (" + stringBuilder.toString() + ") AND version_id = 'WEEK'";
        }
        return queryCriteriaParam;
    }

    /**
     * 组装库存相关数据
     */
    private static void inventoryData(Map<String, NewProductStockPointVO> productionProductCodeMap, String productCode,
                                      Map<String, List<BomRoutingStepInputVO>> semiBomMap,
                                      Map<String, List<InventoryBatchDetailVO>> semiFinishInventoryMap,
                                      Map<String, SubInventoryCargoLocationVO> cargoLocationMap,
                                      Map<String, String> stepMap,
                                      Map<String, List<InventoryBatchDetailVO>> operationInventoryMap,
                                      Map<String, List<InventoryBatchDetailVO>> finishInventoryMap,
                                      Map<String, String> externalInventoryMap,
                                      Map<String, Object> sonMap,
                                      String specialStockPoint, String specialStockPoint2) {
        NewProductStockPointVO productItem = productionProductCodeMap.get(productCode);

        String semiInventory = "";
        if(semiBomMap.containsKey(productCode)){
            List<BomRoutingStepInputVO> semiBomList = semiBomMap.get(productCode);
            List<Double> semiValue = new ArrayList<>();
            for (BomRoutingStepInputVO bomRoutingStepInputVO : semiBomList) {
                String semiProductCode = bomRoutingStepInputVO.getProductCode();
                // 半品物料
                NewProductStockPointVO semiProductCodeItem = productionProductCodeMap.get(semiProductCode);
                if(Objects.isNull(semiProductCodeItem)){
                    continue;
                }
                String semiProductCodeItemProductCode = semiProductCodeItem.getProductCode();
                String stockPointCode = semiProductCodeItem.getStockPointCode();
                String semiKey = stockPointCode + "-" + semiProductCodeItemProductCode;
                // 维护产品编码对应的半品库存
                List<InventoryBatchDetailVO> semiFinishList = MasterPlanServiceImpl.getFinishInventory(semiFinishInventoryMap
                        .get(semiKey), cargoLocationMap);
                BigDecimal semiFinishInventory = semiFinishList.stream().map(t ->
                        new BigDecimal(t.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
                double semiBomInventory = MasterPlanServiceImpl.getSemiBomInventory(semiProductCodeItem, stepMap,
                        operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, productCode);
                semiBomInventory = semiBomInventory + semiFinishInventory.doubleValue();
                semiValue.add(semiBomInventory);
            }
            // 发货计划总览的成型后库存，应该是大小片各自的半品库存，然后取小
            if(CollectionUtils.isNotEmpty(semiValue)){
                semiValue.sort(Comparator.comparing(Double::doubleValue));
                semiInventory = new BigDecimal(String.valueOf(semiValue.get(0))).stripTrailingZeros().toPlainString();
            }
        }else{
            double semiBomInventory = MasterPlanServiceImpl.getSemiBomInventory(productItem, stepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, productCode);
            semiInventory = new BigDecimal(String.valueOf(semiBomInventory)).stripTrailingZeros().toPlainString();
        }
        BigDecimal finishedInventory = MasterPlanServiceImpl.getFinishInventory(finishInventoryMap.get(productCode), cargoLocationMap)
                .stream().map(t ->
                        new BigDecimal(t.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
        String externalInventory = "0";
        if (externalInventoryMap.containsKey(productCode)) {
            externalInventory = new BigDecimal(String.valueOf(externalInventoryMap.get(productCode))).stripTrailingZeros().toPlainString();
        }
        sonMap.put("finishInventory", finishedInventory);
        sonMap.put("semiInventory", semiInventory);
        sonMap.put("externalInventory", externalInventory);
    }

    @SneakyThrows
    @Override
    public void exportData(HttpServletResponse response, Integer utilization) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "超负荷预警表");
        //数据
        List<List<String>> dataList = new ArrayList<>();
        //获取动态列
        List<String> forecastMonthList = getForecastMonthList(dataList,utilization);
        //初始化头
        List<List<String>> headers = initHeaders(forecastMonthList);
        // 设置单元格合并
        EasyExcel.write(out)
                .head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .sheet("Sheet1")
                .doWrite(dataList);
    }

    /**
     * 初始化头
     * @param forecastMonthList
     * @return
     */
    private static List<List<String>> initHeaders(List<String> forecastMonthList) {
        List<List<String>> headers = new ArrayList<>();
        String mainTitle = "超负荷预警表";
        headers.add(Lists.newArrayList(mainTitle, "产线"));
        headers.add(Lists.newArrayList(mainTitle, "周平均节拍"));
        headers.add(Lists.newArrayList(mainTitle, "项"));
        for (String month : forecastMonthList) {
            headers.add(Lists.newArrayList(mainTitle, month));
        }
        headers.add(Lists.newArrayList(mainTitle, "计划员"));
        return headers;
    }

    /**
     * 获取数据
     *
     * @param dataList
     * @param utilization
     * @return
     */
    public List<String> getForecastMonthList(List<List<String>> dataList, Integer utilization) {
        //获取所有用户
        List<User> users = ipsNewFeign.userList();
        //是否准确查询产线数据
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(SystemHolder.getScenario());
        standardStepVOS = standardStepVOS.stream()
                .filter(item->"FORMING_PROCESS".equals(item.getStandardStepType()))
                .collect(Collectors.toList());
        if (standardStepVOS.isEmpty()){
            throw new RuntimeException("未找到该租户下的关键工序数据，请进行维护");
        }
        String queryCriteriaParam = this.concatKeyStandardStep(standardStepVOS);
        // 获取关键工序的产能负荷表数据
        List<CapacityLoadVO> capacityLoadVOS = capacityLoadDao.selectByCondition(null, queryCriteriaParam);
        //没有数据就返回空集合
        if (CollectionUtils.isEmpty(capacityLoadVOS)){
            return new ArrayList<>();
        }
        //获取每个关键工序对应的资源编码
        Set<String> resourceCodes = capacityLoadVOS.stream().map(CapacityLoadVO::getResourceCode).collect(Collectors.toSet());
        //查询关键工序产能供应数据
        Map<String, Object> params = new HashMap<>();
        params.put("versionId", CapacityBalanceTypeEnum.WEEK.getCode());
        params.put("resourceCodeList",resourceCodes);
        List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOList = capacitySupplyRelationshipDao.selectByParams(params);
        log.info("产能负荷数据条数{}，产能供应数据条数{}", capacityLoadVOS.size(), capacitySupplyRelationshipPOList.size());
        // 获取所有对应的物料编码集合
        List<String> capacityProductCodes = capacitySupplyRelationshipPOList.stream().map(CapacitySupplyRelationshipPO::getSourceProductCode).distinct().collect(Collectors.toList());
        //获取当前租户对应的销售组织 SystemHolder.getTenantCode()
        List<Scenario> scenarios = ipsNewFeign.selectDefaultByTenantId(SystemHolder.getTenantCode());
        //获取销售组织的物料对象集合 SystemHolder.getScenario()
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), ImmutableMap.of("productCodes", capacityProductCodes, "organizationId", scenarios.get(0).getAlternativeColumn()));
        // 获取产能负荷中最近的时间
        Date startDate = getMin(capacityLoadVOS, CapacityLoadVO::getForecastTime, Comparator.comparing(Date::getTime));
        // 将产能供应数据按照时间周分类
        Map<String, List<CapacitySupplyRelationshipPO>> suppluRelationMap = new HashMap<>();
        // 获取每个时间周的数据 比如2025-04-14~2025-04-20
        Set<String> weekSet = new HashSet<>();
        for (CapacitySupplyRelationshipPO po : capacitySupplyRelationshipPOList) {
            String key = getKey(po.getSupplyTime(), startDate, DateUtils.COMMON_DATE_STR3);
            if (!suppluRelationMap.containsKey(key)){
                List<CapacitySupplyRelationshipPO> list = new ArrayList<>();
                list.add(po);
                suppluRelationMap.put(key, list);
            }else {
                suppluRelationMap.get(key).add(po);
            }
            weekSet.add(key);
        }
        //给时间排序
        List<String> weekList = new ArrayList(weekSet);
        Collections.sort(weekList);
        //获取时间周的最近和最远的时间
        String minDate = weekSet.stream().min(String::compareTo).get().split("~")[0];
        String maxDate =weekSet.stream().max(String::compareTo).get().split("~")[1] + " 23:59:59";
        //获取发货计划表数据 就这四五周的数据
        Map<String, Object> currentQueryMap = ImmutableMap.of("productCodes", capacityProductCodes,"startTime",minDate,"endTime",maxDate);
        List<DeliveryPlanVO2> deliveryPlanVO2s = dfpFeign.selectDeliveryPlanPublishedByParams(SystemHolder.getScenario(), currentQueryMap);

        //负荷率标准
        BigDecimal divide1 = new BigDecimal(utilization).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        //产能负荷按照产线分类
        Map<String, List<CapacityLoadVO>> orignMap = capacityLoadVOS.stream().collect(Collectors.groupingBy(CapacityLoadVO::getResourceCode));
        Map<String, List<CapacityLoadVO>> map = new TreeMap<>(orignMap);
        //获取（主资源）产线数据
        Set<String> keys = map.keySet();
        List<PhysicalResourceVO> physicalResourceVOS =
                newMdsFeign.selectPhysicalResourceByParams(SystemHolder.getScenario(), ImmutableMap.of("physicalResourceCodes", keys));
        //根据产线获取产线效率
        Map<String, BigDecimal> productionEfficiencyMap = physicalResourceVOS.stream()
                .collect(Collectors.toMap(PhysicalResourceVO::getPhysicalResourceCode, PhysicalResourceVO::getProductionEfficiency));
        for (Map.Entry<String, List<CapacityLoadVO>> entry : map.entrySet()) {
            //判断负荷率是否合格
            boolean isOk = false;
            //当前产线
            String currentResourceCode = entry.getKey();
            //获取负荷率和排产量的集合
            List<String> utilizationList = new ArrayList<>();
            utilizationList.add(currentResourceCode);
            utilizationList.add("");
            utilizationList.add("负荷率");
            List<String> schedulingList = new ArrayList<>();
            schedulingList.add(currentResourceCode);
            schedulingList.add("");
            schedulingList.add("排产量");
            //设置该产线的均产能之和以及拥有数据的周数
            BigDecimal sumTotalBeat = new BigDecimal(0);
            int weekCount = 0;
            //将该产线下已有的数据按照时间分组
            Map<String, List<CapacityLoadVO>> listMap = entry.getValue().stream().collect(Collectors.groupingBy(t -> getKey(t.getForecastTime(), startDate, DateUtils.COMMON_DATE_STR3)));
            for (String week : weekList) {
                List<CapacityLoadVO> capacityLoadVOS1 = listMap.get(week);
                //表示在这一周没有数据
                if (capacityLoadVOS1 == null || capacityLoadVOS1.isEmpty()) {
                    utilizationList.add("0%");//负荷率为空
                    schedulingList.add("0");//排产量为空
                } else {
                    //周可用产能
                    BigDecimal weekAvailableCapacity = getSum(capacityLoadVOS1, CapacityLoadVO::getAvailableCapacity);
                    //周占用产能
                    BigDecimal weekProductionCapacity = getSum(capacityLoadVOS1, CapacityLoadVO::getProductionCapacity);
                    //周负荷率= 周占用产能 / 周可用产能
                    BigDecimal divide = weekProductionCapacity.divide(weekAvailableCapacity, 2, RoundingMode.HALF_UP);
                    if (divide.compareTo(divide1) >0 && !isOk){
                        isOk = true;
                    }
                    //赋值-周负荷率
                    utilizationList.add(divide.multiply(new BigDecimal(100)) + "%");

                    //周排产量
                    BigDecimal weekDemandQuantity = capacityLoadVOS1.stream().map(CapacityLoadVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    schedulingList.add(weekDemandQuantity.toString());

                    // 获取每周的平均节拍 平均节拍=sum(需求量*节拍)/sum(需求量)
                    List<CapacitySupplyRelationshipPO> value = suppluRelationMap.get(week).stream().filter(item -> entry.getKey().equals(item.getResourceCode())).collect(Collectors.toList());
                    BigDecimal sumBeat = value.stream().map(t -> t.getDemandQuantity().multiply(new BigDecimal(t.getBeat()))).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal sumDemandQuantity = value.stream().map(CapacitySupplyRelationshipPO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 累加每周的平均节拍
                    sumTotalBeat = sumTotalBeat.add(sumBeat.divide(sumDemandQuantity, 2, RoundingMode.HALF_UP));
                    weekCount++;
                }
            }
            if (isOk && weekCount > 0) {
                //有数据的周均产能
                BigDecimal weekBeat = sumTotalBeat.divide(new BigDecimal(weekCount), 2, RoundingMode.HALF_UP);
                if (productionEfficiencyMap.containsKey(currentResourceCode)) {
                    weekBeat = new BigDecimal(7 * 24 * 3600).multiply(productionEfficiencyMap.get(currentResourceCode)).divide(weekBeat, 0, RoundingMode.CEILING);
                } else {
                    weekBeat = new BigDecimal(7 * 24 * 3600 * 0.8).divide(weekBeat, 0, RoundingMode.CEILING);
                }
                utilizationList.set(1, weekBeat.toString());
                schedulingList.set(1, weekBeat.toString());
                //将负荷率和排产量的数据加入到集合中
                dataList.add(utilizationList);
                dataList.add(schedulingList);

                //发货计划量集合
                List<String> sumDemandQuantityList = new ArrayList<>();
                sumDemandQuantityList.add(currentResourceCode);
                sumDemandQuantityList.add(weekBeat.toString());
                sumDemandQuantityList.add("发货计划量");
                dataList.add(sumDemandQuantityList);
                //周 个数
                int size = weekList.size();
                //获取该产线下的供应数据
                List<CapacitySupplyRelationshipPO> currentCapacitySupplyRelationshipPO =
                        capacitySupplyRelationshipPOList.stream()
                                .filter(item -> currentResourceCode.equals(item.getResourceCode()))
                                .collect(Collectors.toList());
                //该产线下所有的物料编码
                List<String> currentResourceCodeToProductCodeList =
                        currentCapacitySupplyRelationshipPO.stream()
                                .map(CapacitySupplyRelationshipPO::getSourceProductCode).distinct().collect(Collectors.toList());
                //按照编码进行分类
                Map<String,List<DeliveryPlanVO2>> collect1 = deliveryPlanVO2s.stream()
                        .filter(item -> currentResourceCodeToProductCodeList.contains(item.getProductCode()))
                        .collect(Collectors.groupingBy(DeliveryPlanVO2::getProductCode));
                Map<String,List<String>> productMap = new HashMap();
                //遍历多少条编码，且发货量默认设置为0
                for (String itemProductCode : collect1.keySet()) {
                    productMap.put(itemProductCode, Collections.nCopies(size, 0 + ""));
                }
                for (int i = 0; i < size; i++) {
                    String weekDateStr = weekList.get(i);
                    String[] parts = weekDateStr.split("~");
                    Date beginDate = DateUtils.stringToDate(parts[0],DateUtils.COMMON_DATE_STR3);
                    Date endDate = DateUtils.stringToDate(parts[1] + " 23:59:59",DateUtils.COMMON_DATE_STR1);
                    //weekDateStr代表这一周两个时间
                    List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOS = suppluRelationMap.get(weekDateStr);
                    if (!capacitySupplyRelationshipPOS.isEmpty()){
                        List<String> productCodes = capacitySupplyRelationshipPOS.stream()
                                .filter(item -> currentResourceCode.equals(item.getResourceCode()))
                                .map(CapacitySupplyRelationshipPO::getSourceProductCode).distinct().collect(Collectors.toList());
                        //这一周的发货计划
                        List<DeliveryPlanVO2> weekData = deliveryPlanVO2s.stream().filter(item -> productCodes.contains(item.getProductCode()))
                                .filter(item -> item.getDemandTime().compareTo(beginDate) >= 0)
                                .filter(item -> item.getDemandTime().compareTo(endDate) <= 0).collect(Collectors.toList());
                        //按照编码分类
                        Map<String, List<DeliveryPlanVO2>> collect = weekData.stream().collect(Collectors.groupingBy(DeliveryPlanVO2::getProductCode));
                        //遍历数据
                        Integer sum = 0;
                        for (Map.Entry<String, List<DeliveryPlanVO2>> productCodeEntry : collect.entrySet()) {
                            //物料编码
                            String key = productCodeEntry.getKey();
                            List<String> originalList = productMap.get(key);
                            //创建原始列表的可变副本
                            List<String> newList = new ArrayList<>(originalList);
                            Integer supplyProportionData = getSupplyProportionData(productCodeEntry.getValue(),key,capacitySupplyRelationshipPOS,currentResourceCode);
                            sum += supplyProportionData;
                            newList.set(i, supplyProportionData.toString());
                            productMap.put(key, newList);
                        }
                        sumDemandQuantityList.add(sum.toString());
                    }
                }
                // 删除值全为0的键
                productMap.entrySet().removeIf(productCodeEntry -> {
                    List<String> list = productCodeEntry.getValue();
                    return list != null && !list.isEmpty() && list.stream().allMatch(i -> "0".equals(i));
                });
                for (Map.Entry<String,List<String>> productEntry :productMap.entrySet()) {
                    String key = productEntry.getKey();
                    List<String> originalList = productEntry.getValue();
                    //塞值
                    List<String> detailProductList = new ArrayList<>();
                    detailProductList.add(currentResourceCode);
                    detailProductList.add(weekBeat.toString());
                    detailProductList.add(key);
                    //物料编码中每周的发货计划量
                    for (String demandQuantity:originalList) {
                        detailProductList.add(demandQuantity);
                    }
                    //设置计划员
                    Optional<NewProductStockPointVO> first = newProductStockPointVOS.stream().filter(item -> key.equals(item.getProductCode())).findFirst();
                    if (first.isPresent()) {
                        Optional<User> first1 = users.stream().filter(item -> item.getId().equals(first.get().getOrderPlanner())).findFirst();
                        detailProductList.add(first1.isPresent() ? first1.get().getCnName() : "");
                    }
                    dataList.add(detailProductList);
                }
            }
        }
        return weekList;
    }

    @Override
    public void sendMessage(List<CapacityLoadDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }
        List<CollectionValueVO> roleTypeValues = ipsFeign.getByCollectionCode("ROLE_MANAGER");
        if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(roleTypeValues)) {
            throw new BusinessException("ROLE_MANAGER值集未配置");
        }
        List<String> managerNames = StreamUtils.columnToList(roleTypeValues, CollectionValueVO::getValueMeaning);
        List<String> managerUserIds = ipsFeign.selectUserByRoleNames(managerNames)
                .stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(managerUserIds)) {
            log.warn("周产能平衡计算消息发送-根据配置的角色没有找到对应的用户!");
            return;
        }

        Date startDate = dtoList.stream().min(Comparator.comparing(CapacityLoadDTO::getForecastTime))
                .map(CapacityLoadDTO::getForecastTime).orElse(new Date());
        Map<String, List<CapacityLoadDTO>> map = dtoList.stream()
                .collect(Collectors.groupingBy(t ->
                        t.getPlantCode() + "-" + t.getResourceGroupCode() + "-" + t.getResourceCode()));

        List<UserMessageDTO> userMessageDTOList = new ArrayList<>();
        for (Map.Entry<String, List<CapacityLoadDTO>> entry : map.entrySet()) {
            Map<String, List<CapacityLoadDTO>> listMap = entry.getValue().stream()
                    .collect(Collectors.groupingBy(t -> getKey(t.getForecastTime(), startDate, DateUtils.COMMON_DATE_STR3)));
            for (Map.Entry<String, List<CapacityLoadDTO>> listEntry : listMap.entrySet()) {
                String key = listEntry.getKey();
                // 周总产能
                BigDecimal weekAvailableCapacity = getSum(listEntry.getValue(), CapacityLoadDTO::getAvailableCapacity);
                // 周总消耗产能
                BigDecimal weekProductionCapacity = getSum(listEntry.getValue(), CapacityLoadDTO::getProductionCapacity);
                // 周负荷率
                BigDecimal divide = weekProductionCapacity.divide(weekAvailableCapacity, 2, RoundingMode.HALF_UP);
                String resourceCode = listEntry.getValue().get(0).getResourceCode();
                if (divide.compareTo(BigDecimal.ZERO) >= 0) {
                    String message = "周产能平衡超负荷告警:" +
                            resourceCode +
                            "产线，" +
                            key +
                            "产线产能超100%";
                    UserMessageDTO messageDTO = new UserMessageDTO();
                    messageDTO.setMessageContent(message);
                    userMessageDTOList.add(messageDTO);
                }
            }
        }
        List<UserMessageDTO> allUserMessageDTOList = new ArrayList<>();
        for (String managerUserId : managerUserIds) {
            for (UserMessageDTO userMessageDTO : userMessageDTOList) {
                String messageContent = userMessageDTO.getMessageContent();
                UserMessageDTO result = UserMessageDTO.builder()
                        .id(UUIDUtil.getUUID())
                        .userId(managerUserId)
                        .messageSource("系统消息")
                        .messageType(MessageTypeEnum.MY_MESSAGE.getCode())
                        .messageTitle("产能平衡超负荷告警")
                        .messageContent(messageContent)
                        .messageEmergency("3")
                        .readStatus(YesOrNoEnum.NO.getCode())
                        .build();
                allUserMessageDTOList.add(result);
            }
        }
        if (CollectionUtils.isNotEmpty(allUserMessageDTOList)) {
            log.info("周产能平衡超负荷告警消息发送开始..., 总条数{}", allUserMessageDTOList.size());
            userMessageUtils.sendMessage(allUserMessageDTOList);
            log.info("周产能平衡超负荷告警消息发送结束!");
        }
    }


    private static <T> BigDecimal getSum(Collection<T> collection, Function<T, BigDecimal> function) {
        return collection.stream().map(function).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private static <T, V> V getMin(Collection<T> collection, Function<T, V> getV, Comparator<V> comparator) {
        return collection.stream().filter(x -> Objects.nonNull(getV.apply(x))).map(getV).min(comparator).orElse(null);
    }


    @Override
    public void doLineEdit(CapacitySupplyRelationshipWeekEditorDTO dto) {
        // 参数校验
        lineEditCheck(dto);
        // 获取修改原数据
        CapacitySupplyRelationshipPO oldCapacitySupplyRelationshipPO = capacitySupplyRelationshipDao.selectByPrimaryKey(dto.getId());
        if (oldCapacitySupplyRelationshipPO == null) {
            throw new BusinessException("修改数据不存在");
        }
        // 特殊工艺校验
        capacitySupplyRelationshipService.checkSpecialOperation(oldCapacitySupplyRelationshipPO.getOperationCode());
        // 获取修改后产线
        RoutingStepResourceVO newRoutingStepResource =
                capacitySupplyRelationshipService.getNewRoutingStepResource(oldCapacitySupplyRelationshipPO.getRoutingStepId(),
                dto.getNewResourceId());
        if (newRoutingStepResource == null) {
            throw new BusinessException("选择的产线不存在");
        }
        List<String> needRefreshResources = new ArrayList<>();
        String newResourceCode = newRoutingStepResource.getPhysicalResourceCode();
        String newResourceName = newRoutingStepResource.getPhysicalResourceName();
        String resourceCode = oldCapacitySupplyRelationshipPO.getResourceCode();
        needRefreshResources.add(resourceCode);
        needRefreshResources.add(newResourceCode);
        needRefreshResources = needRefreshResources.stream().distinct().collect(Collectors.toList());

        //资源变化
        boolean resourceChange = !resourceCode.equals(newResourceCode);

        Date sourceSupplyTime = oldCapacitySupplyRelationshipPO.getSupplyTime();
        Date newSupplyTime = dto.getNewSupplyTime();
        //供应时间变化
        boolean supplyTimeChange = !sourceSupplyTime.equals(newSupplyTime);

        //数量变化，移动了一部分量到新的产线，或供应时间去
        boolean qtyChange = oldCapacitySupplyRelationshipPO.getSupplyQuantity().compareTo(dto.getNewSupplyQuantity()) != 0;

        if (!supplyTimeChange && !resourceChange && !qtyChange){
            return;
        }
        if (!supplyTimeChange && !resourceChange && qtyChange){
            throw new BusinessException("在资源，供应时间不变时，数量不允许修改！");
        }
        if (dto.getNewSupplyQuantity().compareTo(oldCapacitySupplyRelationshipPO.getSupplyQuantity()) > 0){
            throw new BusinessException("修改后的数量不能大于原数量！");
        }
        // 数量变化，说明移动了部分数量到别的日期或者产线上，生产一个新的周产能供应关系
        // 根据新的供应时间，资源，查询周产能供应关系，如果已存在数据，则把新的数量移动到新的产线上去，并把数量从原数据中减去，否则新增一条供应关系数据
        Map<String, Object> params = new HashMap<>();
        params.put("routingStepId", oldCapacitySupplyRelationshipPO.getRoutingStepId());
        params.put("forecastTime", DateUtils.dateToString(oldCapacitySupplyRelationshipPO.getForecastTime(), DateUtils.COMMON_DATE_STR3));
        params.put("supplyTime", DateUtils.dateToString(dto.getNewSupplyTime(), DateUtils.COMMON_DATE_STR3));
        params.put("resourceCode", newResourceCode);
        params.put("productCode", oldCapacitySupplyRelationshipPO.getProductCode());
        params.put("versionId", oldCapacitySupplyRelationshipPO.getVersionId());
        List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOS = capacitySupplyRelationshipDao.selectByParams(params);
        List<CapacitySupplyRelationshipPO> updateList = new ArrayList<>();
        List<CapacitySupplyRelationshipPO> insertList = new ArrayList<>();
        List<String> deleteList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(capacitySupplyRelationshipPOS)) {
            // 一般来说这种数据能查到的话只有一条
            CapacitySupplyRelationshipPO capacitySupplyRelationshipPO = capacitySupplyRelationshipPOS.get(0);
            if (qtyChange) {
                capacitySupplyRelationshipPO.setSupplyQuantity(capacitySupplyRelationshipPO.getSupplyQuantity().add(dto.getNewSupplyQuantity()));

                oldCapacitySupplyRelationshipPO.setSupplyQuantity(oldCapacitySupplyRelationshipPO.getSupplyQuantity().subtract(dto.getNewSupplyQuantity()));
                updateList.add(oldCapacitySupplyRelationshipPO);
            }else {
                // 整条数据换了供应时间或者资源, 删除原数据
                capacitySupplyRelationshipPO.setSupplyQuantity(dto.getNewSupplyQuantity());
                deleteList.add(oldCapacitySupplyRelationshipPO.getId());
            }
            updateList.add(capacitySupplyRelationshipPO);

        }else {
            // 没有现有的供应关系, 如果是数量变更，则新增一条数据，更新原数据的供应数量
            if (qtyChange) {
                CapacitySupplyRelationshipPO po = new CapacitySupplyRelationshipPO();
                BeanUtils.copyProperties(oldCapacitySupplyRelationshipPO, po);
                po.setId(UUIDUtil.getUUID());
                po.setSupplyQuantity(dto.getNewSupplyQuantity());
                po.setSupplyTime(dto.getNewSupplyTime());
                po.setResourceCode(newResourceCode);
                po.setResourceName(newResourceName);
                po.setBeat(newRoutingStepResource.getUnitProductionTime().toString());
                insertList.add(po);
                oldCapacitySupplyRelationshipPO.setSupplyQuantity(oldCapacitySupplyRelationshipPO.getSupplyQuantity().subtract(dto.getNewSupplyQuantity()));
                updateList.add(oldCapacitySupplyRelationshipPO);
            } else {
                // 只有资源变更或者供供应时间变更，则直接更新原数据的产线或者供应时间
                oldCapacitySupplyRelationshipPO.setResourceCode(newResourceCode);
                oldCapacitySupplyRelationshipPO.setResourceName(newResourceName);
                oldCapacitySupplyRelationshipPO.setSupplyTime(dto.getNewSupplyTime());
                updateList.add(oldCapacitySupplyRelationshipPO);
            }
        }
        // 数据库处理
        commonDataUpdate(insertList, updateList, deleteList);

        // 刷新负荷数据
        capacityLoadService.doRefreshCapacityLoad(needRefreshResources);
    }
    @Override
    public void doBatchEdit(CapacitySupplyRelationshipWeekEditorDTO dto) {
        batchEditCheck(dto);
        // 原数据
        List<CapacitySupplyRelationshipPO> sourceDataList = capacitySupplyRelationshipDao.selectByPrimaryKeys(dto.getIds());
        if (CollectionUtils.isEmpty(sourceDataList)) {
            throw new BusinessException("修改数据不存在");
        }
        List<CapacitySupplyRelationshipPO> outDataList = sourceDataList
                .stream().filter(t -> SupplyModelEnum.OUTSOURCED.getCode().equals(t.getSupplyModel()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outDataList)){
            throw new BusinessException("委外数据不允许批量修改");
        }
        List<String> operationCodeList = sourceDataList.stream().map(CapacitySupplyRelationshipPO::getOperationCode)
                .distinct().collect(Collectors.toList());
        if (operationCodeList.size()>1){
            throw new BusinessException("勾选数据工序必须相同");
        }
        //特殊工艺校验
        capacitySupplyRelationshipService.checkSpecialOperation(operationCodeList.get(0));

        List<PhysicalResourceVO> physicalResourceVOS = newMdsFeign.selectVOByPhysicalIds(Collections.singletonList(dto.getNewResourceId()));
        if (CollectionUtils.isEmpty(physicalResourceVOS)) {
            throw new BusinessException("未找到该资源");
        }
        PhysicalResourceVO physicalResourceVO = physicalResourceVOS.get(0);
        String newResourceCode = physicalResourceVO.getPhysicalResourceCode();
        String newResourceName = physicalResourceVO.getPhysicalResourceName();
        List<String> routingStepIds = sourceDataList.stream().map(CapacitySupplyRelationshipPO::getRoutingStepId).distinct().collect(Collectors.toList());
        List<RoutingStepResourceVO> stepResourceVOS = newMdsFeign.selectRoutingStepResourceByRoutingStepIds(SystemHolder.getScenario(),routingStepIds);
        Map<String, RoutingStepResourceVO> routingStepResourceVOMap = stepResourceVOS.stream()
                .filter(t->YesOrNoEnum.YES.getCode().equals(t.getEnabled()))
                .collect(Collectors.toMap(t -> t.getRoutingStepId() + "-" + t.getPhysicalResourceId(),
                        Function.identity(), (v1, v2) -> v2));
        // 获取已有供应关系数据
        List<String> forecastTimeList = sourceDataList.stream().map(t ->
                        DateUtils.dateToString(t.getForecastTime(), DateUtils.COMMON_DATE_STR3))
                .distinct().collect(Collectors.toList());
        List<String> productCodeList = sourceDataList.stream().map(CapacitySupplyRelationshipPO::getProductCode).collect(Collectors.toList());
        Map<String, Object> params = new HashMap<>();
        params.put("forecastTimeList", forecastTimeList);
        params.put("resourceCode", newResourceCode);
        params.put("productCodeList", productCodeList);
        params.put("versionId", CapacityBalanceTypeEnum.WEEK.getCode());
        List<CapacitySupplyRelationshipPO> existDataList = capacitySupplyRelationshipDao.selectByParams(params);

        List<String> needRefreshResources = sourceDataList.stream().map(CapacitySupplyRelationshipPO::getResourceCode)
                .distinct().collect(Collectors.toList());
        needRefreshResources.add(newResourceCode);
        needRefreshResources = needRefreshResources.stream().distinct().collect(Collectors.toList());

        List<CapacitySupplyRelationshipPO> updateList = new ArrayList<>();
        List<String> deleteList = new ArrayList<>();
        // 批量编辑只能改资源或者供应时间，不能改数量
        for (CapacitySupplyRelationshipPO relationshipPO : sourceDataList) {
            // 过滤是否已存在目标供应时间的数据
            CapacitySupplyRelationshipPO targetPO = existDataList.stream().filter(t ->
                    relationshipPO.getProductCode().equals(t.getProductCode())
                    && dto.getNewSupplyTime().equals(t.getSupplyTime())
                    && relationshipPO.getForecastTime().equals(t.getForecastTime())
                    && newResourceCode.equals(t.getResourceCode())
            ).findAny().orElse(null);
            // 在新的供应时间已经有供应关系数据了，直接把数量加过去，原数据删掉
            if (targetPO != null) {
                if (!relationshipPO.getId().equals(targetPO.getId())) {
                    targetPO.setSupplyQuantity(targetPO.getSupplyQuantity().add(relationshipPO.getSupplyQuantity()));
                    updateList.add(targetPO);
                    deleteList.add(relationshipPO.getId());
                }
            } else {
                // 新的供应时间没有数据，则直接把这条数据的供应时间和资源改成新的
                relationshipPO.setSupplyTime(dto.getNewSupplyTime());
                relationshipPO.setResourceCode(newResourceCode);
                relationshipPO.setResourceName(newResourceName);
                String key = String.join("-", relationshipPO.getRoutingStepId(), dto.getNewResourceId());
                RoutingStepResourceVO routingStepResourceVO = routingStepResourceVOMap.get(key);
                if (routingStepResourceVO == null) {
                    throw new BusinessException(relationshipPO.getProductCode() + "的候选资源中未找到该资源或该资源已失效");
                }
                relationshipPO.setBeat(routingStepResourceVO.getUnitProductionTime().toString());
                updateList.add(relationshipPO);
            }
        }

        // 数据库处理
        commonDataUpdate(null, updateList, deleteList);

        // 刷新负荷数据
        capacityLoadService.doRefreshCapacityLoad(needRefreshResources);
    }


    @Override
    public void doLockBatch(CapacitySupplyRelationshipWeekEditorDTO dto) {
        if (dto == null) {
            throw new BusinessException("参数不能为空");
        }
        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new BusinessException("勾选数据不能为空");
        }
        String lockStatus = dto.getNewResourceId();
        if (StringUtils.isEmpty(lockStatus)) {
            throw new BusinessException("锁定状态不能为空");
        }
        boolean exists = Arrays.stream(LockStatusEnum.values()).anyMatch(enumValue -> enumValue.getCode().equals(lockStatus));
        if (!exists){
            throw new BusinessException("锁状态值不存在");
        }
        List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOS = capacitySupplyRelationshipDao.selectByPrimaryKeys(dto.getIds());
        if (capacitySupplyRelationshipPOS.size() != dto.getIds().size()) {
            throw new BusinessException("数据不存在");
        }
        List<String> operationCodeList = capacitySupplyRelationshipPOS.stream().map(CapacitySupplyRelationshipPO::getOperationCode).distinct().collect(Collectors.toList());
        List<CollectionValueVO> specialTechnology = ipsFeign.getByCollectionCode("SPECIAL_TECHNOLOGY");
        List<String> specialTechnologyList = specialTechnology.stream().map(CollectionValueVO::getValueMeaning).collect(Collectors.toList());
        List<String> specialOperationCodeList = operationCodeList.stream().filter(specialTechnologyList::contains).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(specialOperationCodeList)){
            throw new BusinessException("特殊工艺不允许修改锁定状态");
        }
        capacitySupplyRelationshipDao.updateLockStatus(dto.getIds(), lockStatus);
    }

    /**
     * 参数校验
     * @param dto
     */
    private void lineEditCheck(CapacitySupplyRelationshipWeekEditorDTO dto) {
        if (dto == null) {
            throw new BusinessException("参数不能为空");
        }
        if (StringUtils.isBlank(dto.getId())) {
            throw new BusinessException("数据id不能为空");
        }
        if (dto.getNewSupplyQuantity() == null || dto.getNewSupplyQuantity().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("供应数量不能为空或小于等于0");
        }
        commonCheck(dto);
    }

    /**
     * 参数校验
     * @param dto
     */
    private void batchEditCheck(CapacitySupplyRelationshipWeekEditorDTO dto) {
        if (dto == null) {
            throw new BusinessException("参数不能为空");
        }
        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new BusinessException("数据id集合不能为空");
        }
        commonCheck(dto);
    }

    private void commonCheck(CapacitySupplyRelationshipWeekEditorDTO dto) {
        if (dto.getNewSupplyTime() == null) {
            throw new BusinessException("供应时间不能为空");
        }
        if (StringUtils.isBlank(dto.getNewResourceId())) {
            throw new BusinessException("产线不能为空");
        }
    }

    public void commonDataUpdate(List<CapacitySupplyRelationshipPO> insertList, List<CapacitySupplyRelationshipPO> updateList, List<String> deleteIdList) {
        if (CollectionUtils.isNotEmpty(insertList)){
            insertList.forEach(t -> {
                        t.setRule(CapacityBalanceRule.manualModification.getCode());
                        t.setForecastMonth(DateUtils.dateToString(t.getForecastTime(), DateUtils.YEAR_MONTH));
                        t.setSupplyMonth(DateUtils.dateToString(t.getSupplyTime(), DateUtils.YEAR_MONTH));
                    }
            );
            BasePOUtils.insertBatchFiller(insertList);
            capacitySupplyRelationshipDao.insertBatch(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)){
            updateList.forEach(t -> {
                        t.setRule(CapacityBalanceRule.manualModification.getCode());
                        t.setForecastMonth(DateUtils.dateToString(t.getForecastTime(), DateUtils.YEAR_MONTH));
                        t.setSupplyMonth(DateUtils.dateToString(t.getSupplyTime(), DateUtils.YEAR_MONTH));
                    }
            );
            BasePOUtils.updateBatchFiller(updateList);
            capacitySupplyRelationshipDao.updateBatch(updateList);
        }
        if (CollectionUtils.isNotEmpty(deleteIdList)){
            capacitySupplyRelationshipDao.deleteBatch(deleteIdList);
        }
    }
}
