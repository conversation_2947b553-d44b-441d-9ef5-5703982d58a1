package com.yhl.scp.mps.capacityBalance.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.mps.capacityBalance.dto.CapacitySupplyRelationshipWeekEditorDTO;
import com.yhl.scp.mps.capacityBalance.service.CapacityWeekBalanceService;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>CapacityWeekBalanceController</code>
 * <p>
 * 周产能平衡查询控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-25 14:20:48
 */
@Slf4j
@Api(tags = "周产能平衡查询控制器")
@RestController
@RequestMapping("capacityWeekBalance")
public class CapacityWeekBalanceController extends BaseController {

    @Resource
    private CapacityWeekBalanceService capacityWeekBalanceService;

    @ApiOperation(value = "设备负荷-设备-周次维度查询")
    @GetMapping(value = "selectResourceByPage")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<CapacityLoadVO>> selectResourceByPage() {
        List<CapacityLoadVO> capacityLoadList = capacityWeekBalanceService.selectResourceByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<CapacityLoadVO> pageInfo = new PageInfo<>(capacityLoadList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "设备负荷-工序-周次维度查询")
    @GetMapping(value = "selectOperationByPage")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<CapacityLoadVO>> selectOperationByPage() {
        List<CapacityLoadVO> capacityLoadList = capacityWeekBalanceService.selectOperationByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<CapacityLoadVO> pageInfo = new PageInfo<>(capacityLoadList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "设备负荷-工序-超负荷预警报")
    @GetMapping(value = "selectOverloadByPage")
    public BaseResponse<PageInfo<CapacityLoadVO>> selectOverloadByPage(@RequestParam(value = "resourceCode",required = false) String resourceCode,
                                                                       @RequestParam("utilization") Integer utilization) {
        List<CapacityLoadVO> capacityLoadList = capacityWeekBalanceService.selectOverloadByPage(resourceCode, utilization);
        PageInfo<CapacityLoadVO> pageInfo = new PageInfo<>(capacityLoadList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "超负荷预警报-导出数据")
    @GetMapping(value = "exportData")
    public void exportData(@RequestParam("utilization") Integer utilization) {
        capacityWeekBalanceService.exportData(this.response, utilization);
    }

    @ApiOperation(value = "周产能供应关系查询")
    @GetMapping(value = "selectSupplyByPage")
    @BusinessMonitorLog(businessCode = "周产能负荷确认", moduleCode = "MPS", businessFrequency = "DAY")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<CapacitySupplyRelationshipVO>> selectSupplyByPage() {
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipList = capacityWeekBalanceService.selectSupplyByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<CapacitySupplyRelationshipVO> pageInfo = new PageInfo<>(capacitySupplyRelationshipList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "周产能平衡-单行编辑")
    @PostMapping(value = "doLineEdit")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> doLineEdit(@RequestBody CapacitySupplyRelationshipWeekEditorDTO dto) {
        capacityWeekBalanceService.doLineEdit(dto);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "周产能平衡-批量编辑")
    @PostMapping(value = "doBatchEdit")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> doBatchEdit(@RequestBody CapacitySupplyRelationshipWeekEditorDTO dto) {
        capacityWeekBalanceService.doBatchEdit(dto);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "周产能平衡-锁定")
    @PostMapping(value = "doLockBatch")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> doLockBatch(@RequestBody CapacitySupplyRelationshipWeekEditorDTO dto) {
        capacityWeekBalanceService.doLockBatch(dto);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
}
