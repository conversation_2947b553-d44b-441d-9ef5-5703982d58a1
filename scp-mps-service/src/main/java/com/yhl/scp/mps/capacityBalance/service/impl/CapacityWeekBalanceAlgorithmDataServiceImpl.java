package com.yhl.scp.mps.capacityBalance.service.impl;

import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.capacityBalance.dto.CapacityBalanceAlgorithmDataDTO;
import com.yhl.scp.mps.capacityBalance.enums.CapacityBalanceTypeEnum;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipDao;
import com.yhl.scp.mps.capacityBalance.service.CapacityLoadService;
import com.yhl.scp.mps.capacityBalance.service.CapacityWeekBalanceAlgorithmDataService;
import com.yhl.scp.mps.capacityBalance.support.CapacityBalanceSpecialSupport;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.productionLeadTime.infrastructure.dao.ProductionLeadTimeDao;
import com.yhl.scp.mps.productionLeadTime.vo.ProductionLeadTimeVO;
import com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.dao.SubInventoryCargoLocationDao;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CapacityWeekBalanceAlgorithmDataServiceImpl</code>
 * <p>
 * 周产能平衡参数组装
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-24 10:34:28
 */
@Slf4j
@Service
public class CapacityWeekBalanceAlgorithmDataServiceImpl extends CapacityBalanceBaseSupport implements CapacityWeekBalanceAlgorithmDataService {

    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private ProductionLeadTimeDao productionLeadTimeDao;
    @Resource
    private CapacityLoadService capacityLoadService;
    @Resource
    private SubInventoryCargoLocationDao subInventoryCargoLocationDao;
    @Resource
    private CapacityBalanceSpecialSupport capacityBalanceSpecialSupport;
    @Resource
    private CapacitySupplyRelationshipDao capacitySupplyRelationshipDao;

    @Override
    public CapacityBalanceAlgorithmDataDTO getCapacityWeekBalanceAlgorithmData(String scenario) {
        String pattern = "yyyy-MM-dd";
        //获取最新发货计划和一致性业务预测数据
        //发货计划是按天维度，把时间转换成yyyy-MM-dd 00:00:00
        String today = DateUtils.dateToString(new Date(), pattern);
        Date now = DateUtils.stringToDate(today, pattern);
        //最新发货计划
        PlanningHorizonVO planningHorizonVO = getPlanningHorizonVO(scenario);
        List<DeliveryPlanVO2> deliveryPlanVO2s = getDeliveryPlanData(planningHorizonVO);

        //发货计划的时间范围（按天）
        List<String> demandTimeDayList = new ArrayList<>();
        //发货计划分解到每天
        Map<String, List<DeliveryPlanVO2>> demandTimeDayMap = new HashMap<>();
        //产能平衡要计算的时间范围(月)
        List<String> monthList = new ArrayList<>();
        CapacityBalanceAlgorithmDataDTO algorithmDataDTO = new CapacityBalanceAlgorithmDataDTO();
        algorithmDataDTO.setType(CapacityBalanceTypeEnum.WEEK.getCode());
        // 周产能平衡的锁定数据
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOS = capacitySupplyRelationshipDao.selectLockOrOutData(CapacityBalanceTypeEnum.WEEK.getCode());

        if (CollectionUtils.isNotEmpty(deliveryPlanVO2s)){
            List<String> oemCodeList = deliveryPlanVO2s.stream().map(DeliveryPlanVO2::getOemCode).distinct().collect(Collectors.toList());
            Map<String, List<String>> productOfOemMap = deliveryPlanVO2s.stream()
                    .collect(Collectors.groupingBy(
                            DeliveryPlanVO2::getProductCode,
                            Collectors.mapping(DeliveryPlanVO2::getOemCode, Collectors.toSet())
                    ))
                    .entrySet()
                    .stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> new ArrayList<>(entry.getValue())
                    ));
            algorithmDataDTO.setProductOfOemMap(productOfOemMap);
            //获取主机厂信息
            getOemInfo(oemCodeList, scenario, algorithmDataDTO);

            for (DeliveryPlanVO2 deliveryPlanVO2 : deliveryPlanVO2s) {
                String demandTime = DateUtils.dateToString(deliveryPlanVO2.getDemandTime(), pattern);
                if (!demandTimeDayList.contains(demandTime)){
                    demandTimeDayList.add(demandTime);
                }
                if (!demandTimeDayMap.containsKey(demandTime)){
                    List<DeliveryPlanVO2> dataList = new ArrayList<>();
                    dataList.add(deliveryPlanVO2);
                    demandTimeDayMap.put(demandTime, dataList);
                }else {
                    demandTimeDayMap.get(demandTime).add(deliveryPlanVO2);
                }
                String month = DateUtils.dateToString(deliveryPlanVO2.getDemandTime(), DateUtils.YEAR_MONTH);
                //保存发货计划的月份
                if (!monthList.contains(month)){
                    monthList.add(month);
                }
            }
            //获取该发货计划覆盖的产品
            List<String> prouctCodeList = deliveryPlanVO2s.stream().map(DeliveryPlanVO2::getProductCode).distinct().collect(Collectors.toList());
            //确定发货计划的时间范围
            Collections.sort(demandTimeDayList);
            String deliveryPlanStart = demandTimeDayList.get(0);
            String deliveryPlanEnd = demandTimeDayList.get(demandTimeDayList.size()-1);
            //整个发货计划横跨的天
            List<String> demandTimeList = getDemandTimeList(deliveryPlanStart, deliveryPlanEnd, pattern);

            //构建发货计划每天每个产品的数量
            Map<String, Map<String, Integer>> deliveryPlanDataMap = getDeliveryPlanDataMap(demandTimeList, demandTimeDayMap, prouctCodeList);

            List<NewStockPointVO> newStockPoints = getStockPointMap(scenario);
            List<NewStockPointVO> bcStockPoints = newStockPoints.stream().filter(t ->
                    StockPointTypeEnum.BC.getCode().equals(t.getStockPointType())).collect(Collectors.toList());
            //工序在制只扣本厂的库存
            List<String> bcStockPointCodes =
                    bcStockPoints.stream().map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

            //获取本厂销售组织类型的仓库(成品库存点)
            List<String> saleOrganizations = bcStockPoints.stream()
                    .filter(e -> StringUtils.isNotEmpty(e.getOrganizeType())
                            && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                    .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
            //获取本厂生产组织类型的仓库(半成品库存点)
            List<String> productOrganizations = bcStockPoints.stream()
                    .filter(e -> StringUtils.isNotEmpty(e.getOrganizeType())
                            && StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                    .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

            // 实时库存数据
            Map<String, Integer> operationInventoryMap = new HashMap<>();
            Map<String, Integer> productInventoryMap = new HashMap<>();
            // 实时库存数据
            List<InventoryBatchDetailVO> inventoryBatchDetails = dfpFeign.selectInventoryDataByProductCodes(scenario,
                    prouctCodeList, StockPointTypeEnum.BC.getCode()).stream().filter(p -> bcStockPointCodes
                    .contains(p.getStockPointCode())).collect(Collectors.toList());
            List<String> spaceList = inventoryBatchDetails.stream().map(InventoryBatchDetailVO::getFreightSpace)
                    .distinct().collect(Collectors.toList());
            // 查询可用本厂有效货位库存
            Map<String, SubInventoryCargoLocationVO> cargoLocationMap = org.apache.commons.collections4.CollectionUtils.isEmpty(spaceList) ?
                    new HashMap<>()
                    : subInventoryCargoLocationDao.selectByBatchCodeAndStockType(spaceList, StockPointTypeEnum.BC.getCode())
                    .stream().collect(Collectors.toMap(SubInventoryCargoLocationVO::getFreightSpaceCode,
                            Function.identity(), (v1, v2) -> v1));
            String rangeData = getRangeData(scenario);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetails)) {
                // 实时库存Map
                productInventoryMap = inventoryBatchDetails.stream()
                        .filter(t -> StringUtils.isEmpty(t.getOperationCode())
                                && saleOrganizations.contains(t.getStockPointCode())
                                && rangeData.equals(t.getSubinventory()))
                        .filter(t -> {
                            String freightSpace = t.getFreightSpace();
                            SubInventoryCargoLocationVO subInventoryCargoLocationVO = cargoLocationMap.get(freightSpace);
                            if (null == subInventoryCargoLocationVO) {
                                return false;
                            }
                            return subInventoryCargoLocationVO.getEnabled().equals(YesOrNoEnum.YES.getCode());
                        })
                        .collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode,
                                Collectors.reducing(0,
                                        t -> new BigDecimal(t.getCurrentQuantity()).intValue(),
                                        Integer::sum
                                )
                        ));
                // 工序在制只扣本厂的库存
                operationInventoryMap = inventoryBatchDetails.stream()
                        .filter(t -> productOrganizations.contains(t.getStockPointCode()))
                        .filter(t -> {
                            String freightSpace = t.getFreightSpace();
                            SubInventoryCargoLocationVO subInventoryCargoLocationVO = cargoLocationMap.get(freightSpace);
                            if (null == subInventoryCargoLocationVO) {
                                return false;
                            }
                            return subInventoryCargoLocationVO.getEnabled().equals(YesOrNoEnum.YES.getCode());
                        })
                        .collect(Collectors.groupingBy( t -> String.join("-",
                                        t.getProductCode(), t.getOperationCode()),
                                Collectors.reducing(0,
                                        t -> new BigDecimal(t.getCurrentQuantity()).intValue(),
                                        Integer::sum
                                )
                        ));
                //扣减实时库存直到扣完为止
                for (String day : demandTimeDayList) {
                    Map<String, Integer> map = deliveryPlanDataMap.get(day);
                    for (Map.Entry<String, Integer> entry : map.entrySet()) {
                        String productCode = entry.getKey();
                        Integer demandQuantity = entry.getValue();
                        //扣减对应库存
                        demandQuantity = productInventory(productInventoryMap, demandQuantity, productCode);
                        entry.setValue(demandQuantity);
                    }
                }
            }
            //算法重要参数
            Map<String, Integer> operationQty = new HashMap<>();
            //每道工序的原始需求量
            Map<String, Integer> primitiveOperationQty = new HashMap<>();
            Map<String, Set<String>> operationOfProductMap = new HashMap<>();
            Map<String, NewProductStockPointVO> productCodeMap = new HashMap<>();
            List<String> routingStepIds = new ArrayList<>();
            //记录物品和工艺路径之间的对应关系
            Map<String, String> routingIdOfProductCodeMap = new HashMap<>();
            //记录工艺路径步骤和工艺路径之间的对应关系
            Map<String, List<RoutingStepVO>> routingStepOfProductMap = new HashMap<>();
            //记录每道工序输入物品和原物品之间的关系
            Map<String, List<String>> sourceProductCodeListMap = new HashMap<>();
            List<String> allInputProductIdList = new ArrayList<>();
            Map<String, String> productIdMap = new HashMap<>();
            //记录半成品和成品的关系
            Map<String, List<String>> map = prouctCodeList.stream().collect(Collectors.groupingBy(t -> t, Collectors.mapping(t->t, Collectors.toList())));
            //递归查询工艺路径
            getRoutingNew(scenario,prouctCodeList, null, productCodeMap, productIdMap,
                    allInputProductIdList, routingIdOfProductCodeMap, routingStepOfProductMap, sourceProductCodeListMap,
                    algorithmDataDTO.getExceptionPOList());
            //根据递归查询到的结果，重新补充qty
            replenishmentQty(deliveryPlanDataMap, routingIdOfProductCodeMap, sourceProductCodeListMap, allInputProductIdList, productIdMap, map);
            algorithmDataDTO.setProductOfSourceProductListMap(map);
            prouctCodeList = new ArrayList<>(productCodeMap.keySet());
            //组装产品工序
            Map<String, RoutingStepVO> routingStepVOMap = getOperationOfProductMap(prouctCodeList, routingIdOfProductCodeMap, routingStepOfProductMap, routingStepIds, operationOfProductMap);
            algorithmDataDTO.setRoutingStepVOMap(routingStepVOMap);
            //获取提前期，组装得到每个产品每道工序应该提前多长时间
            List<ProductionLeadTimeVO> productionLeadTimeVOS = productionLeadTimeDao.selectAverageLeadTime();
            Map<String, BigDecimal> productLeadTimeMap = new HashMap<>();
            for (Map.Entry<String, NewProductStockPointVO> entry : productCodeMap.entrySet()) {
                String productCode = entry.getKey();
                String stockPointCode = entry.getValue().getStockPointCode();
                List<String> operationList = operationOfProductMap.get(productCode).stream().sorted(Comparator.comparing(String::valueOf).reversed()).collect(Collectors.toList());
                List<ProductionLeadTimeVO> leadTimeVOS = productionLeadTimeVOS.stream().filter(t -> stockPointCode.equals(t.getStockPointCode()) && operationList.contains(t.getOperationCode())).collect(Collectors.toList());
                Map<String, BigDecimal> leadTimeMap = leadTimeVOS.stream().collect(Collectors.toMap(ProductionLeadTimeVO::getOperationCode, ProductionLeadTimeVO::getLeadTime));
                BigDecimal total = BigDecimal.ZERO;
                for (String operationCode : operationList) {
                    if (leadTimeMap.containsKey(operationCode)){
                        total =  leadTimeMap.get(operationCode).add(total);
                    }
                    String key = productCode+"-"+operationCode;
                    productLeadTimeMap.put(key, total);
                }
            }
            // demandTimeDayList = demandTimeDayList.stream().sorted(Comparator.comparing(String::valueOf).reversed()).collect(Collectors.toList());
            // 锁定数据处理
            Map<String, BigDecimal> lockQtyData = getLockQtyData(capacitySupplyRelationshipVOS, algorithmDataDTO, deliveryPlanDataMap, pattern);
            // 获取标准工艺上的良品率
            Map<String, StandardStepVO> standardStepMap = getStandardStepMap(scenario);
            Map<String, String> routingStepIdMap = new HashMap<>();
            //工艺路径分解
            decompositionProcessDay(demandTimeDayList, routingStepIds, deliveryPlanDataMap, operationQty, primitiveOperationQty,
                    operationInventoryMap, lockQtyData, routingIdOfProductCodeMap, routingStepOfProductMap, new HashMap<>(),
                    new HashMap<>(), productCodeMap, new ArrayList<>(), true, productLeadTimeMap,
                    planningHorizonVO.getPlanStartTime(), deliveryPlanStart, routingStepIdMap, standardStepMap,
                    algorithmDataDTO.getLockDataMap(), new ArrayList<>());

            //获取工艺路径步骤候选资源
            List<String> routingStepResourceId = getRoutingStepResource(routingStepIds, scenario, algorithmDataDTO);

            algorithmDataDTO.setRoutingStepIdMap(routingStepIdMap);
            algorithmDataDTO.setMonthList(monthList);
            algorithmDataDTO.setOperationQty(operationQty);
            algorithmDataDTO.setPrimitiveOperationQty(primitiveOperationQty);
            algorithmDataDTO.setProductMap(productCodeMap);
            algorithmDataDTO.setOperationOfProductMap(operationOfProductMap);
            algorithmDataDTO.setProductCodeList(prouctCodeList);
            algorithmDataDTO.setRoutingStepResourceIds(routingStepResourceId);
            algorithmDataDTO.setProcessOutsourcingPercentDays(new HashMap<>());
            algorithmDataDTO.setDemandOutsourcingPercentDays(new HashMap<>());

            // 净需求量扣减锁定数量
            deductionLockData(lockQtyData, algorithmDataDTO);


            //获取设备资源相关信息
            capacityLoadService.getResourceNew(scenario, demandTimeDayList, algorithmDataDTO, pattern);
            //获取设备生产资源关系，生产节拍，资源优先级
            getCandidateResourceTimeNew(algorithmDataDTO, scenario, new ArrayList<>());
            algorithmDataDTO.setDayList(demandTimeDayList);
            //获取设置的算法执行规则 (只有一条)
            getRule(algorithmDataDTO);
            //获取零件风险等级
            getMaterialRiskLevel(scenario, prouctCodeList, algorithmDataDTO);
            //处理特殊工艺
            capacityBalanceSpecialSupport.getSpecialOperation(prouctCodeList, scenario, demandTimeDayList, deliveryPlanDataMap, algorithmDataDTO, pattern);
        }

        return algorithmDataDTO;
    }


}
