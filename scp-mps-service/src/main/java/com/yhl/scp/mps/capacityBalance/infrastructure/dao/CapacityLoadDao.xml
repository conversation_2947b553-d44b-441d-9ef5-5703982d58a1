<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacityLoadDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacityLoadPO">
        <!--@Table mps_capacity_load-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_id" jdbcType="VARCHAR" property="versionId"/>
        <result column="forecast_time" jdbcType="TIMESTAMP" property="forecastTime"/>
        <result column="demand_quantity" jdbcType="VARCHAR" property="demandQuantity"/>
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode"/>
        <result column="operation_name" jdbcType="VARCHAR" property="operationName"/>
        <result column="resource_code" jdbcType="VARCHAR" property="resourceCode"/>
        <result column="resource_name" jdbcType="VARCHAR" property="resourceName"/>
        <result column="resource_group_code" jdbcType="VARCHAR" property="resourceGroupCode"/>
        <result column="resource_group_name" jdbcType="VARCHAR" property="resourceGroupName"/>
        <result column="plant_code" jdbcType="VARCHAR" property="plantCode"/>
        <result column="available_capacity" jdbcType="VARCHAR" property="availableCapacity"/>
        <result column="production_capacity" jdbcType="VARCHAR" property="productionCapacity"/>
        <result column="capacity_utilization" jdbcType="VARCHAR" property="capacityUtilization"/>
        <result column="publish_user" jdbcType="VARCHAR" property="publishUser"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO">
        <!-- TODO -->
        <result column="forecast_month" jdbcType="TIMESTAMP" property="forecastMonth"/>
    </resultMap>
    <sql id="Base_Column_List">
id,version_id,forecast_time,demand_quantity,operation_code,operation_name,resource_code,resource_name,resource_group_code,resource_group_name,plant_code,available_capacity,production_capacity,capacity_utilization,publish_user,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.versionId != null and params.versionId != ''">
                and version_id = #{params.versionId,jdbcType=VARCHAR}
            </if>
            <if test="params.forecastTime != null">
                and forecast_time = #{params.forecastTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.forecastTimes != null and params.forecastTimes.size()>0">
                and forecast_time in
                <foreach collection="params.forecastTimes" index="index" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="params.demandQuantity != null">
                and demand_quantity = #{params.demandQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.operationCode != null and params.operationCode != ''">
                and operation_code = #{params.operationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.operationName != null and params.operationName != ''">
                and operation_name = #{params.operationName,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceCode != null and params.resourceCode != ''">
                and resource_code = #{params.resourceCode,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceCodes != null and params.resourceCodes.size()>0">
                and resource_code in
                <foreach collection="params.resourceCodes" index="index" separator="," open="(" close=")"
                         item="item">
                    #{item}
                </foreach>
            </if>
            <if test="params.resourceName != null and params.resourceName != ''">
                and resource_name = #{params.resourceName,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceGroupCode != null and params.resourceGroupCode != ''">
                and resource_group_code = #{params.resourceGroupCode,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceGroupCodes != null and params.resourceGroupCodes.size()>0">
                and resource_group_code in
                <foreach collection="params.resourceGroupCodes" index="index" separator="," open="(" close=")"
                         item="item">
                    #{item}
                </foreach>
            </if>
            <if test="params.resourceGroupName != null and params.resourceGroupName != ''">
                and resource_group_name = #{params.resourceGroupName,jdbcType=VARCHAR}
            </if>
            <if test="params.plantCode != null and params.plantCode != ''">
                and plant_code = #{params.plantCode,jdbcType=VARCHAR}
            </if>
            <if test="params.availableCapacity != null">
                and available_capacity = #{params.availableCapacity,jdbcType=VARCHAR}
            </if>
            <if test="params.productionCapacity != null">
                and production_capacity = #{params.productionCapacity,jdbcType=VARCHAR}
            </if>
            <if test="params.capacityUtilization != null">
                and capacity_utilization = #{params.capacityUtilization,jdbcType=VARCHAR}
            </if>
            <if test="params.publishUser != null and params.publishUser != ''">
                and publish_user = #{params.publishUser,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.versionIds != null and params.versionIds.size()>0">
                and version_id in
                <foreach collection="params.versionIds" index="index" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_capacity_load
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_capacity_load
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />,
        date_format(forecast_time,'%Y-%m') as forecast_month
        from mps_capacity_load
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_capacity_load
        <include refid="Base_Where_Condition" />
    </select>

    <select id="selectLatestData" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_capacity_load
        where version_id  = 'NEW'
        and resource_code != 'VIRTUAL_RESOURCE'
    </select>
    <select id="selectWeekMaxVersionTime" resultType="java.util.Date">
        select max(create_time) from mps_capacity_load where version_id='WEEK'
    </select>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacityLoadPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mps_capacity_load(
        id,
        version_id,
        forecast_time,
        demand_quantity,
        operation_code,
        operation_name,
        resource_code,
        resource_name,
        resource_group_code,
        resource_group_name,
        plant_code,
        available_capacity,
        production_capacity,
        capacity_utilization,
        publish_user,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{forecastTime,jdbcType=TIMESTAMP},
        #{demandQuantity,jdbcType=VARCHAR},
        #{operationCode,jdbcType=VARCHAR},
        #{operationName,jdbcType=VARCHAR},
        #{resourceCode,jdbcType=VARCHAR},
        #{resourceName,jdbcType=VARCHAR},
        #{resourceGroupCode,jdbcType=VARCHAR},
        #{resourceGroupName,jdbcType=VARCHAR},
        #{plantCode,jdbcType=VARCHAR},
        #{availableCapacity,jdbcType=VARCHAR},
        #{productionCapacity,jdbcType=VARCHAR},
        #{capacityUtilization,jdbcType=VARCHAR},
        #{publishUser,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacityLoadPO">
        insert into mps_capacity_load(
        id,
        version_id,
        forecast_time,
        demand_quantity,
        operation_code,
        operation_name,
        resource_code,
        resource_name,
        resource_group_code,
        resource_group_name,
        plant_code,
        available_capacity,
        production_capacity,
        capacity_utilization,
        publish_user,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{forecastTime,jdbcType=TIMESTAMP},
        #{demandQuantity,jdbcType=VARCHAR},
        #{operationCode,jdbcType=VARCHAR},
        #{operationName,jdbcType=VARCHAR},
        #{resourceCode,jdbcType=VARCHAR},
        #{resourceName,jdbcType=VARCHAR},
        #{resourceGroupCode,jdbcType=VARCHAR},
        #{resourceGroupName,jdbcType=VARCHAR},
        #{plantCode,jdbcType=VARCHAR},
        #{availableCapacity,jdbcType=VARCHAR},
        #{productionCapacity,jdbcType=VARCHAR},
        #{capacityUtilization,jdbcType=VARCHAR},
        #{publishUser,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mps_capacity_load(
        id,
        version_id,
        forecast_time,
        demand_quantity,
        operation_code,
        operation_name,
        resource_code,
        resource_name,
        resource_group_code,
        resource_group_name,
        plant_code,
        available_capacity,
        production_capacity,
        capacity_utilization,
        publish_user,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (#{entity.id,jdbcType=VARCHAR},
        #{entity.versionId,jdbcType=VARCHAR},
        #{entity.forecastTime,jdbcType=TIMESTAMP},
        #{entity.demandQuantity,jdbcType=VARCHAR},
        #{entity.operationCode,jdbcType=VARCHAR},
        #{entity.operationName,jdbcType=VARCHAR},
        #{entity.resourceCode,jdbcType=VARCHAR},
        #{entity.resourceName,jdbcType=VARCHAR},
        #{entity.resourceGroupCode,jdbcType=VARCHAR},
        #{entity.resourceGroupName,jdbcType=VARCHAR},
        #{entity.plantCode,jdbcType=VARCHAR},
        #{entity.availableCapacity,jdbcType=VARCHAR},
        #{entity.productionCapacity,jdbcType=VARCHAR},
        #{entity.capacityUtilization,jdbcType=VARCHAR},
        #{entity.publishUser,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mps_capacity_load(
        id,
        version_id,
        forecast_time,
        demand_quantity,
        operation_code,
        operation_name,
        resource_code,
        resource_Name,
        resource_group_code,
        resource_group_Name,
        plant_code,
        available_capacity,
        production_capacity,
        capacity_utilization,
        publish_user,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.versionId,jdbcType=VARCHAR},
        #{entity.forecastTime,jdbcType=TIMESTAMP},
        #{entity.demandQuantity,jdbcType=VARCHAR},
        #{entity.operationCode,jdbcType=VARCHAR},
        #{entity.operationName,jdbcType=VARCHAR},
        #{entity.resourceCode,jdbcType=VARCHAR},
        #{entity.resourceName,jdbcType=VARCHAR},
        #{entity.resourceGroupCode,jdbcType=VARCHAR},
        #{entity.resourceGroupName,jdbcType=VARCHAR},
        #{entity.plantCode,jdbcType=VARCHAR},
        #{entity.availableCapacity,jdbcType=VARCHAR},
        #{entity.productionCapacity,jdbcType=VARCHAR},
        #{entity.capacityUtilization,jdbcType=VARCHAR},
        #{entity.publishUser,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacityLoadPO">
        update mps_capacity_load set
        version_id = #{versionId,jdbcType=VARCHAR},
        forecast_time = #{forecastTime,jdbcType=TIMESTAMP},
        demand_quantity = #{demandQuantity,jdbcType=VARCHAR},
        operation_code = #{operationCode,jdbcType=VARCHAR},
        operation_name = #{operationName,jdbcType=VARCHAR},
        resource_code = #{resourceCode,jdbcType=VARCHAR},
        resource_name = #{resourceName,jdbcType=VARCHAR},
        resource_group_code = #{resourceGroupCode,jdbcType=VARCHAR},
        resource_group_name = #{resourceGroupName,jdbcType=VARCHAR},
        plant_code = #{plantCode,jdbcType=VARCHAR},
        available_capacity = #{availableCapacity,jdbcType=VARCHAR},
        production_capacity = #{productionCapacity,jdbcType=VARCHAR},
        capacity_utilization = #{capacityUtilization,jdbcType=VARCHAR},
        publish_user = #{publishUser,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacityLoadPO">
        update mps_capacity_load
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.forecastTime != null">
                forecast_time = #{item.forecastTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.operationName != null and item.operationName != ''">
                operation_name = #{item.operationName,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceCode != null and item.resourceCode != ''">
                resource_code = #{item.resourceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceName != null and item.resourceName != ''">
                resource_name = #{item.resourceName,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceGroupCode != null and item.resourceGroupCode != ''">
                resource_group_code = #{item.resourceGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceGroupName != null and item.resourceGroupName != ''">
                resource_group_name = #{item.resourceGroupName,jdbcType=VARCHAR},
            </if>
            <if test="item.plantCode != null and item.plantCode != ''">
                plant_code = #{item.plantCode,jdbcType=VARCHAR},
            </if>
            <if test="item.availableCapacity != null">
                available_capacity = #{item.availableCapacity,jdbcType=VARCHAR},
            </if>
            <if test="item.productionCapacity != null">
                production_capacity = #{item.productionCapacity,jdbcType=VARCHAR},
            </if>
            <if test="item.capacityUtilization != null">
                capacity_utilization = #{item.capacityUtilization,jdbcType=VARCHAR},
            </if>
            <if test="item.publishUser != null and item.publishUser != ''">
                publish_user = #{item.publishUser,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mps_capacity_load
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="forecast_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.forecastTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_group_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceGroupCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_group_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceGroupName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.plantCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="available_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.availableCapacity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionCapacity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="capacity_utilization = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.capacityUtilization,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="publish_user = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.publishUser,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mps_capacity_load 
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.forecastTime != null">
                forecast_time = #{item.forecastTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.operationName != null and item.operationName != ''">
                operation_name = #{item.operationName,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceCode != null and item.resourceCode != ''">
                resource_code = #{item.resourceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceName != null and item.resourceName != ''">
                resource_name = #{item.resourceName,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceGroupCode != null and item.resourceGroupCode != ''">
                resource_group_code = #{item.resourceGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceGroupName != null and item.resourceGroupName != ''">
                resource_group_name = #{item.resourceGroupName,jdbcType=VARCHAR},
            </if>
            <if test="item.plantCode != null and item.plantCode != ''">
                plant_code = #{item.plantCode,jdbcType=VARCHAR},
            </if>
            <if test="item.availableCapacity != null">
                available_capacity = #{item.availableCapacity,jdbcType=VARCHAR},
            </if>
            <if test="item.productionCapacity != null">
                production_capacity = #{item.productionCapacity,jdbcType=VARCHAR},
            </if>
            <if test="item.capacityUtilization != null">
                capacity_utilization = #{item.capacityUtilization,jdbcType=VARCHAR},
            </if>
            <if test="item.publishUser != null and item.publishUser != ''">
                publish_user = #{item.publishUser,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mps_capacity_load where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mps_capacity_load where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteUnpublishedBatch">
        delete from mps_capacity_load where version_id  = 'NEW'
    </delete>

    <delete id="deleteByVersion">
        delete from mps_capacity_load
        <where>
            <choose>
                <when test="versionIds != null and versionIds.size() > 0">
                    version_id in
                    <foreach collection="versionIds" item="item" open="(" separator="," close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </when>
                <when test="versionIds == null or versionIds.size() == 0">
                    version_id is null
                </when>
                <otherwise>
                    1 = -1
                </otherwise>
            </choose>
        </where>
    </delete>

    <update id="updateVersionIds" parameterType="java.util.List">
        update mps_capacity_load t set t.version_id = #{versionId,jdbcType=VARCHAR}
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="selectCategoryByCondition" resultMap="VOResultMap">
        SELECT
            t.plant_code,
            t.operation_code,
            t.operation_name,
            t.resource_group_code,
            t.resource_group_name,
            t.resource_code,
            t.resource_name
        FROM
            mps_capacity_load t
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        GROUP BY
            t.plant_code,
            t.operation_code,
            t.operation_name,
            t.resource_group_code,
            t.resource_group_name,
            t.resource_code,
            t.resource_name
        ORDER BY
            plant_code,
            operation_code,
            operation_name,
            resource_group_code,
            resource_code
    </select>

    <select id="selectCapacityLoadDurationByVersion" resultType="java.util.Date">
        SELECT
            t.forecast_time
        FROM
            mps_capacity_load t
        WHERE
            t.version_id = #{versionId}
        GROUP BY
            t.forecast_time
        ORDER BY
            t.forecast_time
    </select>

    <select id="selectShiftLine" resultMap="VOResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_capacity_load where version_id = 'NEW'
        AND capacity_utilization >= 1
        and resource_code in
        <foreach item="item" collection="line" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getResourceGroupDropdown" resultMap="VOResultMap">
        SELECT t.plant_code,
               t.operation_code,
               t.resource_group_code,
               t.resource_group_name
        FROM mps_capacity_load t
        WHERE t.version_id = #{versionId}
        GROUP BY t.plant_code,
                 t.operation_code,
                 t.resource_group_code,
                 t.resource_group_name
        ORDER BY t.plant_code,
                 t.operation_code;
    </select>

    <select id="selectCapacityWeekLoad" resultMap="VOResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_capacity_load where version_id = 'WEEK'
        AND capacity_utilization >= 0.8
        and resource_code in
        <foreach item="item" collection="permissionPhysicalResourceCodes" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>
