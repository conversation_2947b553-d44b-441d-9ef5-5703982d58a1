package com.yhl.scp.mps.domain.algorithm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.yhl.aps.api.runner.APSInput;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.ams.basic.enums.ScheduleTypeEnum;
import com.yhl.scp.biz.common.enums.RedisKeyManageEnum;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.newProduct.vo.NewProductTrialSubmissionDetailVO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.mds.basic.calendar.vo.ResourceCalendarBasicVO;
import com.yhl.scp.mds.basic.resource.enums.ResourceCategoryEnum;
import com.yhl.scp.mds.basic.resource.vo.PhysicalResourceBasicVO;
import com.yhl.scp.mds.basic.spec.enums.SpecKeyEnum;
import com.yhl.scp.mds.enums.StandardStepEnum;
import com.yhl.scp.mds.extension.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepOutputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepResourceVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.spec.vo.SpecBusinessVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.algorithm.enums.DemandTypeEnum;
import com.yhl.scp.mps.algorithm.enums.FulfillmentDemandTypeEnum;
import com.yhl.scp.mps.algorithm.fulfillment.FulfillmentDemand;
import com.yhl.scp.mps.algorithm.fulfillment.FulfillmentSupply;
import com.yhl.scp.mps.algorithm.fulfillment.RzzFulfillment;
import com.yhl.scp.mps.algorithm.schedule.input.*;
import com.yhl.scp.mps.coating.service.CoatingMaintenanceAmountService;
import com.yhl.scp.mps.coating.service.MpsCoatingChangeTimeService;
import com.yhl.scp.mps.coating.vo.CoatingMaintenanceAmountVO;
import com.yhl.scp.mps.coating.vo.MpsCoatingChangeTimeVO;
import com.yhl.scp.mps.common.enums.PostponeRuleEnum;
import com.yhl.scp.mps.dispatch.ams.input.OccupyTime;
import com.yhl.scp.mps.dispatch.ams.input.ResourceData;
import com.yhl.scp.mps.dispatch.ams.input.Schedule;
import com.yhl.scp.mps.domain.dispatch.support.BaseScheduleSupport;
import com.yhl.scp.mps.fixtureRelation.infrastructure.dao.ProductFixtureRelationDao;
import com.yhl.scp.mps.fixtureRelation.infrastructure.po.ProductFixtureRelationPO;
import com.yhl.scp.mps.model.service.MoldChangeTimeService;
import com.yhl.scp.mps.model.vo.MoldChangeTimeVO;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanExtDao;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.proabnormalfeedback.service.ProAbnormalFeedbackService;
import com.yhl.scp.mps.proabnormalfeedback.vo.ProAbnormalFeedbackVO;
import com.yhl.scp.mps.product.service.ProductAdvanceBatchRuleService;
import com.yhl.scp.mps.product.vo.ProductAdvanceBatchRuleVO;
import com.yhl.scp.mps.productionCapacity.service.ProductionCapacityService;
import com.yhl.scp.mps.productionCapacity.vo.LoadingPositionCapacityVO;
import com.yhl.scp.mps.productionCapacity.vo.ProductionCapacityVO;
import com.yhl.scp.mps.productionLeadTime.service.ProductionLeadTimeService;
import com.yhl.scp.mps.productionLimit.service.ProductionLimitService;
import com.yhl.scp.mps.productionLimit.vo.ProductionLimitVO;
import com.yhl.scp.mps.rule.enums.AlgorithmConstraintRuleEnum;
import com.yhl.scp.mps.rule.service.AlgorithmConstraintRuleService;
import com.yhl.scp.sds.basic.feedback.infrastructure.po.FeedbackProductionBasicPO;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import com.yhl.scp.sds.basic.order.vo.OperationBasicVO;
import com.yhl.scp.sds.basic.order.vo.WorkOrderBasicVO;
import com.yhl.scp.sds.basic.pegging.enums.SupplyTypeEnum;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationInputPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO;
import com.yhl.scp.sds.extension.order.vo.*;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.FulfillmentPO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.extension.pegging.vo.FulfillmentVO;
import com.yhl.scp.sds.extension.pegging.vo.SupplyVO;
import com.yhl.scp.sds.order.infrastructure.dao.OperationDao;
import com.yhl.scp.sds.order.infrastructure.dao.OperationInputDao;
import com.yhl.scp.sds.order.service.*;
import com.yhl.scp.sds.pegging.convertor.FulfillmentConvertor;
import com.yhl.scp.sds.pegging.infrastructure.dao.FulfillmentDao;
import com.yhl.scp.sds.pegging.infrastructure.dao.SupplyBasicDao;
import com.yhl.scp.sds.pegging.service.DemandService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>RzzBaseAlgorithmDataService</code>
 * <p>
 * 算法数据组装
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-10 09:50:43
 */
@Slf4j
@Service
public class RzzBaseAlgorithmDataService extends RzzBaseAlgorithmDataConfigService {

    private static final String PARENT = "PARENT";

    private static final String SUB = "SUB";
    private static final int MILLIS_IN_DAY = 24 * 60 * 60 * 1000;
    @Resource
    ProductAdvanceBatchRuleService productAdvanceBatchRuleService;
    @Resource
    private OperationDao operationDao;
    @Resource
    private OperationService operationService;
    @Resource
    private MasterPlanExtDao masterPlanExtDao;
    @Resource
    private OperationSubTaskService operationSubTaskService;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private OperationResourceService operationResourceService;
    @Resource
    private OperationInputService operationInputService;
    @Resource
    private OperationOutputService operationOutputService;
    @Resource
    private OperationExtendService operationExtendService;
    @Resource
    private DemandService demandService;
    @Resource
    private SupplyBasicDao supplyBasicDao;
    @Resource
    private FulfillmentDao fulfillmentDao;
    @Resource
    private OperationInputDao operationInputDao;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private IpsFeign ipsFeign;
    @Resource
    private MdsFeign mdsFeign;
    @Resource
    private ProductionCapacityService productionCapacityService;
    @Resource
    private MoldChangeTimeService moldChangeTimeService;
    @Resource
    private CoatingMaintenanceAmountService coatingMaintenanceAmountService;
    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private MpsCoatingChangeTimeService mpsCoatingChangeTimeService;
    @Resource
    private ProductionLimitService productionLimitService;
    @Resource
    private AlgorithmConstraintRuleService algorithmConstraintRuleService;
    @Resource
    private ProductionLeadTimeService productionLeadTimeService;
    @Resource
    private ProductFixtureRelationDao productFixtureRelationDao;
    @Resource
    private OperationTaskExtDao operationTaskExtDao;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ProAbnormalFeedbackService proAbnormalFeedbackService;

    /**
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param dur       天数
     * @return
     */
    private static List<Pair<String, String>> getDayDur(Date startTime, Date endTime, Integer dur) {
        //这个方法需要根据dur找出startTime和endTime之间所有连续的日期范围，例如2019-01-01到2019-01-07， dur是3，返回[{2019-01-01, 2019-01-03},{2019-01-02, 2019-01-04},{2019-01-03, 2019-01-05},{2019-01-04, 2019-01-07}]
        List<Pair<String, String>> result = new ArrayList<>();
        Date currentDate = new Date(startTime.getTime());

        while (currentDate.getTime() <= endTime.getTime()) {
            Date startDate = new Date(currentDate.getTime());
            Date endDate = new Date(currentDate.getTime() + dur * MILLIS_IN_DAY);
            // Date endDate = new Date(currentDate.getTime() + (dur - 1) * MILLIS_IN_DAY);

            // 确保结束日期不超过 endTime
            if (endDate.after(endTime)) {
                endDate = new Date(endTime.getTime());
            }

            // 将日期格式化为字符串并添加到结果列表中
            result.add(Pair.of(DateUtils.dateToString(startDate, DateUtils.COMMON_DATE_STR1), DateUtils.dateToString(endDate, DateUtils.COMMON_DATE_STR1)));

            // 更新当前日期为下一天
            currentDate = new Date(currentDate.getTime() + MILLIS_IN_DAY);

            // 如果当前日期已经超过了 endTime，则停止循环
            if (currentDate.after(endTime)) {
                break;
            }
            if (endDate.equals(endTime)) {
                break;
            }
        }
        return result;
    }

    /**
     * 将小数表示的字符串转换成标准格式
     *
     * @param time
     * @return
     */
    private static String getTime(String time) {
        if (StringUtils.isBlank(time)) {
            return "00:00:00";
        }
        //time是一个表示小时的字符串，例如 2，2.5，要把2转换成02:00:00，2.5转换成02:30:00
        // 解析输入字符串为浮点数
        double hours = Double.parseDouble(time);
        int hourPart = (int) hours;
        int minutePart = (int) ((hours - hourPart) * 60);

        // 格式化小时部分
        String hourStr = String.format("%02d", hourPart);
        // 格式化分钟部分
        String minuteStr = String.format("%02d", minutePart);

        // 返回标准时间格式
        return String.format("%s:%s:00", hourStr, minuteStr);
    }

    private static Date getResourceStarTime(List<OperationVO> allOperations, PlanningHorizonVO planningHorizon) {
        Date startTime = planningHorizon.getPlanStartTime();
        OperationVO operationVO = allOperations.stream().filter(t -> t.getStartTime() != null).min(Comparator.comparing(OperationBasicVO::getStartTime)).orElse(null);
        if (operationVO != null) {
            startTime = operationVO.getStartTime().before(planningHorizon.getPlanStartTime()) ? operationVO.getStartTime() : planningHorizon.getPlanStartTime();
        }
        return startTime;
    }

    public RzzBaseAlgorithmData getAlgorithmData(String algorithmLogId, List<OperationVO> needOperations) {
        AlgorithmLog algorithmLog = StrUtil.isNotEmpty(algorithmLogId) ? ipsFeign.getLogById(algorithmLogId) : null;
        //1.获取需要排程的工序数据
        if (CollectionUtils.isEmpty(needOperations)) {
            needOperations = getNeedOperations();
        }
        //2.查询所有资源数据，及物理资源备注=YZT的工具资源
        List<String> resourceIds = operationTaskExtDao.selectPhysicalResourceByOperationTask();
        List<PhysicalResourceVO> resources = mdsFeign.getPhysicalResourcesByParams(ImmutableMap.of("ids", resourceIds));
        List<String> needOpIds = needOperations.stream().map(OperationVO::getId).collect(Collectors.toList());
        //3.获取其他工序数据信息,先获取计划期间数据信息
        PlanningHorizonVO planningHorizonVO = newMdsFeign.selectPlanningHorizon(SystemHolder.getScenario());
        List<OperationVO> otherOperations = getOtherOperations(planningHorizonVO, needOpIds);//已计划工序(包括子工序和父工序)
        if (null != algorithmLog) {
            // 权限筛选工序
            otherOperations = processOperation(algorithmLog, otherOperations, needOperations, resources);
        }
        //排程所有工序数据
        List<OperationVO> allOperations = new ArrayList<>();
        allOperations.addAll(needOperations);
        allOperations.addAll(otherOperations);
        if (CollUtil.isEmpty(allOperations)) {
            throw new BusinessException("未获取到需要排程的工序订单数据信息");
        }
        List<MoldChangeTimeVO> moldChangeTimeVOS = moldChangeTimeService.selectTime();
        return this.getAlgorithmData(resources, allOperations, needOperations, ScheduleTypeEnum.RULE_AUTO.getCode(), planningHorizonVO, null, null, null, algorithmLog, moldChangeTimeVOS, null, null);
    }

    private List<OperationVO> processOperation(AlgorithmLog algorithmLog, List<OperationVO> otherOperations, List<OperationVO> needOperations, List<PhysicalResourceVO> resources) {
        String creator = algorithmLog.getCreator();
        String scenario = algorithmLog.getScenario();
        List<String> workOrderIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(algorithmLog.getLineGroup()) && StringUtils.isNotEmpty(algorithmLog.getProductLine())) {
            List<String> lineGroupList = Arrays.stream(algorithmLog.getLineGroup().split(",")).collect(Collectors.toList());
            List<String> productLineList = Arrays.stream(algorithmLog.getProductLine().split(",")).collect(Collectors.toList());
            // List<WorkOrderVO> workOrderVOS = masterPlanExtDao.selectWorkOrderByLineGroupAndProductLine(lineGroupList, productLineList);
            List<String> resourceIds = resources.stream().filter(t -> productLineList.contains(t.getPhysicalResourceCode())).map(BaseVO::getId).collect(Collectors.toList());
            List<String> subWorkOrderIds = otherOperations.stream().filter(t -> StringUtils.isNotEmpty(t.getPlannedResourceId()) && resourceIds.contains(t.getPlannedResourceId())).map(OperationBasicVO::getOrderId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(subWorkOrderIds)) {
                workOrderIds.addAll(subWorkOrderIds);
            }
            List<WorkOrderVO> orderVOS = workOrderService.selectByPrimaryKeys(workOrderIds);
            List<String> parentOrderIds = orderVOS.stream().map(WorkOrderVO::getParentId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(parentOrderIds)) {
                workOrderIds.addAll(parentOrderIds);
            }
        } else {
            List<String> plannerProduct = newMdsFeign.getPlannerProduct(scenario, creator);
            List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByProducts(plannerProduct);
            List<String> productIds = StreamUtils.columnToList(newProductStockPointVOS, NewProductStockPointVO::getId);
            // 待排工序的物料id，避免出现半品没有权限问题
            productIds.addAll(StreamUtils.columnToList(needOperations, OperationVO::getProductId));

            List<WorkOrderVO> workOrderVOS = workOrderService.selectAll().stream().filter(p -> productIds.contains(p.getProductId())).collect(Collectors.toList());
            workOrderIds = StreamUtils.columnToList(workOrderVOS, WorkOrderVO::getId);
        }
        List<String> finalWorkOrderIds = workOrderIds;
        return otherOperations.stream().filter(p -> finalWorkOrderIds.contains(p.getOrderId())).collect(Collectors.toList());
    }

    public RzzBaseAlgorithmData getAlgorithmData(List<PhysicalResourceVO> resources, List<OperationVO> allOperations, List<OperationVO> needOperations, String scheduleType, PlanningHorizonVO planningHorizon, String handworkType, String postponeRule, List<String> infinitResourceIdList, AlgorithmLog algorithmLog, List<MoldChangeTimeVO> moldChangeTimeVOS, List<String> detailIds, List<String> szWorkOrderIds) {
        log.info("getAlgorithmData allOperations1 size is " + allOperations.size());

        List<OperationVO> unPlanedSubOperations = new ArrayList<>();
        //待排工序 手工编辑不会走
        List<String> needOperationIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(needOperations)) {
            needOperationIds = needOperations.stream().map(OperationVO::getId).collect(Collectors.toList());
            unPlanedSubOperations = operationService.selectVOByParams(ImmutableMap.of("parentIds", needOperationIds));
        }

        // 手工编辑不会走
        if (CollectionUtils.isEmpty(allOperations)) {//自动排程、优化排程
            allOperations = new ArrayList<>();
            List<OperationVO> otherOperations = operationService.getPlanedOperationsByParams(planningHorizon.getPlanStartTime(), null, new ArrayList<>());//未排程以外的工序
            allOperations.addAll(otherOperations);//已排子工序
            allOperations.addAll(needOperations);//待排父工序
            if (!CollectionUtils.isEmpty(unPlanedSubOperations)) {
                allOperations.addAll(unPlanedSubOperations);//待排父工序下面的未排子工序
            }
        }
        List<String> needScheduleOperationIds = needOperations.stream().map(OperationVO::getId).collect(Collectors.toList());

        log.info("getAlgorithmData allOperations2 size is " + allOperations.size());
        log.info("getAlgorithmData allResource size is " + resources.size());


        List<String> allOperationIds = allOperations.stream().map(OperationVO::getId).collect(Collectors.toList());
        //开始组装算法数据，
        RzzBaseAlgorithmData algorithmData = new RzzBaseAlgorithmData();
        algorithmData.setScheduleType(scheduleType);
        //获取算法约束规则数据
        getAlgorithmConstraintRules(algorithmData);
        //连续炉开机规则限制资源
        List<String> lxResourceCodes = Arrays.asList("S1HW03");
        List<String> lxResourceIds = resources.stream().filter(t -> lxResourceCodes.contains(t.getPhysicalResourceCode())).map(PhysicalResourceBasicVO::getId).collect(Collectors.toList());
        algorithmData.setLxResourceIds(lxResourceIds);

        long FulfillmentDTOTime = System.currentTimeMillis();
        List<FulfillmentVO> fulfillmentVOList;
        if (ScheduleTypeEnum.HANDWORK.getCode().equals(scheduleType)) {
            fulfillmentVOList = new ArrayList<>();
        } else {
            fulfillmentVOList = this.queryFulfillmentByOperationIds(allOperationIds);
        }
        log.info("数据查询 fulfillmentDTOs 用时:" + (System.currentTimeMillis() - FulfillmentDTOTime));

        //拿到fulfillment所有工序和制造订单id
        List<String> continuousProductionProcessIds = new ArrayList<>();
        List<String> fulfillOperationIds = new ArrayList<>();//通过物料分配关系关联的并且不在'allOperations'里面的工序
        for (FulfillmentVO fulfillmentVO : fulfillmentVOList) {
            DemandVO demand = fulfillmentVO.getDemandVO();
            SupplyVO supply = fulfillmentVO.getSupplyVO();
            if (StringUtils.isNotBlank(demand.getOperationId()) && !allOperationIds.contains(demand.getOperationId())) {
                fulfillOperationIds.add(demand.getOperationId());
            }
            if (StringUtils.isNotBlank(supply.getOperationId()) && !allOperationIds.contains(supply.getOperationId())) {
                fulfillOperationIds.add(supply.getOperationId());
            }
            if (DemandTypeEnum.CUSTOMER_ORDER_DEMAND.getCode().equals(fulfillmentVO.getDemandType())) {
                continuousProductionProcessIds.add(supply.getSupplyOrderId());
            }
        }
        continuousProductionProcessIds = continuousProductionProcessIds.stream().distinct().collect(Collectors.toList());
        algorithmData.setContinuousProductionProcessIds(continuousProductionProcessIds);
        if (!CollectionUtils.isEmpty(fulfillOperationIds)) {
            long fulfillOperationsTime = System.currentTimeMillis();
            List<OperationVO> fulfillAddOperations = new ArrayList<>();
            List<OperationVO> fulfillOperations = operationService.getVOListByIds(fulfillOperationIds);
            if (CollectionUtils.isNotEmpty(fulfillOperations)) {
                List<String> parentIds = fulfillOperations.stream().map(OperationVO::getId).collect(Collectors.toList());
                List<OperationVO> operations = operationService.selectVOByParams(ImmutableMap.of("parentIds", parentIds));
                if (CollectionUtils.isNotEmpty(operations)) {
                    fulfillAddOperations.addAll(operations);
                }
            }
            fulfillAddOperations.addAll(fulfillOperations);
            List<String> addPlanResourceIds = fulfillOperations.stream().filter(t -> StringUtils.isNotEmpty(t.getPlannedResourceId())).distinct().map(OperationBasicVO::getPlannedResourceId).collect(Collectors.toList());
            List<String> resourceIds = resources.stream().map(BaseVO::getId).collect(Collectors.toList());
            addPlanResourceIds = addPlanResourceIds.stream().filter(t -> !resourceIds.contains(t)).collect(Collectors.toList());
            log.info("根据供应关系新增的资源数量{}", addPlanResourceIds);
            List<PhysicalResourceVO> physicalResourceVOS = newMdsFeign.selectByPhysicalIds(SystemHolder.getScenario(), addPlanResourceIds);
            resources.addAll(physicalResourceVOS);
            allOperations.addAll(fulfillAddOperations);
            log.info("数据查询 fulfillOperations 用时:" + (System.currentTimeMillis() - fulfillOperationsTime));
        }
        allOperationIds = allOperations.stream().map(OperationVO::getId).collect(Collectors.toList());

        //4.获取所有工序 已按照工序顺序排序
        List<String> workOrderIds = allOperations.stream().map(OperationVO::getOrderId).distinct().collect(Collectors.toList());
        long workOrderTime = System.currentTimeMillis();

        List<WorkOrderVO> workOrders = workOrderService.selectByParams(ImmutableMap.of("ids", workOrderIds));
        //获取制造订单上层、下层、顶层、底层制造订单(不处理这块逻辑)
        log.info("数据查询 workOrders 用时:" + (System.currentTimeMillis() - workOrderTime));
        algorithmData.setWorkOrderVOMap(workOrders.stream().collect(Collectors.toMap(BaseVO::getId, Function.identity())));

        //查询制造订单规格
        Map<String, List<SpecBusinessVO>> specWorkOrderCollectOfWorkOrderId = new HashMap<>();
        //查询物品规格
        Map<String, List<SpecBusinessVO>> specProductCollectOfPointId = new HashMap<>();
        //查询工艺路径规格
        Map<String, List<SpecBusinessVO>> specRoutingCollectOfRoutingId = new HashMap<>();

        //查询工序候选资源数据
        long operationResourcesTime = System.currentTimeMillis();
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("enabled", YesOrNoEnum.YES.getCode());
        queryParams.put("operationIds", allOperationIds);
        // 2025/04/04 逻辑变更，候选资源全部从基础数据工序候选资源中获取
        List<String> routingStepIds = StreamUtils.columnToList(allOperations, OperationVO::getRoutingStepId).stream().distinct().collect(Collectors.toList());
        List<RoutingStepResourceVO> totalRoutingStepResourceVOS = newMdsFeign.selectRoutingStepResourceByRoutingStepIds2(routingStepIds);
        List<RoutingStepResourceVO> routingStepResourceVOS;
        if (ScheduleTypeEnum.HANDWORK.getCode().equals(scheduleType)) {
            // 手工编辑时过滤一下候选资源
            List<String> resourceIds = resources.stream().map(BaseVO::getId).collect(Collectors.toList());
            routingStepResourceVOS = totalRoutingStepResourceVOS.stream().filter(t -> resourceIds.contains(t.getPhysicalResourceId())).collect(Collectors.toList());
        } else {
            routingStepResourceVOS = totalRoutingStepResourceVOS;
        }
        log.info("数据查询 operationResourcesTime 用时:" + (System.currentTimeMillis() - operationResourcesTime));

        //输入物料
        long inputMaterialsTime = System.currentTimeMillis();
        List<OperationInputVO> inputMaterials = operationInputService.selectByParams(queryParams);
        log.info("数据查询 inputMaterialsTime 用时:" + (System.currentTimeMillis() - inputMaterialsTime));

        //输出物料
        long outMaterialsTime = System.currentTimeMillis();
        List<OperationOutputVO> outMaterials = operationOutputService.selectByParams(queryParams);
        log.info("数据查询 outMaterials 用时:" + (System.currentTimeMillis() - outMaterialsTime));

        //物品，库存点
        long productStockPointTime = System.currentTimeMillis();
        List<String> productIds = workOrders.stream().map(WorkOrderVO::getProductId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(inputMaterials)) {
            productIds.addAll(inputMaterials.stream().map(OperationInputVO::getProductId).distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(outMaterials)) {
            productIds.addAll(outMaterials.stream().map(OperationOutputVO::getProductId).distinct().collect(Collectors.toList()));
        }
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByIds(SystemHolder.getScenario(), productIds);
        List<NewStockPointVO> stockPointVOS = newMdsFeign.selectAllStockPoint(SystemHolder.getScenario());
        Map<String, String> newProductStockPointMap = newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getId, NewProductStockPointVO::getProductCode, (v1, v2) -> v1));
        Map<String, NewProductStockPointVO> productMap = newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getId, v -> v, (v1, v2) -> v1));
        //记录物品对应的库存点编码和装车位置
        Map<String, String> productIdOfStockAndLoadingPositionMap = getLoadingPositionMap(newProductStockPointVOS);
        //记录物品对应库存点编码
        Map<String, String> productIdOfStockMap = newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getId, NewProductStockPointVO::getStockPointCode, (v1, v2) -> v1));
        Map<String, String> productIdOfCodeMap = newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getId, NewProductStockPointVO::getProductCode, (v1, v2) -> v1));
        log.info("数据查询 productStockPointNew 用时:" + (System.currentTimeMillis() - productStockPointTime));
        long productStockPointBaseTime = System.currentTimeMillis();
        List<String> productCodeList = newProductStockPointVOS.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        List<PartRiskLevelVO> partRiskLevelVOS = dfpFeign.selectMaterialRiskLeveByProductCodeList(null, productCodeList);
        Map<String, Object> params = new HashMap<>();
        params.put("productCodeList", productCodeList);
        List<MdsProductStockPointBaseVO> stockPointBaseVOS = newMdsFeign.selectProductStockPointBaseByParams(SystemHolder.getScenario(), params);
        algorithmData.setStockPointBaseVOS(stockPointBaseVOS);
        Map<String, String> productBaseMap = stockPointBaseVOS.stream().filter(p -> StrUtil.isNotBlank(p.getStandardResourceId())).collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getStandardResourceId, (existing, replacement) -> existing));
        //物料对应膜系
        Map<String, String> stockPointBaseMap = stockPointBaseVOS.stream().filter(t -> t.getMembraneSystem() != null && !"/".equals(t.getMembraneSystem()) && !"无".equals(t.getMembraneSystem())).collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getMembraneSystem, (v1, v2) -> v1));
        //物料对应夹丝类型
        Map<String, String> clampTypeBaseMap = stockPointBaseVOS.stream().filter(t -> t.getClampType() != null && !"/".equals(t.getClampType()) && !"无".equals(t.getClampType())).collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getClampType, (v1, v2) -> v1));
        //物料对应调光类型
        Map<String, String> itemFlagBaseMap = stockPointBaseVOS.stream().filter(t -> t.getItemFlag() != null && !"/".equals(t.getItemFlag()) && !"无".equals(t.getItemFlag())).collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getItemFlag, (v1, v2) -> v1));
        //物料对应除膜工艺
        Map<String, String> attr1BaseMap = stockPointBaseVOS.stream().filter(t -> t.getAttr1() != null && !"/".equals(t.getAttr1()) && !"无".equals(t.getAttr1())).collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getAttr1, (v1, v2) -> v1));
        //物料关键工序对应产线组
        Map<String, String> lineGroupBaseMap = stockPointBaseVOS.stream().filter(t -> t.getLineGroup() != null && !"/".equals(t.getLineGroup()) && !"无".equals(t.getLineGroup())).collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getLineGroup, (v1, v2) -> v1));
        Map<String, String> hudBaseMap = stockPointBaseVOS.stream().filter(t -> t.getHud() != null && !"/".equals(t.getLineGroup()) && !"无".equals(t.getHud())).collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getHud, (v1, v2) -> v1));
        Map<String, String> toolingMoldMap = stockPointBaseVOS.stream().filter(t -> t.getStandardResourceId() != null).collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getStandardResourceId, (v1, v2) -> v1));

        Map<String, String> membraneSystemMap = new HashMap<>();
        Map<String, String> clampOfProductMap = new HashMap<>();
        Map<String, String> itemFlagOfProductMap = new HashMap<>();
        Map<String, String> attr1OfProductMap = new HashMap<>();
        Map<String, String> lineGroupOfProductMap = new HashMap<>();
        Map<String, String> hudOfProductMap = new HashMap<>();
        for (Map.Entry<String, String> entry : newProductStockPointMap.entrySet()) {
            String productId = entry.getKey();
            String productCode = entry.getValue();
            if (stockPointBaseMap.containsKey(productCode)) {
                membraneSystemMap.put(productId, stockPointBaseMap.get(productCode));
            }
            if (clampTypeBaseMap.containsKey(productCode)) {
                clampOfProductMap.put(productId, clampTypeBaseMap.get(productCode));
            }
            if (itemFlagBaseMap.containsKey(productCode)) {
                itemFlagOfProductMap.put(productId, itemFlagBaseMap.get(productCode));
            }
            if (attr1BaseMap.containsKey(productCode)) {
                attr1OfProductMap.put(productId, attr1BaseMap.get(productCode));
            }
            if (lineGroupBaseMap.containsKey(productCode)) {
                lineGroupOfProductMap.put(productId, attr1BaseMap.get(productCode));
            }
            if (hudBaseMap.containsKey(productCode)) {
                hudOfProductMap.put(productId, attr1BaseMap.get(productCode));
            }
        }
        algorithmData.setMembraneSystemMap(membraneSystemMap);
        algorithmData.setClampOfProductMap(clampOfProductMap);
        algorithmData.setItemFlagOfProductMap(itemFlagOfProductMap);
        algorithmData.setAttr1OfProductMap(attr1OfProductMap);
        algorithmData.setLineGroupOfProductMap(lineGroupOfProductMap);
        algorithmData.setHudOfProductMap(hudOfProductMap);
        log.info("数据查询 productStockPointBaseVO 用时:" + (System.currentTimeMillis() - productStockPointBaseTime));

        //查询镀膜切花时间配置数据
        long mpsCoatingChangeTimeTime = System.currentTimeMillis();
        List<MpsCoatingChangeTimeVO> coatingChangeTimeList = mpsCoatingChangeTimeService.selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));
        algorithmData.setCoatingChangeTimeList(coatingChangeTimeList);
        List<CollectionValueVO> filmsCollectionValueList = ipsFeign.getByCollectionCode("FILMS");
        Map<String, String> filmsCollectionValueMap = filmsCollectionValueList.stream().collect(Collectors.toMap(CollectionValueVO::getCollectionValue, CollectionValueVO::getValueMeaning, (v1, v2) -> v1));
        algorithmData.setFilmsCollectionValueMap(filmsCollectionValueMap);
        log.info("数据查询 coatingChangeTimeList 用时:" + (System.currentTimeMillis() - mpsCoatingChangeTimeTime));

        //查询后工序库容(查询所有的)
//        List<String> loadingPositionList = newProductStockPointVOS.stream().map(NewProductStockPointVO::getLoadingPosition).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<ProductionCapacityVO> productionCapacityVOS = productionCapacityService.selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));
        Map<String, ProductionCapacityVO> withLoadingPositionOfCapcityMap = productionCapacityVOS.stream().filter(t -> StringUtils.isNotEmpty(t.getLoadingPosition())).collect(Collectors.toMap(t -> String.join("#", t.getPlantCode(), t.getLoadingPosition(), t.getOperationCode()), Function.identity(), (v1, v2) -> v1));
        Map<String, ProductionCapacityVO> withNotLoadingPositionOfCapcityMap = productionCapacityVOS.stream().filter(t -> StringUtils.isEmpty(t.getLoadingPosition())).collect(Collectors.toMap(t -> String.join("#", t.getPlantCode(), t.getOperationCode()), Function.identity(), (v1, v2) -> v1));

        algorithmData.setProductIdOfStockAndLoadingPositionMap(productIdOfStockAndLoadingPositionMap);
        algorithmData.setProductIdOfStockMap(productIdOfStockMap);
        algorithmData.setProductIdOfCodeMap(productIdOfCodeMap);
        algorithmData.setWithLoadingPositionOfCapcityMap(withLoadingPositionOfCapcityMap);
        algorithmData.setWithNotLoadingPositionOfCapcityMap(withNotLoadingPositionOfCapcityMap);
        algorithmData.setLoadingPositionOfCapcityMap(new HashMap<>());

        //查询换模时间
        algorithmData.setMoldChangeTimeDataList(getRzzMoldChangeTimeData(allOperations, resources, moldChangeTimeVOS, newProductStockPointVOS));
        //查询镀膜保养量
        long maintenanceAmount = System.currentTimeMillis();
        List<CoatingMaintenanceAmountVO> maintenanceAmountVOS = coatingMaintenanceAmountService.selectByParams(new HashMap<>());
        Map<String, String> resourceCodeOfIdMap = resources.stream().collect(Collectors.toMap(PhysicalResourceBasicVO::getPhysicalResourceCode, BaseVO::getId, (v1, v2) -> v1));
        Map<String, Pair<BigDecimal, String>> equipmentMaintenanceQuantityMap = new HashMap<>();
        for (CoatingMaintenanceAmountVO amountVO : maintenanceAmountVOS) {
            if (resourceCodeOfIdMap.containsKey(amountVO.getResourceCode())) {
                equipmentMaintenanceQuantityMap.put(resourceCodeOfIdMap.get(amountVO.getResourceCode()), Pair.of(amountVO.getEquipmentMaintenanceQuantity(), getTime(amountVO.getEquipmentMaintenanceTime())));
            }
        }
        algorithmData.setEquipmentMaintenanceQuantityMap(equipmentMaintenanceQuantityMap);
        log.info("数据查询 maintenanceAmount 用时:" + (System.currentTimeMillis() - maintenanceAmount));

        //查询生产提前期数据
        long productionLeadTime = System.currentTimeMillis();
        algorithmData.setProductionLeadTimeMap(productionLeadTimeService.getProductionLeadTimeMap());
        List<CollectionValueVO> technologyTypeSubclassList = ipsFeign.getByCollectionCode("TECHNOLOGY_TYPE_SUBCLASS");
        Map<String, String> techonlogyTypeMap = technologyTypeSubclassList.stream().collect(Collectors.toMap(CollectionValueVO::getValueMeaning, CollectionValueVO::getCollectionValue, (v1, v2) -> v1));
        algorithmData.setTechonlogyTypeMap(techonlogyTypeMap);
        log.info("数据查询 productionLeadTime 用时:" + (System.currentTimeMillis() - productionLeadTime));
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(null);
        Map<String, StandardStepVO> standardStepVOMap = StreamUtils.mapByColumn(standardStepVOS, StandardStepVO::getId);
        // 新品试制提报明细数据
        if (CollectionUtils.isEmpty(szWorkOrderIds)) {
            szWorkOrderIds = new ArrayList<>();
        }
        Map<String, String> workOrderParentIdMap = workOrders.stream().filter(t -> t.getParentId() != null).collect(Collectors.toMap(BaseVO::getId, WorkOrderBasicVO::getParentId));
        Map<String, NewProductTrialSubmissionDetailVO> detailVOMap = getNewProductTrialSubmissionDetailMap(detailIds, workOrders, szWorkOrderIds);


        //查询输入物品规格
        //根据工艺路径查询输入物
        // 组装输入物品/输出物品规格数据
        Map<String, List<SpecBusinessVO>> specRoutingStepCollectOfStepId = new HashMap<>(16);
        Map<String, List<SpecBusinessVO>> specRoutingStepInputCollectOfInputId = new HashMap<>(16);
        Map<String, List<SpecBusinessVO>> specRoutingStepOutputCollectOfOutputId = new HashMap<>(16);
        Map<String, RoutingStepInputVO> routingStepInputMapOfJoinKey = new HashMap<>(16);
        Map<String, RoutingStepOutputVO> routingStepOutputMapOfJoinKey = new HashMap<>(16);

        //组装制造订单数据
        algorithmData.setWorkOrders(getWorkOrders(workOrders, specWorkOrderCollectOfWorkOrderId, specProductCollectOfPointId, specRoutingCollectOfRoutingId));

        // 获取压制台的换模时间
        algorithmData.setYztSwitchSeconds(getYztSwitchData());
        //组装工序数据
        Set<String> draPhysicalResource = new HashSet<>();
        // 记录成型工序
        List<String> formatOperations = new ArrayList<>();
        // 工序对应优先级最高资源id
        Map<String, String> operationOnResourceIdMap = new HashMap<>();
        algorithmData.setOperations(getOperations(allOperations, inputMaterials, outMaterials, scheduleType, handworkType,
                specRoutingStepCollectOfStepId, specRoutingStepInputCollectOfInputId, routingStepInputMapOfJoinKey, specRoutingStepOutputCollectOfOutputId,
                routingStepOutputMapOfJoinKey, specProductCollectOfPointId, algorithmData, productMap, resources, draPhysicalResource,
                algorithmLog, standardStepVOMap, routingStepResourceVOS, detailVOMap, szWorkOrderIds, formatOperations, workOrderParentIdMap, operationOnResourceIdMap));

        algorithmData.setOperationOnResourceIdMap(operationOnResourceIdMap);

        algorithmData.setResourceMap(getResourceMap(resources, planningHorizon, postponeRule, infinitResourceIdList,
                algorithmLog, algorithmData.getOperations(), formatOperations, getResourceStarTime(allOperations, planningHorizon)));

        algorithmData.setDryBendingResourceIds(draPhysicalResource);

        // 计算关键工序的最早开始时间
        afterSetAfterOperationTime(algorithmData, productMap, workOrders, partRiskLevelVOS, toolingMoldMap, standardStepVOMap, scheduleType, algorithmLog);

        //组装物料数据
        algorithmData.setProductInSockingPoints(getProductInSockingPoints(newProductStockPointVOS, stockPointVOS, specProductCollectOfPointId, toolingMoldMap, productBaseMap));

        //组装供需关系数据
        algorithmData.setFulfillmentList(getFulfillments(fulfillmentVOList, algorithmData.getWorkOrders(), algorithmData.getOperations()));

        ScheduleParamDetail scheduleParamDetail = getScheduleParamDetail(planningHorizon);
        algorithmData.setScheduleParamDetail(scheduleParamDetail);
        algorithmData.setNeedScheduleOperationIds(needScheduleOperationIds);
        //组装特殊工艺产能约束限制数据
        algorithmData.setSpecialLimitDataList(getSpecialLimit(planningHorizon));
        algorithmData.setSourceOperations(allOperations);
        algorithmData.setMoldChangeTimeVOS(moldChangeTimeVOS);
        algorithmData.setStandardStepVOS(standardStepVOS);
        return algorithmData;
    }

    private Map<String, NewProductTrialSubmissionDetailVO> getNewProductTrialSubmissionDetailMap(List<String> detailIds, List<WorkOrderVO> workOrders, List<String> szWorkOrderIds) {
        Map<String, Object> params = new HashMap<>();
        boolean szFlag = false;
        Map<String, NewProductTrialSubmissionDetailVO> detailVOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(detailIds)) {
            params.put("ids", detailIds);
        } else if (CollectionUtils.isEmpty(detailIds) && CollectionUtils.isEmpty(szWorkOrderIds)) {
            szFlag = true;
            List<WorkOrderVO> szWorkOrders = workOrders.stream().filter(t -> !"LC".equals(t.getOrderType())).collect(Collectors.toList());
            szWorkOrderIds.addAll(szWorkOrders.stream().map(WorkOrderVO::getId).collect(Collectors.toList()));
            szWorkOrderIds.addAll(szWorkOrders.stream().map(WorkOrderVO::getParentId).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(szWorkOrderIds)) {
                params.put("workOrderIds", szWorkOrderIds);
            }
        }
        if (!params.isEmpty()) {
            List<NewProductTrialSubmissionDetailVO> detailVOS = dfpFeign.selectNewProductTrialSubmissionDetailByParams(SystemHolder.getScenario(), params);
            if (!szFlag && CollectionUtils.isNotEmpty(szWorkOrderIds) && CollectionUtils.isNotEmpty(detailVOS)) {
                // 新品试制时下发新增的，此时workOrderId还为null
                for (String szWorkOrderId : szWorkOrderIds) {
                    detailVOMap.put(szWorkOrderId, detailVOS.get(0));
                }
            } else {
                // 自动排产
                detailVOMap = detailVOS.stream().collect(Collectors.toMap(NewProductTrialSubmissionDetailVO::getWorkOrderId, Function.identity(), (v1, v2) -> v1));
            }
        }
        return detailVOMap;
    }

    /**
     * 获取压制台的换模时间
     *
     * @return
     */
    private Integer getYztSwitchData() {
        List<CollectionValueVO> yztChangeTimes = ipsFeign.getByCollectionCode("YZT_CHANGE_TIME");
        if (CollectionUtils.isEmpty(yztChangeTimes)) {
            return 0;
        }
        Optional<CollectionValueVO> optional = yztChangeTimes.stream().sorted(Comparator.comparing(CollectionValueVO::getValueSequence).reversed()).findFirst();
        if (!optional.isPresent()) {
            return 0;
        }
        // 默认分钟
        String collectionValue = optional.get().getCollectionValue();
        if (StringUtils.isBlank(collectionValue)) {
            return 0;
        }
        // 转换成秒
        int seconds = Integer.valueOf(collectionValue) * 60;
        return seconds;
    }

    private void afterSetAfterOperationTime(RzzBaseAlgorithmData algorithmData, Map<String, NewProductStockPointVO> productMap, List<WorkOrderVO> workOrders, List<PartRiskLevelVO> partRiskLevelVOS, Map<String, String> toolingMoldMap, Map<String, StandardStepVO> standardStepVOMap, String scheduleType, AlgorithmLog algorithmLog) {

        List<String> lockWorkOrderIds = new ArrayList<>();
        if (ScheduleTypeEnum.RULE_AUTO.getCode().equals(scheduleType) && null != algorithmLog) {
            String creator = algorithmLog.getCreator();
            String key = RedisKeyManageEnum.MPS_ALGORITHM_LOCK_WORK_ORDER.getKey().replace("{userId}", creator);
            List<Object> cacheDeleteWorkOrderDelete = redisUtil.lGet(key);
            if (CollectionUtils.isNotEmpty(cacheDeleteWorkOrderDelete)) {
                Object obj = cacheDeleteWorkOrderDelete.get(0);
                if (obj instanceof List) {
                    List<?> list = (List<?>) obj;
                    if (!list.isEmpty() && list.get(0) instanceof String) {
                        lockWorkOrderIds = (List<String>) obj;
                    }
                }
            }
        }
        Map<String, WorkOrderVO> workOrderVOMap = StreamUtils.mapByColumn(workOrders, WorkOrderVO::getId);
        Map<String, PartRiskLevelVO> partRiskLevelVOMap = mapByProductCode(partRiskLevelVOS);
        List<ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOS = productAdvanceBatchRuleService.selectAll();
        Map<String, ProductAdvanceBatchRuleVO> riskLevelAdvanceBatchRuleVOMap = mapByRiskLevel(productAdvanceBatchRuleVOS);

        // 根据不同维度分组提前生产批次规则（物料编码，车型，风险等级）
        Map<String, ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getProductCode()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getProductCode, v -> v, (k1, k2) -> k1));
        Map<String, ProductAdvanceBatchRuleVO> productTypeAdvanceBatchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getProductType()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getProductType, v -> v, (k1, k2) -> k1));

        List<RzzOperationData> operations = algorithmData.getOperations();
        List<String> process = ListUtil.of("FORMING_PROCESS", "COATING_PROCESS");
        List<String> finalLockWorkOrderIds = lockWorkOrderIds;
        List<String> limitStatus = Arrays.asList(PlannedStatusEnum.STARTED.getCode(), PlannedStatusEnum.FINISHED.getCode());
        operations.forEach(operation -> {
            WorkOrderVO workOrderVO = workOrderVOMap.get(operation.getWorkOrderId());
            String planStatus = operation.getPlanStatus();
            if (limitStatus.contains(planStatus)) {
                operation.setEarliestBeginTime(null);
                return;
            }
            if (workOrderVO == null) return;
            String workOrderVOId = workOrderVO.getId();
            if (finalLockWorkOrderIds.contains(workOrderVOId)) {
                log.info("锁定期内工单不更新最早开始时间：{}", workOrderVOId);
                return;
            }
            NewProductStockPointVO productStockPointVO = productMap.get(workOrderVO.getProductId());
            if (productStockPointVO == null) return;
            String productCode = productStockPointVO.getProductCode();
            String vehicleModelCode = productStockPointVO.getVehicleModelCode();

            operation.setProductType(productStockPointVO.getVehicleModelCode());
            operation.setWorkWearCategory(toolingMoldMap.get(productCode));

            StandardStepVO standardStepVO = standardStepVOMap.get(operation.getStandardStepId());
            if (standardStepVO == null || !process.contains(standardStepVO.getStandardStepType())) return;

            BigDecimal maximumNumberDays = BaseScheduleSupport.getMaximumNumberDays(
                    productCode, vehicleModelCode,
                    productAdvanceBatchRuleVOMap,
                    productTypeAdvanceBatchRuleVOMap,
                    partRiskLevelVOMap,
                    riskLevelAdvanceBatchRuleVOMap,
                    false);
            if (maximumNumberDays == null) return;

            if (!operation.getWhetherTime()) {
                return;
            }
            Date earliestBeginTime = DateUtil.offsetDay(workOrderVO.getDueDate(), -maximumNumberDays.intValue());
            // 如果最早开始时间 <= 交期-最大提前天数，则不更新；
            Date oldEarliestBeginTime = operation.getEarliestBeginTime();
            boolean flag = null != oldEarliestBeginTime && oldEarliestBeginTime.getTime() <= earliestBeginTime.getTime();
            if (ScheduleTypeEnum.RULE_AUTO.getCode().equals(scheduleType) && finalLockWorkOrderIds.contains(workOrderVOId) && flag) {
                log.info("二次排程锁定期内工单最早开始晚于历史最早开始，不更新最早开始：{}", workOrderVOId);
                return;
            }
            if (ScheduleTypeEnum.RULE_AUTO.getCode().equals(scheduleType)) {
                // 根据最早开始时间排序，取最早时间（制造订单最早开始，最新计算最早开始，历史最早开始）取最早
                Date minDate = StreamUtils.getMinDate(workOrderVO.getEarliestStartTime(), earliestBeginTime, oldEarliestBeginTime);
                earliestBeginTime = minDate;
            }
            if (ScheduleTypeEnum.HANDWORK.getCode().equals(scheduleType) && operation.getEarliestBeginTime() != null) {
                earliestBeginTime = oldEarliestBeginTime.before(earliestBeginTime) ? oldEarliestBeginTime : earliestBeginTime;
            }
            operation.setEarliestBeginTime(earliestBeginTime);
        });
        algorithmData.setOperations(operations);
    }

    private Map<String, PartRiskLevelVO> mapByProductCode(List<PartRiskLevelVO> list) {
        return list.stream().collect(Collectors.toMap(PartRiskLevelVO::getProductCode, v -> v, (k1, k2) -> k1));
    }

    private Map<String, ProductAdvanceBatchRuleVO> mapByRiskLevel(List<ProductAdvanceBatchRuleVO> list) {
        return list.stream().filter(p -> StrUtil.isNotEmpty(p.getRiskLevel())).collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getRiskLevel, v -> v, (k1, k2) -> k1));
    }

    private List<RzzSpecialLimitData> getSpecialLimit(PlanningHorizonVO planningHorizon) {
        //查询特殊工艺产能约束,主要分为膜系和夹丝两种
        List<ProductionLimitVO> productionLimitVOList = productionLimitService.selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));
        List<CollectionValueVO> factorValueList = ipsFeign.getByCollectionCode("ElEMENT_VALUE");
        Map<String, String> factorMap = factorValueList.stream().collect(Collectors.toMap(CollectionValueVO::getCollectionValue, CollectionValueVO::getValueMeaning, (v1, v2) -> v1));
        List<String> membraneList = factorValueList.stream().filter(t -> t.getValueMeaning().contains("膜")).map(CollectionValueVO::getCollectionValue).collect(Collectors.toList());
        List<Integer> days = productionLimitVOList.stream().map(ProductionLimitVO::getDays).distinct().collect(Collectors.toList());
        Date startTime = planningHorizon.getPlanStartTime();
        Date endTime = planningHorizon.getPlanEndTime();
        Map<Integer, List<Pair<String, String>>> limitDayMap = new HashMap<>();
        for (Integer day : days) {
            List<Pair<String, String>> dayDur = getDayDur(startTime, endTime, day);
            limitDayMap.put(day, dayDur);
        }
        Map<String, RzzSpecialLimitData> specMap = new HashMap<>();
        for (ProductionLimitVO productionLimitVO : productionLimitVOList) {
            String factorValue = productionLimitVO.getFactorValue();
            List<Pair<String, String>> effectiveTimeList = limitDayMap.get(productionLimitVO.getDays());
            if (factorMap.containsKey(factorValue)) {
                RzzSpecialLimitData specialLimitData = new RzzSpecialLimitData();
                //要素值是字典表的value,需要翻译成字典表的meaning
                String clampType = factorMap.get(factorValue);
                //膜系的限制（**除模）
                if (membraneList.contains(factorValue)) {
                    Spec spec = new Spec();
                    spec.setKey("special");
                    spec.setName("special");
                    spec.setValue("special");
                    specialLimitData.setSpec(spec);
                    specialLimitData.setEffectiveTimeList(effectiveTimeList);
                    specialLimitData.setMaxQuantity(new BigDecimal(productionLimitVO.getMainOperationProductionCapacity()));
                    specMap.put("special", specialLimitData);
                } else {
                    //夹丝类型
                    Spec spec = new Spec();
                    spec.setKey(clampType);
                    spec.setName(clampType);
                    spec.setValue(clampType);
                    specialLimitData.setSpec(spec);
                    specialLimitData.setEffectiveTimeList(effectiveTimeList);
                    specialLimitData.setMaxQuantity(new BigDecimal(productionLimitVO.getMainOperationProductionCapacity()));
                    specMap.put(clampType, specialLimitData);
                }
            }
        }
        return new ArrayList<>(specMap.values());
    }

    private Map<String, String> getLoadingPositionMap(List<NewProductStockPointVO> newProductStockPointVOS) {
        //工序后库容量装车位置映射关系
        List<CollectionValueVO> loadingPositionReflectionList = ipsFeign.getByCollectionCode("PRODUCTION_CAPACITY_LOADING_POSITION");
        Map<String, String> loadingPositionReflectionMap = loadingPositionReflectionList.stream().collect(Collectors.toMap(CollectionValueVO::getValueMeaning, CollectionValueVO::getCollectionValue, (v1, v2) -> v1));
        Map<String, String> productIdOfStockAndLoadingPositionMap = new HashMap<>();
        for (NewProductStockPointVO vo : newProductStockPointVOS) {
            if (StringUtils.isNotEmpty(vo.getLoadingPosition())) {
                String loadingPosition = vo.getLoadingPosition();
                if (loadingPositionReflectionMap.containsKey(loadingPosition)) {
                    loadingPosition = loadingPositionReflectionMap.get(vo.getLoadingPosition());
                    ;
                }
                String value = String.join("#", vo.getStockPointCode(), loadingPosition);
                productIdOfStockAndLoadingPositionMap.put(vo.getId(), value);
            }
        }
        return productIdOfStockAndLoadingPositionMap;
    }

    private List<FulfillmentVO> queryFulfillmentByOperationIds(List<String> operationIds) {
        List<FulfillmentVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(operationIds)) {
            return result;
        }
        List<DemandVO> demandVOS = masterPlanExtDao.selectDemandVOByOperationIds(ImmutableMap.of("operationIds", operationIds));
        List<FulfillmentVO> fulfillmentVOListOfDemand = this.createFulfillmentsByDemands(demandVOS);
        if (CollectionUtils.isNotEmpty(fulfillmentVOListOfDemand)) {
            result.addAll(fulfillmentVOListOfDemand);
        }
        List<SupplyVO> supplyVOS = masterPlanExtDao.selectSupplyVOByOperationIds(ImmutableMap.of("operationIds", operationIds));
        List<FulfillmentVO> fulfillmentVOListOfSupply = this.createFulfillmentsBySupplies(supplyVOS);
        if (CollectionUtils.isNotEmpty(fulfillmentVOListOfSupply)) {
            result.addAll(fulfillmentVOListOfSupply);
        }
//        CompletableFuture<Void> demandFuture = CompletableFuture.runAsync(() -> {
//            List<DemandVO> demandVOS = masterPlanExtDao.selectDemandVOByOperationIds(ImmutableMap.of("operationIds", operationIds));
//            List<FulfillmentVO> fulfillmentVOListOfDemand = this.createFulfillmentsByDemands(demandVOS);
//            if (CollectionUtils.isNotEmpty(fulfillmentVOListOfDemand)) {
//                result.addAll(fulfillmentVOListOfDemand);
//            }
//        });
//        CompletableFuture<Void> supplyFuture = CompletableFuture.runAsync(() -> {
//            List<SupplyVO> supplyVOS = masterPlanExtDao.selectSupplyVOByOperationIds(ImmutableMap.of("operationIds", operationIds));
//            List<FulfillmentVO> fulfillmentVOListOfSupply = this.createFulfillmentsBySupplies(supplyVOS);
//            if (CollectionUtils.isNotEmpty(fulfillmentVOListOfSupply)) {
//                result.addAll(fulfillmentVOListOfSupply);
//            }
//        });
//        CompletableFuture.allOf(demandFuture, supplyFuture).join();
        return result;
    }

    private List<FulfillmentVO> createFulfillmentsBySupplies(List<SupplyVO> supplyVOS) {
        if (CollectionUtils.isEmpty(supplyVOS)) {
            log.info("计划排程查询物料分配关系：找不到对应供应");
            return new ArrayList<>();
        }
        List<String> supplyIds = supplyVOS.stream().map(SupplyVO::getId).collect(Collectors.toList());
        List<FulfillmentPO> fulfillmentList = fulfillmentDao.selectByParams(ImmutableMap.of("supplyIds", supplyIds));
        List<FulfillmentVO> fulfillmentVOS = FulfillmentConvertor.INSTANCE.po2Vos(fulfillmentList);
        if (CollectionUtils.isEmpty(fulfillmentList)) {
            log.info("计划排程查询物料分配关系：找不到对应分配关系");
            return new ArrayList<>();
        }
        Map<String, SupplyVO> supplyMaps = supplyVOS.stream().collect(Collectors.toMap(SupplyVO::getId, v -> v, (v1, v2) -> v1));
        List<String> demandIds = fulfillmentList.stream().map(FulfillmentPO::getDemandId).collect(Collectors.toList());
        Map<String, Object> params = new HashMap<>();
        params.put("ids", demandIds);
        List<DemandVO> demands = demandService.selectVOByParams(params);
        Map<String, DemandVO> demandMaps = demands.stream().collect(Collectors.toMap(DemandVO::getId, v -> v, (v1, v2) -> v1));
//        List<String> operationOutputIds = supplyVOS.stream().map(SupplyVO::getOperationOutputId).collect(Collectors.toList());
//        List<OperationOutputPO> operationOutputPOS = operationOutputDao.selectByPrimaryKeys(operationOutputIds);
//        Map<String, String> operationOutputMap = operationOutputPOS.stream().collect(Collectors.toMap(OperationOutputPO::getId, OperationOutputPO::getOperationId, (v1, v2) -> v1));
        List<String> operationInputIds = demands.stream().map(DemandVO::getOperationInputId).collect(Collectors.toList());
        List<OperationInputPO> operationInputPOS = operationInputDao.selectByPrimaryKeys(operationInputIds);
        Map<String, String> operationInputMap = operationInputPOS.stream().collect(Collectors.toMap(OperationInputPO::getId, OperationInputPO::getOperationId, (v1, v2) -> v1));


        List<String> operationIds = operationInputPOS.stream().map(OperationInputPO::getOperationId).collect(Collectors.toList());
        List<OperationPO> operations = operationDao.selectByPrimaryKeys(operationIds);
        Map<String, Integer> operationMap = operations.stream().collect(Collectors.toMap(OperationPO::getId, OperationPO::getRoutingStepSequenceNo, (v1, v2) -> v1));
        List<String> filterIds = new ArrayList<>();
        fulfillmentVOS.forEach(fulfillmentVO -> {
            fulfillmentVO.setDemandProductStockPointId(fulfillmentVO.getDemandProductStockPointId());
            fulfillmentVO.setDemandProductId(fulfillmentVO.getDemandProductId());
            fulfillmentVO.setFulfillmentQuantity(fulfillmentVO.getFulfillmentQuantity());

            DemandVO demand = demandMaps.get(fulfillmentVO.getDemandId());
            if (null == demand) {
                filterIds.add(fulfillmentVO.getId());
                return;
            }
            String operationId = operationInputMap.get(demand.getOperationInputId());
            demand.setOperationId(operationId);
            demand.setSequenceNumber(operationMap.get(operationId));
            fulfillmentVO.setDemandVO(demand);
            SupplyVO supplyVO = supplyMaps.get(fulfillmentVO.getSupplyId());
            fulfillmentVO.setSupplyVO(supplyVO);
        });
        fulfillmentVOS = fulfillmentVOS.stream().filter(t -> !filterIds.contains(t.getId())).collect(Collectors.toList());
        return fulfillmentVOS;
    }

    private List<FulfillmentVO> createFulfillmentsByDemands(List<DemandVO> demandVOS) {
        if (CollectionUtils.isEmpty(demandVOS)) {
            log.info("计划排程查询物料分配关系：找不到对应需求");
            return new ArrayList<>();
        }
        List<String> demandIds = demandVOS.stream().map(DemandVO::getId).collect(Collectors.toList());
        List<FulfillmentPO> fulfillmentList = fulfillmentDao.selectByParams(ImmutableMap.of("demandIds", demandIds));
        if (CollectionUtils.isEmpty(fulfillmentList)) {
            log.info("计划排程查询物料分配关系：找不到对应分配关系");
            return new ArrayList<>();
        }
        Map<String, DemandVO> demandMaps = demandVOS.stream().collect(Collectors.toMap(DemandVO::getId, v -> v, (v1, v2) -> v1));
        List<String> supplyIds = fulfillmentList.stream().map(FulfillmentPO::getSupplyId).collect(Collectors.toList());
        Map<String, Object> params = new HashMap<>();
        params.put("ids", supplyIds);
        // List<SupplyVO> supplies = supplyBasicDao.selectVOByParams(params);
        // List<SupplyVO> supplies = supplyService.selectByParams(params);
        List<SupplyVO> supplies = supplyBasicDao.selectSuppliesViewByParams(params);
        Map<String, SupplyVO> supplyMaps = supplies.stream().collect(Collectors.toMap(SupplyVO::getId, v -> v, (v1, v2) -> v1));

        List<String> operationInputIds = demandVOS.stream().map(DemandVO::getOperationInputId).collect(Collectors.toList());

        List<OperationInputPO> operationInputPOS = operationInputDao.selectByPrimaryKeys(operationInputIds);
        Map<String, String> operationInputMap = operationInputPOS.stream().collect(Collectors.toMap(OperationInputPO::getId, OperationInputPO::getOperationId, (v1, v2) -> v1));
        List<String> operationIds = operationInputPOS.stream().map(OperationInputPO::getOperationId).collect(Collectors.toList());

        List<OperationPO> operations = operationDao.selectByPrimaryKeys(operationIds);
        Map<String, Integer> operationMap = operations.stream().collect(Collectors.toMap(OperationPO::getId, OperationPO::getRoutingStepSequenceNo, (v1, v2) -> v1));

        List<FulfillmentVO> fulfillmentVOS = FulfillmentConvertor.INSTANCE.po2Vos(fulfillmentList);
        List<String> filterIds = new ArrayList<>();
        fulfillmentVOS.forEach(fulfillmentVO -> {
            fulfillmentVO.setDemandProductStockPointId(fulfillmentVO.getDemandProductStockPointId());
            fulfillmentVO.setDemandProductId(fulfillmentVO.getDemandProductId());
            fulfillmentVO.setFulfillmentQuantity(fulfillmentVO.getFulfillmentQuantity());
            DemandVO demand = demandMaps.get(fulfillmentVO.getDemandId());
            if (null == demand) {
                filterIds.add(fulfillmentVO.getId());
                return;
            }
            String operationId = operationInputMap.get(demand.getOperationInputId());
            demand.setOperationId(operationId);
            demand.setSequenceNumber(operationMap.get(operationId));
            fulfillmentVO.setDemandVO(demand);
            SupplyVO supplyVO = supplyMaps.get(fulfillmentVO.getSupplyId());
            fulfillmentVO.setSupplyVO(supplyVO);
        });
        fulfillmentVOS = fulfillmentVOS.stream().filter(t -> !filterIds.contains(t.getId())).collect(Collectors.toList());
        return fulfillmentVOS;
    }

    /**
     * 查询需要排程的工序数据
     *
     * @return
     */
    private List<OperationVO> getNeedOperations() {
        Map<String, Object> params = new HashMap<>();
        params.put("planStatusList", Arrays.asList(PlannedStatusEnum.UNPLAN.getCode(), PlannedStatusEnum.SOME_PLANNED.getCode()));
        List<OperationVO> unPlanOperations = operationDao.selectVOByParams(params);
        if (CollUtil.isEmpty(unPlanOperations)) {
            return new ArrayList<>();
        }
        //未计划只考虑父工序（item.getParentId()==null代表父工序）
        List<String> unPlanedParentOperationIds = unPlanOperations.stream().filter(item -> item.getParentId() == null).map(OperationVO::getId).distinct().collect(Collectors.toList());
        //未计划子工序的父工序
        List<String> unPlanedParentOperationIds2 = unPlanOperations.stream().filter(item -> item.getParentId() != null).map(OperationVO::getParentId).distinct().collect(Collectors.toList());
        //取并集，获取所有需要排程的父工序（告诉算法待排的父工序有哪些）
        List<OperationVO> unPlanedParentOperations = selectByPrimaryKeys(CollectionUtils.getUnionSection(unPlanedParentOperationIds, unPlanedParentOperationIds2));
        List<OperationVO> needOperations = new ArrayList<>();
        needOperations = unPlanedParentOperations;
        return needOperations;
    }

    public List<OperationVO> selectByPrimaryKeys(List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return operationDao.selectVOByPrimaryKeys(ids);
    }

    /**
     * 排程期间已计划(排在资源上)的工序
     *
     * @return
     */
    public List<OperationVO> getOtherOperations(PlanningHorizonVO planningHorizon, List<String> needOpIds) {
        // 已计划工序
        List<OperationVO> operations = operationService.getPlanedOperationsByParams(planningHorizon.getPlanStartTime(), null, new ArrayList<>());
        List<String> operationIds = operations.stream().map(OperationVO::getParentId).collect(Collectors.toList());
        // 已计划工序父工序
        List<OperationVO> parentOperations = operationService.getVOListByIds(operationIds);
        // 排除需要排程的父工序
        operations.addAll(parentOperations.stream().filter(item -> !needOpIds.contains(item.getId())).collect(Collectors.toList()));
        log.info("getOtherOperations result size is {}", operations.size());
        return operations;
    }

    private List<ResourceCalendarVO> getResourceCalendarsInPlanningHorizonByResourceIds(List<String> physicalResourceIds) {
        Map<String, Object> params = new HashMap<>();
        params.put("physicalResourceIds", physicalResourceIds);
        params.put("calendarTypes", Lists.newArrayList("NORMAL", "OVERTIME"));
        params.put("enabled", "YES");
        return operationTaskExtDao.selectResourceCalendarsByParams(params);
    }

    private Map<String, List<ProAbnormalFeedbackVO>> selectProAbnormalFeedbackByResources(List<PhysicalResourceVO> resources) {
        Map<String, List<ProAbnormalFeedbackVO>> proAbnormalFeedbackMap = MapUtil.newHashMap();
        if (CollectionUtils.isEmpty(resources)) {
            return proAbnormalFeedbackMap;
        }
        List<String> resourceCodes = resources.stream().map(PhysicalResourceVO::getPhysicalResourceCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resourceCodes)) {
            return proAbnormalFeedbackMap;
        }
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("resourceCodes", resourceCodes);
        params.put("enabled", YesOrNoEnum.YES.getCode());
        List<ProAbnormalFeedbackVO> proAbnormalFeedbacks = proAbnormalFeedbackService.selectByParams(params);
        if (CollectionUtils.isEmpty(proAbnormalFeedbacks)) {
            return proAbnormalFeedbackMap;
        }
        return proAbnormalFeedbacks.stream().collect(Collectors.groupingBy(ProAbnormalFeedbackVO::getResourceCode));
    }

    private Map<String, List<RzzResourceData>> getResourceMap(List<PhysicalResourceVO> resources, PlanningHorizonVO planningHorizonVO,
                                                              String postponeRule, List<String> infinitResourceIdList,
                                                              AlgorithmLog algorithmLog, List<RzzOperationData> operations,
                                                              List<String> formatOperations, Date resourceStarTime) {
        Map<String, List<RzzResourceData>> resourceDataMap = new HashedMap<>();
        List<String> physicalResourceIds = resources.stream().map(PhysicalResourceVO::getId).collect(Collectors.toList());
        List<ResourceCalendarVO> resourceCalendars = this.getResourceCalendarsInPlanningHorizonByResourceIds(physicalResourceIds);
        if (CollUtil.isEmpty(resourceCalendars)) {
            throw new BusinessException("计划期间内未维护资源日历");
        }
        //根据resourceId分组
        Map<String, List<ResourceCalendarVO>> resourceCalendarMap = resourceCalendars.stream().collect(Collectors.groupingBy(ResourceCalendarVO::getPhysicalResourceId));
        Map<String, List<ProAbnormalFeedbackVO>> abnormalFeedbackMap = this.selectProAbnormalFeedbackByResources(resources);
        resources = resources.stream().filter(k -> StringUtils.isNotBlank(k.getResourceType())).collect(Collectors.toList());
        //根据resourceType 分组
        Map<String, List<PhysicalResourceVO>> reMap = resources.stream().collect(Collectors.groupingBy(PhysicalResourceVO::getResourceType));


        if (Objects.nonNull(algorithmLog)) {
            String creator = algorithmLog.getCreator();
            List<PhysicalResourceVO> infiniteCapacityResourceIds = getInfiniteCapacityResourceIds(creator);
            if (StringUtils.isNotEmpty(algorithmLog.getProductLine())) {
                // 过滤的有限能力资源,无限能力资源不受影响
                List<String> productLines = Arrays.stream(algorithmLog.getProductLine().split(",")).collect(Collectors.toList());
                infiniteCapacityResourceIds = infiniteCapacityResourceIds.stream().filter(t -> productLines.contains(t.getPhysicalResourceCode())).collect(Collectors.toList());
            }
            List<PhysicalResourceVO> filteredResources = filterResourcesByCapacity(resources, infiniteCapacityResourceIds);
            physicalResourceIds = filteredResources.stream().map(PhysicalResourceVO::getId).collect(Collectors.toList());
            log.info("用户：{}，构建物理资源占位数量：{}", creator, physicalResourceIds.size());
            log.info("占位资源：{}", JSON.toJSONString(physicalResourceIds));
        }
        if (CollectionUtils.isEmpty(physicalResourceIds)) throw new BusinessException("用户无可用资源，请检查数据");
        Map<String, Object> subTaskParams = Maps.newHashMap();
        subTaskParams.put("physicalResourceIds", physicalResourceIds);
        List<OperationSubTaskVO> operationSubTasks = operationSubTaskService.selectByParams(subTaskParams);
        log.info("资源占位数据行：{}", operationSubTasks.size());
        List<String> ids = operations.stream().map(RzzOperationData::getOperationId).collect(Collectors.toList());
        operationSubTasks = operationSubTasks.stream().filter(p -> ids.contains(p.getOperationId())).collect(Collectors.toList());
        log.info("工序判断后资源占位数据行：{}", operationSubTasks.size());
        //根据resourceId分组
        List<RzzOperationData> handleResourceTaskList = StreamUtils.filter(operations, p -> StrUtil.isNotEmpty(p.getCurrentPlannedResourceId()));
        handleResourceTask(handleResourceTaskList, operationSubTasks, planningHorizonVO);
        Map<String, List<OperationSubTaskVO>> resourceSubTaskMap = operationSubTasks.stream().collect(Collectors.groupingBy(OperationSubTaskVO::getPhysicalResourceId));

        List<CollectionValueVO> shiftType = ipsFeign.getByCollectionCode("SHIFT_TYPE");
        Map<String, CollectionValueVO> shiftTypeMap = shiftType.stream().collect(Collectors.toMap(CollectionValueVO::getCollectionValue, Function.identity(), (v1, v2) -> v1));

        for (Map.Entry<String, List<PhysicalResourceVO>> entry : reMap.entrySet()) {
            String resourceType = entry.getKey();
            List<RzzResourceData> resourceDatas = new ArrayList<>();
            for (PhysicalResourceVO resource : entry.getValue()) {
                RzzResourceData resourceData = new RzzResourceData();
                resourceData.setResourceId(resource.getId());
                resourceData.setGroupId(resource.getStandardResourceId());
                resourceData.setResourceCode(resource.getPhysicalResourceCode());
                boolean infiniteFlag = YesOrNoEnum.YES.getCode().equals(resource.getInfiniteCapacity());
                resourceData.setInfinity(infiniteFlag);
                //顺延规则为无限能力叠加并且拖拽资源不为空
                if (StringUtils.equals(postponeRule, PostponeRuleEnum.INFINITE_ABILITY_STACKS.getCode()) && CollectionUtils.isNotEmpty(infinitResourceIdList)) {
                    if (infinitResourceIdList.contains(resource.getId())) {
                        resourceData.setInfinity(true);
                    }
                }
                resourceData.setObserveLineRules(YesOrNoEnum.YES.getCode().equals(resource.getStrictProductionLineConstraints()));

                resourceData.setOverlapType(resource.getAssignQuantityType());
                //多重能力资源、炉资源
                if (infiniteFlag) {
                    resourceData.setMaxBatchSize(9999);
                } else {
                    String resourceQuantityCoefficient = resource.getResourceQuantityCoefficient();
                    resourceData.setMaxBatchSize(StringUtils.isNotEmpty(resourceQuantityCoefficient) ? Integer.valueOf(resourceQuantityCoefficient) : null);
                }
                resourceData.setResourceType(resource.getResourceCategory());
                if (resource.getBufferTimeBefore() != null) {
                    resourceData.setBufferTimeBefore(resource.getBufferTimeBefore().toString());
                }
                if (resource.getBufferTimeAfter() != null) {
                    resourceData.setBufferTimeAfter(resource.getBufferTimeAfter().toString());
                }

                resourceData.setProcessDurationLogic(ResourceData.PROCESS_DURATION_LOGIC_MAP.get(resource.getProductionDurationLogic()));

                resourceData.setDynamicSetupandCleanupDurationLogic(ResourceData.DYNAMIC_MAP.get(resource.getDynamicSetupAndCleanupDurationLogic()));
                resourceData.setSetupandCleanupDurationLogic(ResourceData.PROCESS_DURATION_LOGIC_MAP.get(resource.getSetupAndCleanupDurationLogic()));
                if (StringUtils.isNotBlank(resource.getSubtaskType())) {
                    List<String> subTaskTypes = new ArrayList<>();
                    String[] taskTypes = resource.getSubtaskType().split(",");
                    for (String taskType : taskTypes) {
                        subTaskTypes.add(ResourceData.SUB_TASK_MAP.get(taskType));
                    }
                    resourceData.setSubTaskTypes(subTaskTypes);
                }
                resourceData.setVariableWorkingHours(YesOrNoEnum.YES.getCode().equals(resource.getVariableWorkHours()));
                resourceData.setLine(resource.getProductionLine());

                resourceData.setSpecReqs(new ArrayList<>());

                if (resourceCalendarMap.get(resource.getId()) != null) {
                    resourceData.setAvailableTimes(getAvailableTimes(resourceCalendarMap.get(resource.getId()), resourceStarTime, planningHorizonVO.getPlanEndTime(), resource, shiftTypeMap));
                }
                if (abnormalFeedbackMap.containsKey(resource.getPhysicalResourceCode())) {
                    resourceData.setAbnormalTimes(getAbnormalTimes(abnormalFeedbackMap.get(resource.getPhysicalResourceCode()), resourceStarTime, planningHorizonVO.getPlanEndTime()));
                }
                //资源占位
                if (!CollUtil.isEmpty(resourceSubTaskMap.get(resource.getId()))) {
                    resourceData.setFixedSchedules(getFixedSchedules(resourceSubTaskMap.get(resource.getId())));
                }

                resourceDatas.add(resourceData);

            }
            resourceDataMap.put(RzzBaseAlgorithmData.RESOURCE_TYPE_TRANS_MAP.get(resourceType), resourceDatas);
        }
        return resourceDataMap;
    }

    private void handleResourceTask(List<RzzOperationData> handleResourceTaskList,
                                    List<OperationSubTaskVO> operationSubTasks,
                                    PlanningHorizonVO planningHorizonVO) {
        Date planEndTime = planningHorizonVO.getPlanEndTime();
        Iterator<OperationSubTaskVO> iterator = operationSubTasks.iterator();
        Map<String, RzzOperationData> operationDataMap = StreamUtils.mapByColumn(handleResourceTaskList, RzzOperationData::getOperationId);
        // 用于收集需要补充的子任务
        List<OperationSubTaskVO> supplyOperation = new ArrayList<>();
        // 用于跟踪已处理的工序ID
        Set<String> processedOperationIds = new HashSet<>();
        // 合并循环：一边处理现有任务，一边收集需要补充的任务
        while (iterator.hasNext()) {
            OperationSubTaskVO operationSubTaskVO = iterator.next();
            String operationId = operationSubTaskVO.getOperationId();
            if (operationDataMap.containsKey(operationId)) {
                RzzOperationData rzzOperationData = operationDataMap.get(operationId);
                String currentPlannedResourceId = rzzOperationData.getCurrentPlannedResourceId();
                if (currentPlannedResourceId != null) {
                    String currentPlannedResourceType = rzzOperationData.getCurrentPlannedResourceType();
                    String taskType = operationSubTaskVO.getTaskType();
                    // 如果任务类型不匹配，则移除该子任务
                    if (!currentPlannedResourceType.contains(taskType)) {
                        iterator.remove();
                    } else {
                        // 更新资源ID和时间
                        operationSubTaskVO.setPhysicalResourceId(currentPlannedResourceId);
                        operationSubTaskVO.setStartTime(planEndTime);
                        operationSubTaskVO.setEndTime(planEndTime);
                        log.info("更换资源工序：{}，更换资源：{}", operationId, currentPlannedResourceId);
                    }
                    // 如果这是第一次处理该工序，则检查是否需要补充其他任务类型
                    if (processedOperationIds.add(operationId)) {
                        List<OperationSubTaskVO> relatedTasks = operationSubTasks.stream()
                                .filter(t -> operationId.equals(t.getOperationId()))
                                .collect(Collectors.toList());
                        // 创建现有任务类型的集合
                        Set<String> existingTypes = relatedTasks.stream()
                                .map(OperationSubTaskVO::getTaskType)
                                .collect(Collectors.toSet());
                        // 检查是否需要补充其他任务类型
                        String[] requiredTypes = currentPlannedResourceType.split(",");
                        for (String type : requiredTypes) {
                            if (!existingTypes.contains(type)) {
                                OperationSubTaskVO newTask = OperationSubTaskVO.builder()
                                        .id(operationSubTaskVO.getId())
                                        .taskType(type)
                                        .operationId(operationSubTaskVO.getOperationId())
                                        .taskId(operationSubTaskVO.getTaskId())
                                        .physicalResourceId(currentPlannedResourceId)
                                        .startTime(planEndTime)
                                        .endTime(planEndTime)
                                        .enabled(operationSubTaskVO.getEnabled())
                                        .build();
                                supplyOperation.add(newTask);
                            }
                        }
                    }
                }
            }
        }
        // 添加补充的任务
        operationSubTasks.addAll(supplyOperation);
    }


    /**
     * 获取异常时间
     *
     * @param proAbnormalFeedbackVOS
     * @param beginTime
     * @param endTime
     * @return
     */
    private List<RzzResourceAvailableTime> getAbnormalTimes(List<ProAbnormalFeedbackVO> proAbnormalFeedbackVOS, Date beginTime, Date endTime) {
        List<RzzResourceAvailableTime> abnormalTimes = Lists.newArrayList();
        if (CollectionUtils.isEmpty(proAbnormalFeedbackVOS)) {
            return abnormalTimes;
        }
        proAbnormalFeedbackVOS.sort(Comparator.comparing(ProAbnormalFeedbackVO::getSubmissionTime));
        for (ProAbnormalFeedbackVO proAbnormalFeedbackVO : proAbnormalFeedbackVOS) {
            Date abnormalStartTime = proAbnormalFeedbackVO.getSubmissionTime();
            Date abnormalEndTime = proAbnormalFeedbackVO.getForecastCloseTime();
            if (Objects.nonNull(proAbnormalFeedbackVO.getAchieveCloseTime())) {
                abnormalEndTime = proAbnormalFeedbackVO.getAchieveCloseTime();
            }
            if (!abnormalStartTime.before(endTime) || !abnormalEndTime.after(beginTime)) {
                // 时间范围外
                continue;
            }
            RzzResourceAvailableTime availableTime = new RzzResourceAvailableTime();
            availableTime.setTimeBegin(abnormalStartTime);
            availableTime.setTimeBeginStr(DateUtils.dateToString(abnormalStartTime, DateUtils.COMMON_DATE_STR1));
            availableTime.setTimeEnd(abnormalEndTime);
            availableTime.setTimeEndStr(DateUtils.dateToString(abnormalEndTime, DateUtils.COMMON_DATE_STR1));
            availableTime.setProcessEfficiency(BigDecimal.ONE);
            abnormalTimes.add(availableTime);
        }
        return abnormalTimes;
    }

    /**
     * 资源占位关键工序权限资源
     */
    private List<PhysicalResourceVO> getInfiniteCapacityResourceIds(String creator) {
        return newMdsFeign.getPlannerPhysicalResource(creator).stream().filter(p -> YesOrNoEnum.NO.getCode().equals(p.getInfiniteCapacity())).collect(Collectors.toList());
    }

    /**
     * 获取资源占位关键工序权限资源（非关键工序资源不校验，关键工序权限校验）
     */
    private List<PhysicalResourceVO> filterResourcesByCapacity(List<PhysicalResourceVO> resources, List<PhysicalResourceVO> infiniteCapacityResource) {
        // 压制台主资源代码，判断是否包含权限，包含则放入全部压制台物理资源
        List<String> yztResourceList = new ArrayList<>();
        List<CollectionValueVO> yztResource = ipsFeign.getByCollectionCode("YZT_RESOURCE");
        if (CollectionUtils.isNotEmpty(yztResource)) {
            yztResourceList.addAll(yztResource.stream().map(CollectionValueVO::getCollectionValue).collect(Collectors.toList()));
        }

        // 判断是否包含压制台资源权限，包含则不对压制台资源权限做控制
        boolean hasYztResourcePermission = infiniteCapacityResource.stream().anyMatch(p -> yztResourceList.stream().anyMatch(yztCode -> yztCode.startsWith(p.getStandardResourceCode())));
        log.info("权限判断是否包含压制台资源：{}", hasYztResourcePermission);

        // 权限ids集合
        List<String> infiniteCapacityResourceIds = infiniteCapacityResource.stream().map(p -> p.getId()).collect(Collectors.toList());

        String yzt = "YZT";
        return resources.stream().filter(p -> {
            String infiniteCapacity = p.getInfiniteCapacity();
            String physicalResourceCode = p.getPhysicalResourceCode();
            // 包含压制台产线组权限，压制台产线不做权限控制
            if (hasYztResourcePermission && physicalResourceCode.startsWith(yzt)) {
                return true;
            }
            if (StrUtil.isEmpty(infiniteCapacity)) {
                return true;
            }
            if (infiniteCapacity.equals(YesOrNoEnum.YES.getCode())) {
                return true;
            }
            if (infiniteCapacityResourceIds.contains(p.getId())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
    }


    private List<RzzResourceAvailableTime> getAvailableTimes(List<ResourceCalendarVO> resourceCalendars, Date planStartDate, Date planEndDate, PhysicalResourceVO resource, Map<String, CollectionValueVO> shiftTypeMap) {
        resourceCalendars.sort(Comparator.comparing(ResourceCalendarBasicVO::getStartTime));
        //获取资源有效期
        Date effectiveTime = resource.getEffectiveTime();
        Date expiryTime = resource.getExpiryTime();
        List<RzzResourceAvailableTime> availableTimes = new ArrayList<>();
        if (effectiveTime != null) {
            effectiveTime = effectiveTime.after(planStartDate) ? effectiveTime : planStartDate;
        } else {
            effectiveTime = planStartDate;
        }
        if (expiryTime != null) {
            expiryTime = expiryTime.before(planEndDate) ? expiryTime : planEndDate;
        } else {
            expiryTime = planEndDate;
        }


        for (ResourceCalendarVO resourceCalendar : resourceCalendars) {
            if ("正常".equals(shiftTypeMap.get(resourceCalendar.getCalendarType()).getDescription())) {
                RzzResourceAvailableTime availableTime = new RzzResourceAvailableTime();
                Date timeBegin = resourceCalendar.getStartTime();
                Date endTime = resourceCalendar.getEndTime();
                if (timeBegin.after(expiryTime)) {
                    continue;
                }
                if (endTime.before(effectiveTime)) {
                    continue;
                }

                if (effectiveTime.after(resourceCalendar.getStartTime())) {
                    timeBegin = effectiveTime;
                }
                availableTime.setTimeBegin(timeBegin);
                availableTime.setTimeBeginStr(DateUtils.dateToString(timeBegin, DateUtils.COMMON_DATE_STR1));
                if (expiryTime != null && expiryTime.before(endTime)) {
                    endTime = expiryTime;
                }
                availableTime.setTimeEnd(endTime);
                availableTime.setTimeEndStr(DateUtils.dateToString(endTime, DateUtils.COMMON_DATE_STR1));
                availableTime.setProcessEfficiency(resourceCalendar.getEfficiency());
                availableTimes.add(availableTime);
            }

        }
        return availableTimes;
    }

    /**
     * 处理 [资源] -[占位列表]
     *
     * @param operationSubTasks 占位列表原数据
     * @return map->key(setupSchedule 设置任务; processSchedule 制造任务 ;cleanupSchedule 清理任务; ) 资源占位
     */
    private List<Map<String, Schedule>> getFixedSchedules(List<OperationSubTaskVO> operationSubTasks) {
        List<Map<String, Schedule>> fixedScheduleMapList = new ArrayList<>();

        // 1-根据operationId##tasktype分类 <operationId-taskType,List<OperationSubTask>>
        // 使用 LinkedHashMap 按照插入顺序排序
        Map<String, List<OperationSubTaskVO>> operationTaskMap = new LinkedHashMap<>();
        for (OperationSubTaskVO operationSubTask : operationSubTasks) {
            // 根据工序ID+任务类型 生成唯一建
            String key = operationSubTask.getOperationId() + "##" + operationSubTask.getTaskType();
            List<OperationSubTaskVO> operationSubTaskList = operationTaskMap.get(key);
            if (operationSubTaskList == null) {
                operationSubTaskList = new ArrayList<>();
            }
            operationSubTaskList.add(operationSubTask);
            operationTaskMap.put(key, operationSubTaskList);
        }

        // 2-根据
        for (Map.Entry<String, List<OperationSubTaskVO>> entry : operationTaskMap.entrySet()) {
            Map<String, Schedule> fixedScheduleMap = new HashMap<>();
            Schedule schedule = new Schedule();
            // 2.1 某工序下的时间集合
            List<OccupyTime> occupyTimes = new ArrayList<>();
            for (OperationSubTaskVO operationSubTask : entry.getValue()) {
                OccupyTime occupyTime = new OccupyTime();
                Date startTime = operationSubTask.getStartTime();
                Date endTime = operationSubTask.getEndTime();
                if (null == startTime || null == endTime) {
                    log.warn("工序：{}，子任务时间块为空", operationSubTask.getOperationId());
                    continue;
                }
                occupyTime.setTimeBegin(DateUtils.dateToString(startTime, DateUtils.COMMON_DATE_STR1));
                occupyTime.setTimeEnd(DateUtils.dateToString(endTime, DateUtils.COMMON_DATE_STR1));
                occupyTimes.add(occupyTime);
            }
            if (CollectionUtils.isEmpty(occupyTimes)) {
                continue;
            }
            schedule.setOperationId(entry.getKey().split("##")[0]);
            schedule.setOccupyTime(occupyTimes);
            // 2.2 根据任务类型，将不同的工序放入不同类型的占位列表中
            fixedScheduleMap.put(ResourceData.SUB_TASK_MAP.get(entry.getKey().split("##")[1]), schedule);
            fixedScheduleMapList.add(fixedScheduleMap);
        }

        return fixedScheduleMapList;
    }

    /**
     * 组装算法制造订单数据
     *
     * @param workOrders
     * @param specWorkOrderCollectOfWorkOrderId
     * @param specProductCollectOfPointId
     * @param specRoutingCollectOfRoutingId
     * @return
     */
    private List<RzzWorkOrderData> getWorkOrders(List<WorkOrderVO> workOrders, Map<String, List<SpecBusinessVO>> specWorkOrderCollectOfWorkOrderId, Map<String, List<SpecBusinessVO>> specProductCollectOfPointId, Map<String, List<SpecBusinessVO>> specRoutingCollectOfRoutingId) {

        List<RzzWorkOrderData> workOrderDatas = new ArrayList<>();
        for (WorkOrderVO workOrder : workOrders) {
            RzzWorkOrderData workOrderData = new RzzWorkOrderData();
            workOrderData.setId(workOrder.getId());
            workOrderData.setDueDate(workOrder.getDueDate());
            workOrderData.setEarliestBeginTime(workOrder.getEarliestStartTime());
            workOrderData.setPriority(workOrder.getPriority());
            workOrderData.setProductId(workOrder.getProductId());
            workOrderData.setQuantity(workOrder.getQuantity());
            workOrderData.setStockPointId(workOrder.getStockPointId());
            workOrderData.setEndingInventoryMinSafeDiff(workOrder.getEndingInventoryMinSafeDiff());
            workOrderData.setMatchCode(workOrder.getMatchCode());
            workOrderData.setSymmetryCode(workOrder.getSymmetryCode());
            String topOrderId = workOrder.getTopOrderId();
            if (StringUtils.isNotBlank(topOrderId)) {
                List<RzzWorkOrderData> topWorkOrderDatas = new ArrayList<>();
                List<String> topOrderIdList = Arrays.asList(topOrderId.split(","));
                List<WorkOrderVO> topWorkOrders = workOrders.stream().filter(item -> topOrderIdList.contains(item.getOrderNo())).collect(Collectors.toList());
                for (WorkOrderVO topWorkOrder : topWorkOrders) {
                    RzzWorkOrderData topWorkOrderData = new RzzWorkOrderData();
                    topWorkOrderData.setId(topWorkOrder.getId());
                    topWorkOrderData.setDueDate(topWorkOrder.getDueDate());
                    topWorkOrderData.setEarliestBeginTime(topWorkOrder.getEarliestStartTime());
                    topWorkOrderData.setPriority(topWorkOrder.getPriority());
                    topWorkOrderData.setProductId(topWorkOrder.getProductId());
                    topWorkOrderData.setQuantity(topWorkOrder.getQuantity());
                    topWorkOrderDatas.add(topWorkOrderData);
                }
                workOrderData.setTopWorkOrderList(topWorkOrderDatas);
            } else {
                workOrderData.setTopWorkOrderList(new ArrayList<>());
            }

//            List<Map<String, Spec>> specDataMapList = new ArrayList<>();
            //获取制造订单规格
//            List<SpecBusinessVO> specSupplyOrders = specWorkOrderCollectOfWorkOrderId.get(workOrder.getId());
//            if (CollectionUtils.isNotEmpty(specSupplyOrders)) {
////                assembleSpecDataMapList(specSupplyOrders, SpecKeyEnum.WORK_ORDER.getCode(), specDataMapList);
//            }
//            //获取物品规格
//            List<SpecBusinessVO> specProductList = specProductCollectOfPointId.get(workOrder.getProductStockPointId());
//            if (CollectionUtils.isNotEmpty(specProductList)) {
////                assembleSpecDataMapList(specProductList, SpecKeyEnum.PRODUCT_STOCK_POINT.getCode(), specDataMapList);
//            }
//            //获取工艺路径规格
//            List<SpecBusinessVO> specRoutingList = specRoutingCollectOfRoutingId.get(workOrder.getRoutingId());
//            if (CollectionUtils.isNotEmpty(specRoutingList)) {
//                assembleSpecDataMapList(specRoutingList, SpecKeyEnum.ROUTING.getCode(), specDataMapList);
//            }
            String currencyUnitId = workOrder.getCurrencyUnitId();
            if (StringUtils.isNotBlank(currencyUnitId) && NumberUtils.isCreatable(currencyUnitId)) {
                Integer i = Integer.valueOf(currencyUnitId);
                workOrderData.setMoldQuantity(i < 1 ? 1 : i);
            } else {
                workOrderData.setMoldQuantity(1);
            }
            workOrderData.setSpec(new ArrayList<>());
            workOrderDatas.add(workOrderData);
        }
        return workOrderDatas;
    }

    private void assembleInputAndOutputSpecData(List<String> routingIdList, Map<String, RoutingStepInputVO> routingStepInputMapOfJoinKey, Map<String, RoutingStepOutputVO> routingStepOutputMapOfJoinKey, Map<String, List<SpecBusinessVO>> specRoutingStepInputCollectOfInputId, Map<String, List<SpecBusinessVO>> specRoutingStepOutputCollectOfOutputId) {
        List<RoutingStepInputVO> routingStepInputItems = new ArrayList<>();
        List<RoutingStepOutputVO> routingStepOutputItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(routingIdList)) {
            routingStepInputItems = mdsFeign.selectRoutingStepInputByParams(ImmutableMap.of("routingIds", routingIdList));
            routingStepOutputItems = mdsFeign.selectRoutingStepOutputByParams(ImmutableMap.of("routingIds", routingIdList));
        }
        //根据工艺路径步骤ID+库存点物品ID组装数据
        if (CollectionUtils.isNotEmpty(routingStepInputItems)) {
            routingStepInputMapOfJoinKey.putAll(routingStepInputItems.stream().collect(Collectors.toMap(item -> String.join("&", item.getRoutingStepId(), item.getInputProductId()), Function.identity(), (v1, v2) -> v1)));
        }
        if (CollectionUtils.isNotEmpty(routingStepOutputItems)) {
            routingStepOutputMapOfJoinKey.putAll(routingStepOutputItems.stream().collect(Collectors.toMap(item -> String.join("&", item.getRoutingStepId(), item.getOutputProductId()), Function.identity(), (v1, v2) -> v1)));
        }
    }

    private List<RzzOperationData> getOperations(List<OperationVO> allOperations, List<OperationInputVO> inputMaterials, List<OperationOutputVO> outMaterials, String scheduleType, String handworkType, Map<String, List<SpecBusinessVO>> specRoutingStepCollectOfStepId, Map<String, List<SpecBusinessVO>> specRoutingStepInputCollectOfInputId, Map<String, RoutingStepInputVO> routingStepInputItemMapOfJoinKey, Map<String, List<SpecBusinessVO>> specRoutingStepOutputCollectOfOutputId, Map<String, RoutingStepOutputVO> routingStepOutputItemMapOfJoinKey, Map<String, List<SpecBusinessVO>> specProductCollectOfPointId, RzzBaseAlgorithmData algorithmData, Map<String, NewProductStockPointVO> newProductStockPointMap, List<PhysicalResourceVO> resources, Set<String> draPhysicalResource, AlgorithmLog algorithmLog, Map<String, StandardStepVO> standardStepVOMap, List<RoutingStepResourceVO> routingStepResourceVOS, Map<String, NewProductTrialSubmissionDetailVO> detailVOMap, List<String> szWorkOrderIds, List<String> formatOperations, Map<String, String> workOrderParentIdMap, Map<String, String> operationOnResourceIdMap) {
        List<RzzOperationData> operationDatas = new ArrayList<>();
        //根据planUnitId 分组
        //父工序（parentId为null的是父工序）
        List<OperationVO> parentOperation = allOperations.stream().filter(item -> item.getParentId() == null).collect(Collectors.toList());
        Map<String, List<OperationVO>> parentOperationMap = parentOperation.stream().collect(Collectors.groupingBy(OperationVO::getOrderId));
        //子工序（parentId不为null的为子工序）
        List<OperationVO> subOperation = allOperations.stream().filter(item -> item.getParentId() != null).collect(Collectors.toList());
        Map<String, List<OperationVO>> subOperationList = subOperation.stream().collect(Collectors.groupingBy(OperationVO::getOrderId));
        //根据operationId分组
        Map<String, List<RoutingStepResourceVO>> routingStepResourceMap = StreamUtils.mapListByColumn(routingStepResourceVOS, RoutingStepResourceVO::getRoutingStepId);

        //根据operationId分组
        Map<String, List<OperationInputVO>> inputMaterialMap = inputMaterials.stream().collect(Collectors.groupingBy(OperationInputVO::getOperationId));

        //根据operationId分组
        Map<String, List<OperationOutputVO>> outputMaterialMap = outMaterials.stream().collect(Collectors.groupingBy(OperationOutputVO::getOperationId));

        Map<String, OperationExtendVO> operaExtendMapByOperaId = MapUtil.newHashMap();
        String scenario = SystemHolder.getScenario();
        CompletableFuture<Void> extendFuture = CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            Map<String, Object> operationByParam = new HashMap<>();
            operationByParam.put("operationIds", allOperations.stream().map(OperationVO::getId).collect(Collectors.toList()));
            List<OperationExtendVO> operationExtendVOList = operationExtendService.selectByParams(operationByParam);
            if (CollectionUtils.isEmpty(operationExtendVOList)) {
                return;
            }
            operaExtendMapByOperaId.putAll(operationExtendVOList.stream().collect(Collectors.toMap(OperationExtendVO::getOperationId, Function.identity(), (v1, v2) -> v1)));
            DynamicDataSourceContextHolder.clearDataSource();
        });
        //获取实时库存汇总数据，按照库存点，装车位置，工序编码分组汇总
        Map<String, String> withLoadingPositInventoryMap = MapUtil.newHashMap();
        CompletableFuture<Void> loadingPositFuture = CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<InventoryBatchDetailVO> withLoadingPositInventoryList = this.selectCollectByGroupType("LOADING_POSIT");
            if (CollectionUtils.isEmpty(withLoadingPositInventoryList)) {
                return;
            }
            withLoadingPositInventoryMap.putAll(withLoadingPositInventoryList.stream().collect(Collectors.toMap(t -> String.join("#", t.getStockPointCode(), t.getLoadingPosition(), t.getOperationCode()), InventoryBatchDetailVO::getCurrentQuantity, (v1, v2) -> v1)));
            DynamicDataSourceContextHolder.clearDataSource();
        });
        //获取实时库存汇总数据，按照库存点，工序编码分组汇总
        Map<String, String> withNoLoadingPositionOfCapcityMap = MapUtil.newHashMap();
        CompletableFuture<Void> noLoadingPositFuture = CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<InventoryBatchDetailVO> noLoadingPositInventoryList = this.selectCollectByGroupType("NO_LOADING_POSIT");
            if (CollectionUtils.isEmpty(noLoadingPositInventoryList)) {
                return;
            }
            withNoLoadingPositionOfCapcityMap.putAll(noLoadingPositInventoryList.stream().collect(Collectors.toMap(t -> String.join("#", t.getStockPointCode(), t.getOperationCode()), InventoryBatchDetailVO::getCurrentQuantity, (v1, v2) -> v1)));
            DynamicDataSourceContextHolder.clearDataSource();

        });
        AtomicReference<Map<String, BigDecimal>> feedbackQuantityMap = new AtomicReference<>(MapUtil.newHashMap());
        CompletableFuture<Void> feedbackFuture = CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            Set<String> subOperationIds = subOperation.stream().map(OperationVO::getId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(subOperationIds)) {
                return;
            }
            Map<String, Object> param = new HashMap<>();
            param.put("operationIds", subOperationIds);
            List<FeedbackProductionBasicPO> feedbackProductions = operationTaskExtDao.selectFeedBackQuantityByOperationIds(param);
            if (CollectionUtils.isEmpty(feedbackProductions)) {
                return;
            }
            feedbackQuantityMap.set(feedbackProductions.stream().collect(Collectors.toMap(FeedbackProductionBasicPO::getOperationId, FeedbackProductionBasicPO::getReportingQuantity, (v1, v2) -> v1)));
            DynamicDataSourceContextHolder.setDataSource(scenario);
        });
        CompletableFuture.allOf(extendFuture, loadingPositFuture, noLoadingPositFuture, feedbackFuture).join();
        // 产品工装关系
        List<ProductFixtureRelationPO> productFixtureRelationPOS = productFixtureRelationDao.selectByParams(new HashMap<>());
        Map<String, List<ProductFixtureRelationPO>> productFixtureMap = productFixtureRelationPOS.stream().collect(Collectors.groupingBy(p -> String.join("&", p.getStockPointCode(), p.getProductCode(), p.getStandardStepCode())));
        // 物理资源
        Map<String, PhysicalResourceVO> physicalResourceVOMap = resources.stream().collect(Collectors.toMap(PhysicalResourceVO::getId, Function.identity(), (v1, v2) -> v1));
        Map<String, List<PhysicalResourceVO>> standardPhysicalResourceMap = resources.stream().collect(Collectors.groupingBy(PhysicalResourceVO::getStandardResourceId));
        // 权限物理资源
        List<String> userPhysicalResource = new ArrayList<>();
        if (Objects.nonNull(algorithmLog)) {
            String creator = algorithmLog.getCreator();
            List<PhysicalResourceVO> plannerPhysicalResource = newMdsFeign.getPlannerPhysicalResource(creator);
            // 指定产线组排产
            if (StringUtils.isNotEmpty(algorithmLog.getProductLine())) {
                List<String> productLines = Arrays.stream(algorithmLog.getProductLine().split(",")).collect(Collectors.toList());
                userPhysicalResource = plannerPhysicalResource.stream().filter(t -> productLines.contains(t.getPhysicalResourceCode())).map(PhysicalResourceVO::getId).collect(Collectors.toList());
            } else {
                userPhysicalResource = plannerPhysicalResource.stream().map(PhysicalResourceVO::getId).collect(Collectors.toList());
            }
            log.info("用户：{}，可用物理资源数量：{}", creator, userPhysicalResource.size());
        }
        List<String> finalUserPhysicalResource = new ArrayList<>(userPhysicalResource);

        getYztResource(algorithmData, standardPhysicalResourceMap);

        List<String> errorMessage = new ArrayList<>();
        String dataSource = DynamicDataSourceContextHolder.getDataSource();
        algorithmData.setResourceNotFoundMap(new HashMap<>());
        // 构建父工序
        CompletableFuture<List<RzzOperationData>> parentOperationFuture = CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(dataSource);
            log.info("构建工序父工序数量：{}", parentOperation.size());
            List<RzzOperationData> parentOperationDataList = new ArrayList<>();
            try {
                parentOperationDataList = getOperationData(parentOperationMap, PARENT, scheduleType, handworkType, inputMaterialMap, outputMaterialMap, specRoutingStepCollectOfStepId, specRoutingStepInputCollectOfInputId, routingStepInputItemMapOfJoinKey, specRoutingStepOutputCollectOfOutputId, routingStepOutputItemMapOfJoinKey, specProductCollectOfPointId, operaExtendMapByOperaId, algorithmData, withLoadingPositInventoryMap, withNoLoadingPositionOfCapcityMap, newProductStockPointMap, productFixtureMap, physicalResourceVOMap, draPhysicalResource, errorMessage, finalUserPhysicalResource, standardStepVOMap, routingStepResourceMap, detailVOMap, szWorkOrderIds, formatOperations, feedbackQuantityMap.get(), workOrderParentIdMap, operationOnResourceIdMap);
            } catch (Exception e) {
                log.error("构建工序父工序失败", e);
                errorMessage.add(e.getMessage());
            }

            DynamicDataSourceContextHolder.clearDataSource();
            log.info("构建父序子工序完成");
            return parentOperationDataList;
        });
        // 构建子工序
        CompletableFuture<List<RzzOperationData>> subOperationFuture = CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(dataSource);
            log.info("构建工序子工序数量：{}", subOperation.size());
            List<RzzOperationData> subOperationDataList = new ArrayList<>();
            try {
                subOperationDataList = getOperationData(subOperationList, SUB, scheduleType, handworkType, inputMaterialMap, outputMaterialMap, specRoutingStepCollectOfStepId, specRoutingStepInputCollectOfInputId, routingStepInputItemMapOfJoinKey, specRoutingStepOutputCollectOfOutputId, routingStepOutputItemMapOfJoinKey, specProductCollectOfPointId, operaExtendMapByOperaId, algorithmData, withLoadingPositInventoryMap, withNoLoadingPositionOfCapcityMap, newProductStockPointMap, productFixtureMap, physicalResourceVOMap, draPhysicalResource, errorMessage, finalUserPhysicalResource, standardStepVOMap, routingStepResourceMap, detailVOMap, szWorkOrderIds, formatOperations, feedbackQuantityMap.get(), workOrderParentIdMap, operationOnResourceIdMap);
            } catch (Exception e) {
                log.error("构建工序子工序失败", e);
                errorMessage.add(e.getMessage());
            }
            DynamicDataSourceContextHolder.clearDataSource();
            log.info("构建工序子工序完成");
            return subOperationDataList;
        });
        CompletableFuture.allOf(parentOperationFuture, subOperationFuture).join();
        try {
            List<RzzOperationData> parentOperationDataList = parentOperationFuture.get();
            List<RzzOperationData> subOperationDataList = subOperationFuture.get();
            operationDatas.addAll(parentOperationDataList);
            operationDatas.addAll(subOperationDataList);
            // 处理用户手工维护的固定时长
            buildOperationDataFixedWorkHours(operationDatas);
        } catch (Exception e) {
            log.error("构建工序失败:", e);
            throw new BusinessException("构建工序失败: " + e.getMessage());
        }
        StringBuilder stringBuilder = new StringBuilder();
        if (CollectionUtils.isNotEmpty(errorMessage)) {
            for (String s : errorMessage) {
                stringBuilder.append(s);
                stringBuilder.append("\n");
            }
            throw new BusinessException("构建工序失败: " + stringBuilder);
        }


        return operationDatas;
    }

    /**
     * 如果工序上维护了固定时间，则使用固定时间进行排产
     *
     * @param operationDataList
     */
    private void buildOperationDataFixedWorkHours(List<RzzOperationData> operationDataList) {
        Map<String, RzzOperationData> operationMap = operationDataList.stream()
                .collect(Collectors.toMap(RzzOperationData::getOperationId, Function.identity()));
        for (Map.Entry<String, RzzOperationData> entry : operationMap.entrySet()) {
            RzzOperationData operationData = entry.getValue();
            int quantity = operationData.getQuantity().intValue();
            String parentId = operationData.getParentId();
            RzzOperationData parentOperation;
            if (parentId != null) {
                // 如果是子工序
                parentOperation = operationMap.get(parentId);
            } else {
                parentOperation = operationData;
            }
            if (parentOperation == null || parentOperation.getFixedWorkHours() == null) {
                continue;
            }
            // 需要增加的固定时间，可能是正，负小时，转换为秒
            BigDecimal fixedWorkHours = parentOperation.getFixedWorkHours();
            long addFixedWorkHours = fixedWorkHours.multiply(new BigDecimal(3600)).intValue();
            List<OperationOnResource> operationOnResourceList = operationData.getOperationOnResource();
            operationOnResourceList.forEach(operationOnResource -> {
                TaskDuration processDuration = operationOnResource.getProcessDuration();
                long fixedDurSeconds = processDuration.getFixedDurSeconds();
                // 原本就是固定时间生产模式
                if (fixedDurSeconds > 0) {
                    processDuration.setFixedDurSeconds(fixedDurSeconds + addFixedWorkHours);
                    log.info("工序：{}，增加固定时间：{}", operationData.getOperationId(), addFixedWorkHours);
                } else {
                    // 以节拍模式生产
                    TaskDurationEffective effective = processDuration.getTaskDurationEffective();
                    if (effective != null) {
                        long dur = effective.getDur();
                        // 原本需要的时间
                        long total = dur * quantity;
                        long newDur = total + addFixedWorkHours;
                        if (newDur > 0) {
                            // 该原来的节拍生产为固定时间生产
                            processDuration.setFixedDurSeconds(newDur);
                            processDuration.setTaskDurationEffective(null);
                            log.info("工序：{}，改节拍生产为固定时间：{}", operationData.getOperationId(), newDur);
                        } else {
                            log.info("工序：{}，增加固定时间失败，因为该工序需要时间小于0,原生产时间{}, 变化时间{} ",
                                    operationData.getOperationId(), total, addFixedWorkHours);
                        }
                    }
                }
            });
        }
    }

    private List<InventoryBatchDetailVO> selectCollectByGroupType(String groupType) {
        List<InventoryBatchDetailVO> resultList = Lists.newArrayList();
        if ("LOADING_POSIT".equals(groupType)) {
            //按照库存点，装车位置，工序编码分组统计实时库存数量
            resultList = operationTaskExtDao.selectCollectGroupByLoadingPosit();
        } else {
            //按照库存点, 工序编码分组统计实时库存数量
            resultList = operationTaskExtDao.selectCollectGroupByNoLoadingPosit();
        }
        resultList = resultList.stream().filter(e -> StringUtils.isNotEmpty(e.getOperationCode())).collect(Collectors.toList());
        return resultList;
    }

    private List<RzzOperationData> getOperationData(Map<String, List<OperationVO>> operationMap, String operationType,
                                                    String scheduleType, String handworkType,
                                                    Map<String, List<OperationInputVO>> inputMaterialMap, Map<String, List<OperationOutputVO>> outputMaterialMap,
                                                    Map<String, List<SpecBusinessVO>> specRoutingStepCollectOfStepId, Map<String, List<SpecBusinessVO>> specRoutingStepInputCollectOfInputId, Map<String, RoutingStepInputVO> routingStepInputItemMapOfJoinKey, Map<String, List<SpecBusinessVO>> specRoutingStepOutputCollectOfOutputId, Map<String, RoutingStepOutputVO> routingStepOutputItemMapOfJoinKey, Map<String, List<SpecBusinessVO>> specProductCollectOfPointId, Map<String, OperationExtendVO> operaExtendMapByOperaId, RzzBaseAlgorithmData algorithmData, Map<String, String> withLoadingPositInventoryMap, Map<String, String> withNoLoadingPositionOfCapcityMap, Map<String, NewProductStockPointVO> newProductStockPointMap, Map<String, List<ProductFixtureRelationPO>> productFixtureMap, Map<String, PhysicalResourceVO> physicalResourceVOMap, Set<String> draPhysicalResource, List<String> errorMessage, List<String> userPhysicalResource, Map<String, StandardStepVO> standardStepVOMap, Map<String, List<RoutingStepResourceVO>> routingStepResourceVOS, Map<String, NewProductTrialSubmissionDetailVO> detailVOMap, List<String> szWorkOrderIds, List<String> formatOperations, Map<String, BigDecimal> feedbackQuantityMap, Map<String, String> workOrderParentIdMap, Map<String, String> operationOnResourceIdMap) {
        List<PhysicalResourceVO> physicalResourceVOS = algorithmData.getYztPhysicalResourceVOS();
        List<String> yztResourceList = algorithmData.getYztResourceCodes();
        Integer cleanupSeconds = algorithmData.getYztSwitchSeconds();
        Map<String, LoadingPositionCapacityVO> loadingPositionOfCapcityMap = algorithmData.getLoadingPositionOfCapcityMap();
        List<RzzOperationData> operationDatas = new ArrayList<>();
        for (Map.Entry<String, List<OperationVO>> entry : operationMap.entrySet()) {
            List<OperationVO> subOperations = entry.getValue();
            subOperations = subOperations.stream().sorted(Comparator.comparing(OperationVO::getRoutingStepSequenceNo)).collect(Collectors.toList());
            if (operationType.equals(SUB)) {
                List<OperationVO> sub = subOperations.stream().filter(p -> null == p.getOperationIndex()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(sub)) {
                    subOperations.sort(Comparator.comparing(OperationVO::getOperationIndex));
                }
            }
            List<RzzOperationData> subOperationDatas = new ArrayList<>();
            //与前工序最小时间间隔
            Map<String, Pair<BigDecimal, BigDecimal>> minTimeConstraintMap = new HashMap<>();
            String cxOperationId = null;
            for (int i = 0; i < subOperations.size(); i++) {
                OperationVO operation = subOperations.get(i);
                String standardStepId = operation.getStandardStepId();
                StandardStepVO standardStepVO = standardStepVOMap.get(standardStepId);
                if (Objects.isNull(standardStepVO)) {
                    throw new BusinessException("工序：" + JSON.toJSONString(operation) + "无标准工艺");
                }
                String standardStepType = standardStepVO.getStandardStepType();
                operation.setStandardStepType(standardStepType);
                RzzOperationData operationData = new RzzOperationData();
                operationData.setPlanStatus(operation.getPlanStatus());
                operationData.setOperationId(operation.getId());
                operationData.setWorkOrderId(operation.getOrderId());
                operationData.setStandardStepId(operation.getStandardStepId());
                operationData.setFixedWorkHours(operation.getFixedWorkHours());
                Date earliestStartTime = operation.getEarliestStartTime();
                if (null != earliestStartTime) {
                    operationData.setEarliestBeginTime(earliestStartTime);
                }
                if (SUB.equals(operationType)) {
                    operationData.setParentId(operation.getParentId());
                }
                operationData.setConnType(operation.getConnectionType());
                operationData.setQuantity(operation.getQuantity());
                operationData.setPlanUnitId(operation.getPlanUnitId());
                operationData.setOperationCode(operation.getOperationCode());
                operationData.setRoutingStepSequenceNo(operation.getRoutingStepSequenceNo());
                if (PARENT.equals(operationType)) {
                    if (i > 0) {
                        operationData.setPrevOperationId(subOperations.get(i - 1).getId());
                    }
                    if (i < subOperations.size() - 1) {
                        operationData.setNextOperationId(subOperations.get(i + 1).getId());
                    }
                } else {
                    if (standardStepVOMap.containsKey(standardStepId)) {
                        // 仅关键工序过滤候选资源数据
                        if (standardStepType.equals(StandardStepEnum.FORMING_PROCESS.getCode())) {
                            formatOperations.add(operation.getId());
                        }
                    }
                }
                List<Map<String, Spec>> inputMaterialSpecDataList = new ArrayList<>();
                if (inputMaterialMap.get(operation.getId()) != null) {
                    List<InputMaterial> inputMaterialList = getInputMaterial(inputMaterialMap.get(operation.getId()), specRoutingStepInputCollectOfInputId, routingStepInputItemMapOfJoinKey);
                    for (InputMaterial inputMaterial : inputMaterialList) {
                        if (CollectionUtils.isNotEmpty(inputMaterial.getSpecs())) {
                            inputMaterialSpecDataList.addAll(inputMaterial.getSpecs());
                        }
                        //输入物品规格置空(输入物品规格放在工序里面)
                        inputMaterial.setSpecs(null);
                    }
                    operationData.setOperationInputMaterial(inputMaterialList);
                }

                if (outputMaterialMap.get(operation.getId()) != null) {
                    operationData.setOperationOutputMaterial(getOutputMaterial(outputMaterialMap.get(operation.getId()), specRoutingStepOutputCollectOfOutputId, routingStepOutputItemMapOfJoinKey, specProductCollectOfPointId));
                }
                //工序可排资源
                List<RoutingStepResourceVO> operationStepResourcesForThisOperation = routingStepResourceVOS.get(operation.getRoutingStepId());

                NewProductStockPointVO newProductStockPointVO = newProductStockPointMap.get(operation.getProductId());
                Integer routingStepSequenceNo = operation.getRoutingStepSequenceNo();
                String mainKey = String.join("&", newProductStockPointVO.getStockPointCode(), newProductStockPointVO.getProductCode(), routingStepSequenceNo.toString());
                List<ProductFixtureRelationPO> productFixtureRelation = productFixtureMap.get(mainKey);
                AtomicBoolean containYzt = new AtomicBoolean(false);
                AtomicBoolean containContinuous = new AtomicBoolean(true);
                // 构建工序候选资源
                operationData.setOperationOnResource(getOperationResources(operation, productFixtureRelation, physicalResourceVOMap, draPhysicalResource, algorithmData, physicalResourceVOS, yztResourceList, containYzt, containContinuous, operationStepResourcesForThisOperation, errorMessage, detailVOMap, szWorkOrderIds, workOrderParentIdMap, operationOnResourceIdMap, scheduleType));
                // 处理资源找不到工序，构建成计划期间结束时间
                if (SUB.equals(operationType)) {
                    handleResourceNotFound(operation, operationData, algorithmData);
                }
                if (containYzt.get()) {
                    operationData.setCleanupDuration(Duration.ofSeconds(cleanupSeconds));
                }
                operationData.setWhetherTime(containContinuous.get());
                List<String> operationResourceIds = operationData.getOperationOnResource().stream().map(OperationOnResource::getResourceId).collect(Collectors.toList());
                operationData.setLastScheduled(getLastScheduled(operaExtendMapByOperaId.get(operation.getId())));
                //都传静态时间
                if (operation.getEarliestStartTime() != null) {
                    operationData.setEarliestBeginTime(operation.getEarliestStartTime());
                }
                if (operation.getLatestEndTime() != null) {
                    operationData.setLatestEndTime(operation.getLatestEndTime());
                }
                List<Map<String, Spec>> specDataMapList = new ArrayList<>();

                //获取工艺路径步骤规格
                List<SpecBusinessVO> specRoutingSteps = specRoutingStepCollectOfStepId.get(operation.getRoutingStepId());
                if (CollectionUtils.isNotEmpty(specRoutingSteps)) {
                    assembleSpecDataMapList(specRoutingSteps, SpecKeyEnum.ROUTING_STEP.getCode(), specDataMapList);
                }
                //加入输入物品规格
                if (CollectionUtils.isNotEmpty(inputMaterialSpecDataList)) {
                    specDataMapList.addAll(inputMaterialSpecDataList);
                }

                //加一个默认匹配
                List<String> specialOperation = new ArrayList<>();
                specialOperation.add("*");
                Map<String, String> techonlogyTypeMap = algorithmData.getTechonlogyTypeMap();
                //加入膜系规格
                String gridType = algorithmData.getMembraneSystemMap().get(operation.getProductId());
                if (StringUtils.isNotEmpty(gridType)) {
                    addMembraneSystemSpec(specDataMapList, gridType);
                    if (techonlogyTypeMap.containsKey(gridType)) {
                        specialOperation.add(techonlogyTypeMap.get(gridType));
                    }
                }
                //加入夹丝规格
                String clampType = algorithmData.getClampOfProductMap().get(operation.getProductId());
                if (StringUtils.isNotEmpty(clampType)) {
                    addClampSpec(specDataMapList, clampType);
                    if (techonlogyTypeMap.containsKey(clampType)) {
                        specialOperation.add(techonlogyTypeMap.get(clampType));
                    }
                }

                operationData.setSpecs(specDataMapList);
                if (YesOrNoEnum.YES.getCode().equals(operation.getFrozen())) {
                    operationData.setStatus("locked");
                }
                //在拖拽这个模块传完工的status
                if (StringUtils.equals(operation.getPlanStatus(), PlannedStatusEnum.FINISHED.getCode())) {
                    operationData.setStatus("complete");
                }

                Set<String> tags = new HashSet<>();
                if (StringUtils.isNotEmpty(operation.getStandardStepType())) {
                    tags.add(operation.getStandardStepType());
                }
                operationData.setTags(tags);
                String stockAndLoadingPosition = algorithmData.getProductIdOfStockAndLoadingPositionMap().get(operation.getProductId());
                if (StringUtils.isNotEmpty(stockAndLoadingPosition)) {
                    //维护带装车位置的tag
                    String stockAndLoadingPositionKye = String.join("#", stockAndLoadingPosition, operation.getRoutingStepCode());
                    ProductionCapacityVO productionCapacityVO = algorithmData.getWithLoadingPositionOfCapcityMap().get(stockAndLoadingPositionKye);
                    if (productionCapacityVO != null && productionCapacityVO.getProcedureCapacityActual() != null) {
                        operationData.getTags().add(stockAndLoadingPositionKye);
                        if (!loadingPositionOfCapcityMap.containsKey(stockAndLoadingPositionKye)) {
                            LoadingPositionCapacityVO loadingPositionCapacityVO = new LoadingPositionCapacityVO();
                            loadingPositionCapacityVO.setMaxStorageQuantity(productionCapacityVO.getProcedureCapacityActual());
                            BigDecimal currentQuantity = new BigDecimal(withLoadingPositInventoryMap.getOrDefault(stockAndLoadingPositionKye, "0"));
                            loadingPositionCapacityVO.setCurrentQauantity(currentQuantity.intValue());
                            loadingPositionOfCapcityMap.put(stockAndLoadingPositionKye, loadingPositionCapacityVO);
                        }
                    }

                }
                //维护不带装车位置的tag
                String stockCode = algorithmData.getProductIdOfStockMap().get(operation.getProductId());
                if (StringUtils.isNotEmpty(stockCode)) {
                    String stockCodeKye = String.join("#", stockCode, operation.getRoutingStepCode());
                    ProductionCapacityVO productionCapacityVO = algorithmData.getWithNotLoadingPositionOfCapcityMap().get(stockCodeKye);
                    if (productionCapacityVO != null && productionCapacityVO.getProcedureCapacityActual() != null) {
                        operationData.getTags().add(stockCodeKye);
                        if (!loadingPositionOfCapcityMap.containsKey(stockCodeKye)) {
                            LoadingPositionCapacityVO loadingPositionCapacityVO = new LoadingPositionCapacityVO();
                            loadingPositionCapacityVO.setMaxStorageQuantity(productionCapacityVO.getProcedureCapacityActual());
                            BigDecimal currentQuantity = new BigDecimal(withNoLoadingPositionOfCapcityMap.getOrDefault(stockCodeKye, "0"));
                            loadingPositionCapacityVO.setCurrentQauantity(currentQuantity.intValue());
                            loadingPositionOfCapcityMap.put(stockCodeKye, loadingPositionCapacityVO);
                        }
                    }
                }

                //连续生产标记
                if ("钢化".equals(operation.getRoutingStepName())) {
                    if (algorithmData.getContinuousProductionProcessIds().contains(operation.getOrderId())) {
                        operationData.getTags().add("连续生产");
                    }
                    operationData.getTags().add("delivery");
                    operationData.getTags().add("forming");
                }
                //delivery，forming，coating 拆批插单规则
                if ("镀膜".equals(operation.getRoutingStepName())) {
                    operationData.getTags().add("delivery");
                    operationData.getTags().add("coating");
                }
                if ("压制".equals(operation.getRoutingStepName()) || "成型".equals(operation.getRoutingStepName())) {
                    operationData.getTags().add("delivery");
                    operationData.getTags().add("forming");
                    if (i > 0) {
                        //镀膜-成型工序之间设置间隔时间
                        OperationVO prevOperationVO = subOperations.get(i - 1);
                        if ("镀膜".equals(prevOperationVO.getRoutingStepName())) {
                            String max = algorithmData.ruleValue(AlgorithmConstraintRuleEnum.COMMON_RULE_11.getCode());
                            operationData.setMinTimeConstraint(max == null ? "0" : max);
                            String min = algorithmData.ruleValue(AlgorithmConstraintRuleEnum.COMMON_RULE_12.getCode());
                            operationData.setMaxTimeConstraint(min == null ? "0" : min);
                            cxOperationId = operation.getId();
                        }
                    }

                }
                if (PARENT.equals(operationType)) {
                    //当前工序候选资源中优先级最高的资源所在的产线组
                    OperationOnResource resource = operationData.getOperationOnResource().stream().min(Comparator.comparing(OperationOnResource::getPriority)).orElse(null);
                    String standResourceCode = resource == null ? "*" : physicalResourceVOMap.get(resource.getResourceId()).getStandardResourceCode();
                    //如果是关键工序
                    if ("FORMING_PROCESS".equals(operation.getStandardStepType())) {
                        String lineGroup = algorithmData.getLineGroupOfProductMap().get(operation.getProductId());
                        if (StringUtils.isNotEmpty(lineGroup)) {
                            standResourceCode = lineGroup;
                        }
                    }
                    //调光
                    String itemFlag = algorithmData.getItemFlagOfProductMap().get(operation.getProductId());
                    if (StringUtils.isNotEmpty(itemFlag) && techonlogyTypeMap.containsKey(itemFlag)) {
                        specialOperation.add(techonlogyTypeMap.get(itemFlag));
                    }
                    //除膜
                    String attr1 = algorithmData.getAttr1OfProductMap().get(operation.getProductId());
                    if (StringUtils.isNotEmpty(attr1) && techonlogyTypeMap.containsKey(attr1)) {
                        specialOperation.add(techonlogyTypeMap.get(attr1));
                    }
                    //hud
                    String hud = algorithmData.getHudOfProductMap().get(operation.getProductId());
                    if (StringUtils.isNotEmpty(hud) && techonlogyTypeMap.containsKey(hud)) {
                        specialOperation.add(techonlogyTypeMap.get(hud));
                    }
                    Map<String, Pair<BigDecimal, BigDecimal>> productionLeadTimeMap = algorithmData.getProductionLeadTimeMap();
                    //当前工序有特殊工艺，遍历所有的提前期数据找到最大的
                    //与前工序最小时间间隔
                    BigDecimal beforeMinTimeConstraint = BigDecimal.ZERO;
                    //与后工序最小时间间隔
                    BigDecimal afterMinTimeConstraint = BigDecimal.ZERO;
                    for (String special : specialOperation) {
                        String productionLeadKey = String.join("-", stockCode, operation.getRoutingStepSequenceNo().toString(), standResourceCode, special);
                        String commonProductionLeadKey = String.join("-", stockCode, operation.getRoutingStepSequenceNo().toString(), "*", "*");

                        if (productionLeadTimeMap.containsKey(productionLeadKey)) {
                            Pair<BigDecimal, BigDecimal> pair = productionLeadTimeMap.get(productionLeadKey);
                            //与前工序的最小间隔
                            beforeMinTimeConstraint = beforeMinTimeConstraint.compareTo(pair.getLeft()) >= 0 ? beforeMinTimeConstraint : pair.getLeft();
                            afterMinTimeConstraint = afterMinTimeConstraint.compareTo(pair.getRight()) >= 0 ? afterMinTimeConstraint : pair.getRight();
                        }
                        if (productionLeadTimeMap.containsKey(commonProductionLeadKey)) {
                            Pair<BigDecimal, BigDecimal> pair = productionLeadTimeMap.get(commonProductionLeadKey);
                            //与前工序的最小间隔
                            beforeMinTimeConstraint = beforeMinTimeConstraint.compareTo(pair.getLeft()) >= 0 ? beforeMinTimeConstraint : pair.getLeft();
                            afterMinTimeConstraint = afterMinTimeConstraint.compareTo(pair.getRight()) >= 0 ? afterMinTimeConstraint : pair.getRight();
                        }
                    }
                    minTimeConstraintMap.put(operation.getId(), Pair.of(beforeMinTimeConstraint, afterMinTimeConstraint));
                }

                //连续炉开机规则
                List<String> intersection = operationResourceIds.stream().filter(algorithmData.getLxResourceIds()::contains).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(intersection)) {
                    operationData.getTags().add("连续炉");
                }

                //工序物料对用的膜系为单银/双银
                if (StringUtils.isNotEmpty(gridType) && (gridType.contains("单银") || gridType.contains("双银"))) {
                    //工序对应物品装车位置为天窗
                    if (StringUtils.isNotEmpty(stockAndLoadingPosition) && stockAndLoadingPosition.contains("天窗")) {
                        operationData.getTags().add("单_双银_天窗");
                    } else {
                        operationData.getTags().add("单_双银");
                    }
                }
                operationData.setFeedBackQuantity(feedbackQuantityMap.getOrDefault(operation.getId(), BigDecimal.ZERO));
                subOperationDatas.add(operationData);
            }
            //根据提前期设置每道工序与前工序的间隔时间
            if (PARENT.equals(operationType)) {
                for (RzzOperationData subOperationData : subOperationDatas) {
                    //前工序id
                    String prevOperationId = subOperationData.getPrevOperationId();
                    BigDecimal right = BigDecimal.ZERO;
                    if (prevOperationId != null) {
                        //前工序的生产后处理时间
                        right = minTimeConstraintMap.get(prevOperationId).getRight();
                    }
                    //本道工序的生产前处理时间
                    BigDecimal left = minTimeConstraintMap.get(subOperationData.getOperationId()).getLeft();
                    BigDecimal add = right.add(left);
                    if (subOperationData.getOperationId().equals(cxOperationId)) {
                        String maxTimeConstraint = add.compareTo(new BigDecimal(subOperationData.getMaxTimeConstraint())) > 0 ? String.valueOf(add.intValue()) : subOperationData.getMaxTimeConstraint();
                        subOperationData.setMaxTimeConstraint(maxTimeConstraint);
                    } else {
                        subOperationData.setMinTimeConstraint(String.valueOf(add.intValue()));
                    }
                }
            }

            operationDatas.addAll(subOperationDatas);
        }
        return operationDatas;
    }

    private void handleResourceNotFound(OperationVO operation, RzzOperationData operationData, RzzBaseAlgorithmData algorithmData) {
        List<OperationOnResource> operationOnResource = operationData.getOperationOnResource();
        if (CollectionUtils.isEmpty(operationOnResource)) {
            return;
        }
        String plannedResourceId = operation.getPlannedResourceId();
        boolean resource = operationOnResource.stream().anyMatch(p -> p.getResourceId().equals(plannedResourceId));
        if (resource) {
            return;
        }
        List<OperationOnResource> resourceList = operationOnResource.stream().filter(t -> ResourceCategoryEnum.MAIN.getCode().equals(t.getResourceType()))
                .sorted(Comparator.comparing(OperationOnResource::getPriority)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resourceList)) {
            log.error("未找到主资源信息, operationId: {}, plannedResourceId: {}", operation.getId(), plannedResourceId);
            resourceList = operationOnResource.stream().sorted(Comparator.comparing(OperationOnResource::getPriority))
                    .collect(Collectors.toList());
        }
        String currentPlannedResourceId = resourceList.get(0).getResourceId();
        String taskType = resourceList.get(0).getTaskType();
        operationData.setCurrentPlannedResourceId(currentPlannedResourceId);
        operationData.setCurrentPlannedResourceType(taskType);
    }

    private void addMembraneSystemSpec(List<Map<String, Spec>> specDataMapList, String gridType) {
        if (StringUtils.isNotEmpty(gridType)) {
            Spec membraneSystemSpec = new Spec();
            membraneSystemSpec.setKey(gridType);
            membraneSystemSpec.setName(gridType);
            membraneSystemSpec.setValue(gridType);
            Map<String, Spec> membraneSystemMap = new HashMap<>();
            membraneSystemMap.put("strSpec", membraneSystemSpec);
            //特殊产能工艺约束，有膜系的统一加一个规格
            Spec specialMembraneSystemSpec = new Spec();
            specialMembraneSystemSpec.setKey("special");
            specialMembraneSystemSpec.setName("special");
            specialMembraneSystemSpec.setValue("special");
            membraneSystemMap.put("special", specialMembraneSystemSpec);
            specDataMapList.add(membraneSystemMap);
        }
    }

    private void addClampSpec(List<Map<String, Spec>> specDataMapList, String clampType) {
        if (StringUtils.isNotEmpty(clampType)) {
            Spec membraneSystemSpec = new Spec();
            membraneSystemSpec.setKey(clampType);
            membraneSystemSpec.setName(clampType);
            membraneSystemSpec.setValue(clampType);
            Map<String, Spec> membraneSystemMap = new HashMap<>();
            membraneSystemMap.put("clampType", membraneSystemSpec);
            specDataMapList.add(membraneSystemMap);
        }
    }

    private List<InputMaterial> getInputMaterial(List<OperationInputVO> operationInputMaterials, Map<String, List<SpecBusinessVO>> specRoutingStepInputCollectOfInputId, Map<String, RoutingStepInputVO> routingStepInputItemMapOfJoinKey) {
        List<InputMaterial> inputMaterials = new ArrayList<>();
        for (OperationInputVO operationInputMaterial : operationInputMaterials) {
            InputMaterial inputMaterial = new InputMaterial();
            inputMaterial.setMainPart(operationInputMaterial.getMainMaterial());
            inputMaterial.setProductId(operationInputMaterial.getProductStockPointId());
            inputMaterial.setStockingPointId(operationInputMaterial.getStockPointCode());
            if (operationInputMaterial.getMinConnectionDuration() != null) {
                inputMaterial.setMinTimeConstraint(DateUtils.second2HourMinuteSecondStr(operationInputMaterial.getMinConnectionDuration()));
            }
            //获取工艺路径输入物品
            List<Map<String, Spec>> specDataMapList = new ArrayList<>();
            RoutingStepInputVO routingStepInputItem = routingStepInputItemMapOfJoinKey.get(String.join("&", operationInputMaterial.getRoutingStepId(), operationInputMaterial.getProductStockPointId()));
            if (null != routingStepInputItem) {
                //获取工艺路径输入物品规格
                List<SpecBusinessVO> specRoutingStepInputList = specRoutingStepInputCollectOfInputId.get(routingStepInputItem.getId());
                if (CollectionUtils.isNotEmpty(specRoutingStepInputList)) {
                    assembleSpecDataMapList(specRoutingStepInputList, SpecKeyEnum.ROUTING_STEP_INPUT.getCode(), specDataMapList);
                }
            }
            inputMaterial.setSpecs(specDataMapList);
            inputMaterials.add(inputMaterial);
        }
        return inputMaterials;
    }

    private void assembleSpecDataMapList(List<?> specDataList, String key, List<Map<String, Spec>> specDataMapList) {
        for (Object item : specDataList) {
            Map<String, Spec> map = new HashMap<>(3);
            Spec specData = new Spec();
            try {
                String specName = BeanUtils.getProperty(item, "specName");
                String numSpec = BeanUtils.getProperty(item, "numSpec");
                specData.setName(specName);
                specData.setKey(key);
                Gson gson = new Gson();
                if (StringUtils.equals(YesOrNoEnum.YES.getCode(), numSpec)) {
                    //数值
                    String numSpecValue = BeanUtils.getProperty(item, "numSpecValue");
                    specData.setValue(gson.fromJson(numSpecValue, JsonElement.class));
                    map.put("numSpec", specData);
                } else {
                    //非数值
                    String specValue = BeanUtils.getProperty(item, "specValue");
                    specData.setValue(gson.fromJson(specValue, JsonElement.class));
                    map.put("strSpec", specData);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            specDataMapList.add(map);
        }
    }

    private List<OutputMaterial> getOutputMaterial(List<OperationOutputVO> operationOutputMaterials, Map<String, List<SpecBusinessVO>> specRoutingStepOutputCollectOfOutputId, Map<String, RoutingStepOutputVO> routingStepOutputItemMapOfJoinKey, Map<String, List<SpecBusinessVO>> specProductMap) {
        List<OutputMaterial> outputMaterials = new ArrayList<>();
        for (OperationOutputVO operationOutputMaterial : operationOutputMaterials) {
            OutputMaterial outputMaterial = new OutputMaterial();
            outputMaterial.setMainProduct(operationOutputMaterial.getMainProduct());
            outputMaterial.setProductId(operationOutputMaterial.getProductStockPointId());
            outputMaterial.setStockingPointId(operationOutputMaterial.getStockPointCode());
            if (operationOutputMaterial.getMinConnectionDuration() != null) {
                outputMaterial.setMinTimeConstraint(DateUtils.second2HourMinuteSecondStr(operationOutputMaterial.getMinConnectionDuration()));
            }

            //获取工艺路径输入物品
            List<Map<String, Spec>> specDataMapList = new ArrayList<>();
            RoutingStepOutputVO routingStepOutputItem = routingStepOutputItemMapOfJoinKey.get(String.join("&", operationOutputMaterial.getRoutingStepId(), operationOutputMaterial.getProductStockPointId()));
            //BOM里没有规格则匹配库存点物品规格
            if (null != routingStepOutputItem) {
                //获取工艺路径输入物品规格
                List<SpecBusinessVO> specRoutingStepOutputList = specRoutingStepOutputCollectOfOutputId.get(routingStepOutputItem.getId());
                if (CollectionUtils.isNotEmpty(specRoutingStepOutputList)) {
                    assembleSpecDataMapList(specRoutingStepOutputList, SpecKeyEnum.ROUTING_STEP_OUTPUT.getCode(), specDataMapList);
                }
            }
            if (CollectionUtils.isNotEmpty(specDataMapList)) {
                List<SpecBusinessVO> specProductList = specProductMap.get(operationOutputMaterial.getProductStockPointId());
                if (CollectionUtils.isNotEmpty(specProductList)) {
                    assembleSpecDataMapList(specProductList, SpecKeyEnum.ROUTING_STEP_OUTPUT.getCode(), specDataMapList);
                }
            }
            outputMaterial.setSpecs(specDataMapList);
            outputMaterials.add(outputMaterial);
        }
        return outputMaterials;
    }

    private List<OperationOnResource> getOperationResources(OperationVO operation, List<ProductFixtureRelationPO> productFixtureRelation, Map<String, PhysicalResourceVO> physicalResourceVOMap, Set<String> draPhysicalResource, RzzBaseAlgorithmData baseAlgorithmData, List<PhysicalResourceVO> physicalResourceVOS, List<String> yztResourceList, AtomicBoolean containYzt, AtomicBoolean containContinuous, List<RoutingStepResourceVO> operationStepResourcesForThisOperation, List<String> errorMessage, Map<String, NewProductTrialSubmissionDetailVO> detailVOMap, List<String> szWorkOrderIds, Map<String, String> workOrderParentIdMap, Map<String, String> operationOnResourceIdMap, String scheduleType) {
        if (CollectionUtils.isEmpty(operationStepResourcesForThisOperation)) {
//            errorMessage.add("工序" + operation.getId() + "产品:" + operation.getProductCode() + "候选资源不存在，或因为当前工序排产资源已不在最新候选资源中而被过滤");
            return new ArrayList<>();
        }
        List<OperationOnResource> operationOnResources = new ArrayList<>();
        for (RoutingStepResourceVO operationResource : operationStepResourcesForThisOperation) {
            String physicalResourceId = operationResource.getPhysicalResourceId();
            PhysicalResourceVO physicalResourceVO = physicalResourceVOMap.get(physicalResourceId);
            if (Objects.isNull(physicalResourceVO)) {
                log.error("工序物理资源为空，physicalResourceId:{}", physicalResourceId);
                continue;
            }
            String hw = "HW";
            String physicalResourceCode = physicalResourceVO.getPhysicalResourceCode();
            if (physicalResourceCode.contains(hw)) {
                // conf文件记录烘弯物理资源id
                draPhysicalResource.add(physicalResourceId);
                containContinuous.set(false);
            }
            OperationOnResource operationOnResource = new OperationOnResource();
            operationOnResource.setResourceCode(physicalResourceVO.getPhysicalResourceCode());
            operationOnResource.setTaskType(physicalResourceVO.getSubtaskType());
            // Z-物理资源id
            operationOnResource.setResourceId(operationResource.getPhysicalResourceId());
            // Z-候选资源id
            operationOnResource.setOperationOnResourceId(operationResource.getId());
            // Z-设置时间
            if (ObjectUtil.isNotNull(operationResource.getSetupDuration())) {
                TaskDuration taskDuration = new TaskDuration();
                taskDuration.setFixedDurSeconds(Integer.parseInt(operationResource.getSetupDuration()));
                operationOnResource.setSetupDuration(taskDuration);
            }
            // Z-制造时间
            if (ObjectUtil.isNotNull(operationResource.getFixedWorkHours()) && operationResource.getFixedWorkHours() > 0) {
                // 固定工时(三选一)
                TaskDuration taskDuration = new TaskDuration();
                taskDuration.setFixedDurSeconds(operationResource.getFixedWorkHours());
                operationOnResource.setProcessDuration(taskDuration);
            } else if (ObjectUtil.isNotNull(operationResource.getUnitsPerHour()) && operationResource.getUnitsPerHour().compareTo(BigDecimal.ZERO) > 0) {
                // 每小时产量(三选一)
                TaskDuration taskDuration = new TaskDuration();
                TaskDurationEffective effective = new TaskDurationEffective();
                effective.setQuantity(operationResource.getUnitsPerHour());
                effective.setDur(3600);
                taskDuration.setTaskDurationEffective(effective);
                operationOnResource.setProcessDuration(taskDuration);
            } else if (ObjectUtil.isNotNull(operationResource.getUnitProductionTime()) && operationResource.getUnitProductionTime().compareTo(BigDecimal.ZERO) > 0) {
                // 每小时产量(三选一)
                TaskDuration taskDuration = new TaskDuration();
                TaskDurationEffective effective = new TaskDurationEffective();
                effective.setQuantity(BigDecimal.valueOf(1));
                effective.setDur(operationResource.getUnitProductionTime().intValue());
                taskDuration.setTaskDurationEffective(effective);
                operationOnResource.setProcessDuration(taskDuration);
            }

            if (szWorkOrderIds != null && szWorkOrderIds.contains(operation.getOrderId())) {
                NewProductTrialSubmissionDetailVO detailVO = detailVOMap.get(operation.getOrderId());
                if (detailVO == null) {
                    String parentOrderId = workOrderParentIdMap.get(operation.getOrderId());
                    if (StringUtils.isNotEmpty(parentOrderId)) {
                        detailVO = detailVOMap.get(parentOrderId);
                    }
                }
                if (detailVO != null) {
                    // 属于新品试制的工序， 需要取固定工时
                    setProcessDuration(operationOnResource, operation, detailVO);
                }
            }

            // Z-清理时间
            if (ObjectUtil.isNotNull(operationResource.getCleanupDuration())) {
                TaskDuration taskDuration = new TaskDuration();
                taskDuration.setFixedDurSeconds(Integer.parseInt(operationResource.getCleanupDuration()));
                operationOnResource.setCleanupDuration(taskDuration);
            }
            // Z-配套使用号
            if (StringUtils.isNotBlank(operationResource.getMatchCode())) {
                operationOnResource.setMatchCode(operationResource.getMatchCode());
            }
            // Z-替代工器具组号
            operationOnResource.setAltToolCode(operationResource.getAltToolCode());
            // Z-优先级
            operationOnResource.setPriority(operationResource.getPriority());
            // Z-最大制造批量
            if (operationResource.getMaxLotSize() != null) {
                operationOnResource.setMaxMadeBatch(operationResource.getMaxLotSize().longValue());
            }
            // Z-资源类型
            operationOnResource.setResourceType(physicalResourceVO.getResourceCategory());
            // Z-生产线
            operationOnResource.setLine(operationResource.getProductionLine());
            //制造必要资源量
            operationOnResource.setProductionUnitBatchSize(operationResource.getProductionUnitBatchSize() == null ? BigDecimal.ZERO : operationResource.getProductionUnitBatchSize());
            //清洗必要资源量
            operationOnResource.setCleanupUnitBatchSize(operationResource.getCleanupUnitBatchSize() == null ? BigDecimal.ZERO : operationResource.getCleanupUnitBatchSize());
            //设置必要资源量
            operationOnResource.setSetupUnitBatchSize(operationResource.getSetupUnitBatchSize() == null ? BigDecimal.ZERO : operationResource.getSetupUnitBatchSize());
            operationOnResources.add(operationOnResource);
        }
        if (CollectionUtils.isEmpty(operationOnResources)) {
            log.warn("工序：{}，没有可用候选资源", operation.getId());
            return new ArrayList<>();
        }
        //如果是关键工序且不是手工调整
        if (!ScheduleTypeEnum.HANDWORK.getCode().equals(scheduleType) && StandardStepEnum.FORMING_PROCESS.getCode().equals(operation.getStandardStepType())) {
            WorkOrderVO workOrderVO = baseAlgorithmData.getWorkOrderVOMap().get(operation.getOrderId());
            if (StringUtils.isNotEmpty(workOrderVO.getCountingUnitId()) && workOrderVO.getCountingUnitId().contains("&")) {
                String[] split = workOrderVO.getCountingUnitId().split("&");
                // 拆单指定的候选资源
                String limitResourceIds = split[1];
                int size = operationOnResources.size();
                List<OperationOnResource> emptyResource = new ArrayList<>(operationOnResources);
                // 根据拆批插单指定资源筛选候选资源
                List<OperationOnResource> useOperation = operationOnResources.stream().filter(t -> limitResourceIds.equals(t.getResourceId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(useOperation)) {
                    // 根据拆批插单指定资源筛选候选资源未找到指定资源，存在换产线情况，用更换完产线资源id继续查找
                    String plannedResourceId = operation.getPlannedResourceId();
                    if (StrUtil.isNotBlank(plannedResourceId)) {
                        log.info("工序：{}，存在更换产线，重新查找候选资源", operation.getId());
                        useOperation = operationOnResources.stream().filter(t -> plannedResourceId.equals(t.getResourceId())).collect(Collectors.toList());
                    }
                }
                if (CollectionUtils.isEmpty(useOperation)) {
                    operationOnResources.sort(Comparator.comparing(OperationOnResource::getPriority));
                    useOperation.add(operationOnResources.get(0));
                    log.warn("工序:{},筛选后没有可用资源,使用优先级最高资源", operation.getId());
                }
                if (CollectionUtils.isEmpty(useOperation)) {
                    log.warn("工序：{}，没有可用候选资源，limitResourceIds：{}，原候选资源行数：{}，候选资源数据：{}", JSON.toJSONString(operation), JSON.toJSONString(limitResourceIds), size, JSON.toJSONString(emptyResource));
                } else {
                    operationOnResources = useOperation;
                    operationOnResourceIdMap.put(operation.getId(), useOperation.get(0).getResourceId());
                }
            } else {
                operationOnResources.sort(Comparator.comparing(OperationOnResource::getPriority).thenComparing(OperationOnResource::getResourceId));
                operationOnResourceIdMap.put(operation.getId(), operationOnResources.get(0).getResourceId());
            }
        }
        // fixme 压制台搭配主资源出现
        List<OperationOnResource> resourceYzt = operationOnResources.stream().filter(t -> yztResourceList.contains(t.getResourceCode().toUpperCase())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(resourceYzt)) {
            String matchCode = "YZT_MATCH_CODE";
            for (OperationOnResource operationOnResource : resourceYzt) {
                operationOnResource.setMatchCode(matchCode);
            }
            String altToolCode = "YZT";
            for (PhysicalResourceVO physicalResourceVO : physicalResourceVOS) {
                OperationOnResource operationOnResource = getOperationOnResource(physicalResourceVO, null);
                operationOnResource.setCleanupUnitBatchSize(BigDecimal.ONE);
                operationOnResource.setAltToolCode(altToolCode);
                operationOnResource.setMatchCode(matchCode);
                operationOnResources.add(operationOnResource);
            }
            containYzt.set(true);
        }

        // 设置压制台资源
        if (CollectionUtils.isNotEmpty(productFixtureRelation)) {
            for (ProductFixtureRelationPO productFixtureRelationPO : productFixtureRelation) {
                String physicalResourceId = productFixtureRelationPO.getPhysicalResourceId();
                PhysicalResourceVO physicalResourceVO = physicalResourceVOMap.get(physicalResourceId);
                if (Objects.nonNull(physicalResourceVO)) {
                    OperationOnResource toolResource = getOperationOnResource(physicalResourceVO, null);
                    toolResource.setAltToolCode(productFixtureRelationPO.getAltToolCode());
                    operationOnResources.add(toolResource);
                }
            }
            log.info("制造订单id：{}，工序：{}，设置压制台资源构建工具资源数量：{}", operation.getId(), operation.getOrderId(), productFixtureRelation.size());
        }
        return operationOnResources;
    }

    private void setProcessDuration(OperationOnResource operationOnResource, OperationVO operationVO, NewProductTrialSubmissionDetailVO detailVO) {
        if (detailVO.getAssembly() != null && detailVO.getAssembly() != 0 && "包装".equals(operationVO.getRoutingStepName())) {
            TaskDuration taskDuration = new TaskDuration();
            // 这里单位是分钟
            taskDuration.setFixedDurSeconds(detailVO.getAssembly() * 60);
            operationOnResource.setProcessDuration(taskDuration);
        }
        if (detailVO.getLamination() != null && detailVO.getLamination() != 0 && "合片".equals(operationVO.getRoutingStepName())) {
            TaskDuration taskDuration = new TaskDuration();
            // 这里单位是分钟
            taskDuration.setFixedDurSeconds(detailVO.getLamination() * 60);
            operationOnResource.setProcessDuration(taskDuration);
        }
        if (detailVO.getShaping() != null && detailVO.getShaping() != 0 && Arrays.asList("成型", "钢化").contains(operationVO.getRoutingStepName())) {
            TaskDuration taskDuration = new TaskDuration();
            // 这里单位是分钟
            taskDuration.setFixedDurSeconds(detailVO.getShaping() * 60);
            operationOnResource.setProcessDuration(taskDuration);
        }
        if (detailVO.getPretreatment() != null && detailVO.getPretreatment() != 0 && "预处理".equals(operationVO.getRoutingStepName())) {
            TaskDuration taskDuration = new TaskDuration();
            // 这里单位是分钟
            taskDuration.setFixedDurSeconds(detailVO.getPretreatment() * 60);
            operationOnResource.setProcessDuration(taskDuration);
        }
    }

    private OperationOnResource getOperationOnResource(PhysicalResourceVO physicalResourceVO, String matchCode) {
        OperationOnResource tool = new OperationOnResource();
        tool.setResourceId(physicalResourceVO.getId());
        tool.setResourceType(physicalResourceVO.getResourceType());
        tool.setProductionUnitBatchSize(BigDecimal.ONE);
        tool.setCleanupUnitBatchSize(BigDecimal.ZERO);
        tool.setSetupUnitBatchSize(BigDecimal.ONE);
        tool.setPriority(1);
        tool.setMatchCode(matchCode);
        // 每小时产量
        TaskDuration taskDuration = new TaskDuration();
        TaskDurationEffective effective = new TaskDurationEffective();
        effective.setQuantity(BigDecimal.valueOf(1));
        effective.setDur(1);
        taskDuration.setTaskDurationEffective(effective);
        tool.setProcessDuration(taskDuration);
        return tool;
    }

    /**
     * 构建[工序] - [上次排程信息(lastScheduled)]
     *
     * @param operationExtendVO 上次排程信息VO
     * @return 上次排程信息
     */
    private LastScheduled getLastScheduled(OperationExtendVO operationExtendVO) {
        if (ObjectUtil.isEmpty(operationExtendVO) || operationExtendVO.getLastProductionStartTime() == null) {
            return null;
        }
        LastScheduled lastScheduled = new LastScheduled();
        // Z-上次排程开始时间
        if (operationExtendVO.getLastProductionStartTime() != null) {
            lastScheduled.setProcessStart(operationExtendVO.getLastProductionStartTime());
        }
        // Z-上次排程制造结束时间
        if (operationExtendVO.getLastProductionEndTime() != null) {
            lastScheduled.setProcessEnd(operationExtendVO.getLastProductionEndTime());
        }
        // Z-资源Id
        List<String> resourceIds = new ArrayList<>();
        if (StringUtils.isNotBlank(operationExtendVO.getLastMainResourceId())) {
            resourceIds.addAll(Arrays.asList(operationExtendVO.getLastMainResourceId().split(",")));
        }
        if (StringUtils.isNotBlank(operationExtendVO.getLastToolResourceId())) {
            resourceIds.addAll(Arrays.asList(operationExtendVO.getLastToolResourceId().split(",")));
        }
        if (StringUtils.isNotBlank(operationExtendVO.getLastSkillId())) {
            resourceIds.addAll(Arrays.asList(operationExtendVO.getLastSkillId().split(",")));
        }
        lastScheduled.setResourceIds(resourceIds);

        return lastScheduled;
    }

    private List<RzzProductInSockingPointData> getProductInSockingPoints(List<NewProductStockPointVO> productStockPoints, List<NewStockPointVO> stockPointVOS, Map<String, List<SpecBusinessVO>> productSpecMap, Map<String, String> toolingMoldMap, Map<String, String> productBaseMap) {
        List<RzzProductInSockingPointData> productInSockingPoints = new ArrayList<>();
        Map<String, NewStockPointVO> stockPointVOMap = stockPointVOS.stream().collect(Collectors.toMap(NewStockPointVO::getStockPointCode, v -> v, (v1, v2) -> v1));
        for (NewProductStockPointVO productStockPoint : productStockPoints) {
            String productCode = productStockPoint.getProductCode();
            RzzProductInSockingPointData productInSockingPoint = new RzzProductInSockingPointData();
            productInSockingPoint.setProductId(productStockPoint.getId());
            productInSockingPoint.setProductCode(productCode);
            String series = productBaseMap.getOrDefault(productCode, productStockPoint.getVehicleModelCode());
            if (StringUtils.isBlank(series)) {
                series = productCode;
            }
            //物品规格
            if (productSpecMap.get(productStockPoint.getId()) != null) {
                productInSockingPoint.setSpec(getProductSpec(productSpecMap.get(productStockPoint.getId())));
            }
            NewStockPointVO newStockPointVO = stockPointVOMap.get(productStockPoint.getStockPointCode());
            productInSockingPoint.setStockingPointId(newStockPointVO.getId());
            productInSockingPoint.setType(RzzProductInSockingPointData.PRODUCT_TYPE_TRANS_MAP.get(productStockPoint.getProductType()));
            productInSockingPoint.setStockPointCode(productStockPoint.getStockPointCode());
            productInSockingPoint.setMoldQuantityLimit(productStockPoint.getMoldQuantityLimit());
            productInSockingPoint.setProductStockPointVO(productStockPoint);
            productInSockingPoint.setProductSeries(series);
            productInSockingPoint.setToolModel(toolingMoldMap.get(productStockPoint.getProductCode()));
            productInSockingPoints.add(productInSockingPoint);
        }
        // 同物品系列取最小的模具数量
        if (CollectionUtils.isNotEmpty(productInSockingPoints)) {
            Map<String, Integer> minMoldQuantityLimitMap = productInSockingPoints.stream().collect(Collectors.groupingBy(RzzProductInSockingPointData::getProductSeries, Collectors.collectingAndThen(Collectors.minBy(Comparator.comparingInt(t -> t.getMoldQuantityLimit() == null ? 1 : t.getMoldQuantityLimit())), opt -> opt.map(RzzProductInSockingPointData::getMoldQuantityLimit).orElse(1))));
            productInSockingPoints.forEach(productInSockingPoint -> {
                productInSockingPoint.setMoldQuantityLimit(minMoldQuantityLimitMap.getOrDefault(productInSockingPoint.getProductSeries(), 1));
            });
        }
        return productInSockingPoints;
    }

    private List<Map<String, Map<String, Object>>> getProductSpec(List<SpecBusinessVO> specProducts) {
        List<Map<String, Map<String, Object>>> specs = new ArrayList<>();
        for (SpecBusinessVO specProduct : specProducts) {
            Map<String, Map<String, Object>> specMap = new HashedMap<>();
            if (YesOrNoEnum.YES.getCode().equals(specProduct.getWhetherNumeric())) {
                if (specProduct.getNumericSpecValue() != null) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("key", SpecKeyEnum.PRODUCT_STOCK_POINT.getCode());
                    map.put("name", specProduct.getSpecName());
                    map.put("value", specProduct.getNumericSpecValue());
                    specMap.put("numSpec", map);
                    specs.add(specMap);
                }

            } else {
                if (StringUtils.isNotBlank(specProduct.getSpecValue())) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("key", SpecKeyEnum.PRODUCT_STOCK_POINT.getCode());
                    map.put("name", specProduct.getSpecName());
                    map.put("value", specProduct.getSpecValue());
                    specMap.put("strSpec", map);
                    specs.add(specMap);
                }

            }
        }
        return specs;
    }

    private List<RzzFulfillment> getFulfillments(List<FulfillmentVO> fulfillmentVOList, List<RzzWorkOrderData> workOrders, List<RzzOperationData> operationDataList) {
        List<RzzFulfillment> fulfillmentList = new ArrayList<>();
        List<String> fulfillmentIdList = new ArrayList<>();
        for (FulfillmentVO fulfillmentVO : fulfillmentVOList) {
            if (fulfillmentIdList.contains(fulfillmentVO.getId())) {
                continue;
            }
            fulfillmentIdList.add(fulfillmentVO.getId());
            //1、demandType不为WORK_ORDER_DEMAND，且supplyType不为WORK_ORDER_SUPPLY、PURCHASE_ORDER_SUPPLY、TRANSPORTATION_ORDER_SUPPLY
            if (StringUtils.equals(fulfillmentVO.getDemandType(), DemandTypeEnum.WORK_ORDER_DEMAND.getCode()) && !StringUtils.equals(fulfillmentVO.getSupplyType(), SupplyTypeEnum.WORK_ORDER_SUPPLY.getCode()) && !StringUtils.equals(fulfillmentVO.getSupplyType(), SupplyTypeEnum.PURCHASE_ORDER_SUPPLY.getCode()) && !StringUtils.equals(fulfillmentVO.getSupplyType(), SupplyTypeEnum.TRANSPORTATION_ORDER_SUPPLY.getCode())) {
                FulfillmentDemand demand = new FulfillmentDemand();
                demand.setType(FulfillmentDemandTypeEnum.OPERATION_DEMAND.getCode());
                demand.setId(fulfillmentVO.getDemandVO().getOperationId());

                FulfillmentSupply supply = new FulfillmentSupply();
                supply.setType(fulfillmentVO.getSupplyType());
                supply.setId(fulfillmentVO.getSupplyOrderId());
                createFulfillment(demand, supply, fulfillmentList, null);
            }

            //跨制造订单
            if (StringUtils.equals(fulfillmentVO.getDemandType(), DemandTypeEnum.CUSTOMER_ORDER_DEMAND.getCode()) && StringUtils.equals(fulfillmentVO.getSupplyType(), SupplyTypeEnum.WORK_ORDER_SUPPLY.getCode())) {
                FulfillmentDemand demand = new FulfillmentDemand();
                demand.setType(FulfillmentDemandTypeEnum.OPERATION_DEMAND.getCode());
                demand.setId(fulfillmentVO.getDemandVO().getOperationId());

                FulfillmentSupply supply = new FulfillmentSupply();
                supply.setType(FulfillmentDemandTypeEnum.OPERATION_SUPPLY.getCode());
                supply.setId(fulfillmentVO.getSupplyVO().getOperationId());
                createFulfillment(demand, supply, fulfillmentList, null);
            }
        }

        //同一个制造订单
        Map<String, List<RzzOperationData>> operationsMapOfWorkOrderId = operationDataList.stream().collect(Collectors.groupingBy(RzzOperationData::getWorkOrderId));
        for (RzzWorkOrderData workOrder : workOrders) {
            List<RzzOperationData> operationData = operationsMapOfWorkOrderId.get(workOrder.getId());
            if (CollectionUtils.isEmpty(operationData)) {
                continue;
            }
            //过滤出父工序,并按照顺序号排序
            List<RzzOperationData> sortOperationData = operationData.stream().filter(item -> StringUtils.isBlank(item.getParentId())).sorted(Comparator.comparing(RzzOperationData::getRoutingStepSequenceNo)).collect(Collectors.toList());
            for (int i = 0; i < sortOperationData.size(); i++) {
                RzzOperationData currentOperationData = sortOperationData.get(i);
                if (i > 0) {
                    RzzOperationData preOperationData = sortOperationData.get(i - 1);
                    FulfillmentDemand demand = new FulfillmentDemand();
                    demand.setId(currentOperationData.getOperationId());
                    demand.setType(FulfillmentDemandTypeEnum.OPERATION_DEMAND.getCode());

                    FulfillmentSupply supply = new FulfillmentSupply();
                    supply.setType(FulfillmentDemandTypeEnum.OPERATION_SUPPLY.getCode());
                    supply.setId(preOperationData.getOperationId());
                    createFulfillment(demand, supply, fulfillmentList, preOperationData.getOperationId());
                }
            }
        }
        List<RzzFulfillment> customerOrderFulfillment = fulfillmentList.stream().filter(p -> StrUtil.isEmpty(p.getOperationId())).collect(Collectors.toList());
        log.info("fulfillment输入数据组装成功:{}", JSONObject.toJSON(customerOrderFulfillment));
        return fulfillmentList;
    }

    private void createFulfillment(FulfillmentDemand demand, FulfillmentSupply supply, List<RzzFulfillment> fulfillmentList, String operationId) {
        RzzFulfillment fulfillment = new RzzFulfillment();
        fulfillment.setDemand(demand);
        fulfillment.setSupply(supply);
        fulfillment.setOperationId(operationId);
        fulfillmentList.add(fulfillment);
    }

    public APSInput test() {
        RzzBaseAlgorithmData algorithmData = getAlgorithmData(null, null);
        //todo 算法文件生成路径
        String filePath = "D:\\test";

        APSInput apsInput = getApsInput(algorithmData, filePath);
        writeConf(apsInput, algorithmData);
        return apsInput;
    }


    private List<RzzMoldChangeTimeData> getRzzMoldChangeTimeData(List<OperationVO> allOperations, List<PhysicalResourceVO> resources, List<MoldChangeTimeVO> moldChangeTimeVOS, List<NewProductStockPointVO> newProductStockPointVOS) {
        List<RzzMoldChangeTimeData> result = new ArrayList<>();
        moldChangeTimeVOS = moldChangeTimeVOS.stream().filter(t -> StringUtils.isNotEmpty(t.getResourceCode())).collect(Collectors.toList());
        Map<String, String> resourceCodeOfIdMap = resources.stream().collect(Collectors.toMap(PhysicalResourceBasicVO::getPhysicalResourceCode, BaseVO::getId, (v1, v2) -> v1));
        Map<String, String> newProductStockPointMap = newProductStockPointVOS.stream().collect(Collectors.toMap(t -> String.join("#", t.getStockPointCode(), t.getProductCode()), NewProductStockPointVO::getId, (v1, v2) -> v1));
        for (MoldChangeTimeVO moldChangeTimeVO : moldChangeTimeVOS) {
            String key = String.join("#", moldChangeTimeVO.getStockPointCode(), moldChangeTimeVO.getProductCode());
            if (newProductStockPointMap.containsKey(key) && resourceCodeOfIdMap.containsKey(moldChangeTimeVO.getResourceCode())) {
                String productId = newProductStockPointMap.get(key);
                String operationCode = moldChangeTimeVO.getOperationCode();
                String resourceId = resourceCodeOfIdMap.get(moldChangeTimeVO.getResourceCode());
                List<OperationVO> operationVOS = allOperations.stream().filter(t -> productId.equals(t.getProductId()) && operationCode.equals(t.getRoutingStepCode())).collect(Collectors.toList());
                for (OperationVO operationVO : operationVOS) {
                    RzzMoldChangeTimeData timeData = new RzzMoldChangeTimeData();
                    timeData.setOperationId(operationVO.getId());
                    timeData.setResourceId(resourceId);
                    timeData.setOutChangeOverTime(DateUtils.minute2hourMinuteSecondStr(Math.toIntExact(moldChangeTimeVO.getOutsideDieChangeTime())));
                    result.add(timeData);
                }
            }
        }
        return result;
    }


    private void getYztResource(RzzBaseAlgorithmData baseAlgorithmData, Map<String, List<PhysicalResourceVO>> standardPhysicalResourceMap) {
        Set<String> yztResourceIds = new HashSet<>();
        List<CollectionValueVO> yztTechnology = ipsFeign.getByCollectionCode("YZT");
        List<CollectionValueVO> yztResource = ipsFeign.getByCollectionCode("YZT_RESOURCE");
        // 压制台主资源代码
        List<String> yztResourceList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(yztResource)) {
            yztResourceList.addAll(yztResource.stream().map(CollectionValueVO::getCollectionValue).collect(Collectors.toList()));
        }
        // 压制台工具资源
        List<PhysicalResourceVO> physicalResourceVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(yztTechnology)) {
            String collectionValue = yztTechnology.get(0).getCollectionValue();
            List<StandardResourceVO> standardResourceVOList = newMdsFeign.selectStandardResourceVOSByParams(null, ImmutableMap.of("standardResourceCode", collectionValue));
            if (CollectionUtils.isNotEmpty(standardResourceVOList)) {
                String id = standardResourceVOList.get(0).getId();
                physicalResourceVOS.addAll(standardPhysicalResourceMap.getOrDefault(id, Lists.newArrayList()));
                for (PhysicalResourceVO physicalResourceVO : physicalResourceVOS) {
                    yztResourceIds.add(physicalResourceVO.getId());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(yztResourceList)) {
            List<PhysicalResourceVO> physicalResources = mdsFeign.getPhysicalResourcesByParams(ImmutableMap.of("physicalResourceCodes", yztResourceList));
            if (CollectionUtils.isNotEmpty(physicalResources)) {
                baseAlgorithmData.setYztMainResourceIds(
                        physicalResources.stream().map(PhysicalResourceVO::getId).distinct().collect(Collectors.toList()));
            }
        }
        baseAlgorithmData.setYztResourceIds(yztResourceIds);
        baseAlgorithmData.setYztResourceCodes(yztResourceList);
        baseAlgorithmData.setYztResourceIdsList(yztResourceList);
        baseAlgorithmData.setYztPhysicalResourceVOS(physicalResourceVOS);
    }

    public void writeConf(APSInput apsInput, RzzBaseAlgorithmData baseAlgorithmData) {
        JSONObject conf = getConf();
        conf.put("runTimeParameter", new JSONObject());
        JSONArray modules = conf.getJSONArray("modules");

        ScheduleParamDetail scheduleParamDetail = baseAlgorithmData.getScheduleParamDetail();
        JSONObject confLoaderObject = getConfLoader(baseAlgorithmData, scheduleParamDetail.getBeginTime(), scheduleParamDetail.getEndTime());
        JSONObject confLoader = confLoaderObject.getJSONObject("confLoader");

        //排程方向
        confLoader.put("scheduleDirection", scheduleParamDetail.getScheduleDirection());
        //资源选择策略
        confLoader.put("resourceSelectionStrategy", scheduleParamDetail.getResourceSelectionStrategy());
        // 资源排序逻辑
        confLoader.put("operationAssignStrategy", scheduleParamDetail.getOperationAssignStrategy());
        // 工序排序规则
        confLoader.put("operatorSortStrategy", scheduleParamDetail.getOperatorSortStrategy());
        confLoader.put("noSwitchIfSameProduct", scheduleParamDetail.getNoSwitchIfSameProduct());
        confLoader.put("inWorkOrder", scheduleParamDetail.isInWorkOrder());

        modules.add(confLoaderObject);
        modules.add(getRuleLoader());
        if (null != baseAlgorithmData.getWhetherAdjust() && baseAlgorithmData.getWhetherAdjust()) {
            modules.add(getPreOperationReverse(baseAlgorithmData));
            log.info("计划调整配置构建完成");
        }
        if (baseAlgorithmData.getDirection()) {
            // 前工序倒排
            modules.add(getPreOperationReverse(baseAlgorithmData));
            log.info("前工序倒排配置构建完成");
        } else {
            // 正排
            //插单模式下并且是插入中间，则sovel4Rule改成insertOrderModule
            if (scheduleParamDetail.getWhetherInsertOrderMiddleModule()) {
                modules.add(getInsertOrderModule());
            } else {
                // 生产排程排序
                // 2025-05-08注释，暂用默认排序规则，制造订单优先级排序
                // modules.add(getRzzSortOperationModule(baseAlgorithmData));
                //生产排程特有的配置
                modules.add(getSolver4Rule(baseAlgorithmData));
            }
            if (CollectionUtils.isNotEmpty(scheduleParamDetail.getInvertResourceSelectionRule())) {
                modules.add(getAutoAdjustThatOperationsSatisfyDeliveryByLatestEndConf(scheduleParamDetail.getInvertResourceSelectionRule(), scheduleParamDetail.getCurrentResourceInverted()));
            }

            if (!baseAlgorithmData.getWhetherAdjust()) {
                //大小片连续生产
                modules.add(getContinuousProductionModule());
                // 延期提前模块
                modules.add(getPostponeInAdvanceModule());
            }

            //拆批插单
            // 2025-05-08注释，拆批插单逻辑调整至后端解析
            //if (baseAlgorithmData.ruleEnable(AlgorithmConstraintRuleEnum.COMMON_RULE_10.getCode())) {
            //    modules.add(getSplitInsertOrderModule(baseAlgorithmData));
            //}
        }
        modules.add(getResultDumper());

        conf.put("runTimeParameter", getRunTimeParameter(baseAlgorithmData));
        Gson gson = new Gson();
        JsonElement config2 = gson.toJsonTree(conf);
        apsInput.setConfig(config2);
    }

    private JSONObject getPreOperationReverse(RzzBaseAlgorithmData baseAlgorithmData) {
        JSONObject jsonObject = new JSONObject();
        JSONArray operationTags = new JSONArray();

        ScheduleParamDetail scheduleParamDetail = baseAlgorithmData.getScheduleParamDetail();
        jsonObject.put("beginTime", DateUtils.dateToString(scheduleParamDetail.getBeginTime(), DateUtils.COMMON_DATE_STR1));
        jsonObject.put("endTime", DateUtils.dateToString(scheduleParamDetail.getEndTime(), DateUtils.COMMON_DATE_STR1));

//        operationTags.add("coating");
        operationTags.add("forming");
        jsonObject.put("reverseTags", operationTags);

        JSONObject preOperationReverseModule = new JSONObject();
        preOperationReverseModule.put("preOperationReverseModule", jsonObject);
        return preOperationReverseModule;
    }

    private JSONObject getRunTimeParameter(RzzBaseAlgorithmData baseAlgorithmData) {
        JSONObject jsonObject = new JSONObject();
        //待排工序
        jsonObject.put("operationsNeedSchedule", baseAlgorithmData.getNeedScheduleOperationIds());
        //供需关系
        jsonObject.put("fulfillment", baseAlgorithmData.getFulfillmentList());
        jsonObject.put("capacity", getCapacityList(baseAlgorithmData));
        //镀膜工序规则 : 排产达到镀膜保养量后进入镀膜维保
        if (baseAlgorithmData.ruleEnable(AlgorithmConstraintRuleEnum.COMMON_RULE_1.getCode())) {
            jsonObject.put("resourceMaintenance", getResourceMaintenanceList(baseAlgorithmData));
        }

        if (baseAlgorithmData.ruleEnable(AlgorithmConstraintRuleEnum.COMMON_RULE_8.getCode())) {
            //镀膜切换时间-产品切换时间表
            jsonObject.put("switchDurForProduct", getSwitchDurForProductList(baseAlgorithmData));
            //镀膜切换时间-规格切换时间表
            jsonObject.put("switchDurForSpec", getSwitchDurForSpecList(baseAlgorithmData));
            //工具资源切换时间-清洗时间换型
//            jsonObject.put("switchDurForTool", getSwitchDurForToolList(baseAlgorithmData));
        }

        return jsonObject;
    }

    private void getAlgorithmConstraintRules(RzzBaseAlgorithmData baseAlgorithmData) {
        Map<String, Pair<String, String>> algorithmConstraintRuleMap = algorithmConstraintRuleService.getAlgorithmConstraintRuleMap();
        baseAlgorithmData.setRuleParams(algorithmConstraintRuleMap);
    }

}
