package com.yhl.scp.mps.plan.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.BigDecimalUtils;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.biz.common.util.AsyncTaskUtils;
import com.yhl.scp.biz.common.util.PageUtils;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.curingTime.service.MdsCuringTimeService;
import com.yhl.scp.mds.curingTime.service.MdsFinishedProductDeliveryService;
import com.yhl.scp.mds.curingTime.vo.MdsCuringTimeVO;
import com.yhl.scp.mds.curingTime.vo.MdsFinishedProductDeliveryVO;
import com.yhl.scp.mds.enums.StandardStepEnum;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.cache.service.CacheGetService;
import com.yhl.scp.mps.cache.service.CacheSetService;
import com.yhl.scp.mps.domain.dispatch.process.AbstractMpsResult;
import com.yhl.scp.mps.domain.dispatch.support.BaseScheduleSupport;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import com.yhl.scp.mps.plan.req.PlanNodeTrackingReq;
import com.yhl.scp.mps.plan.res.PlanNodeTrackingRes;
import com.yhl.scp.mps.plan.service.MasterPlanService;
import com.yhl.scp.mps.plan.service.OperationPackingReportService;
import com.yhl.scp.mps.plan.service.PlanNodeTrackingService;
import com.yhl.scp.mps.plan.vo.FulfillDetailVO;
import com.yhl.scp.mps.plan.vo.MasterPlanWorkOrderBodyVO;
import com.yhl.scp.mps.plan.vo.OperationPackingReportVO;
import com.yhl.scp.mps.plan.vo.PlanNodeTrackingVO;
import com.yhl.scp.mps.productionLeadTime.enums.ProductionLeadTimeEnum;
import com.yhl.scp.mps.productionLeadTime.service.ProductionLeadTimeService;
import com.yhl.scp.mps.productionLeadTime.vo.ProductionLeadTimeVO;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import com.yhl.scp.sds.extension.order.dto.WorkOrderDTO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.order.convertor.WorkOrderConvertor;
import com.yhl.scp.sds.order.service.WorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <code>PlanNodeTrackingServiceImpl</code>
 * <p>
 * 计划追踪明细
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-14 10:16:30
 */
@Slf4j
@Service
public class PlanNodeTrackingServiceImpl implements PlanNodeTrackingService {

    @Resource
    private OperationTaskExtDao operationTaskExtDao;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private MdsFeign mdsFeign;
    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private CacheGetService cacheGetService;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private IpsFeign ipsFeign;
    @Resource
    private OperationPackingReportService operationPackingReportService;
    @Resource
    private MasterPlanService masterPlanService;
    @Resource
    private ProductionLeadTimeService productionLeadTimeService;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private CacheSetService cacheSetService;

    @Override
    public PageInfo<PlanNodeTrackingRes> getPlanNodeTrackingList(PlanNodeTrackingReq planNodeTrackingReq) {
        PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();
        Date planStartTime = planningHorizon.getPlanStartTime();
        String scenario = SystemHolder.getScenario();
        String userId = SystemHolder.getUserId();
        planNodeTrackingReq.setScenario(scenario);
        planNodeTrackingReq.setUserId(userId);
        setReqParams(planNodeTrackingReq);
        ExecutorService executorService = Executors.newFixedThreadPool(10);

        // operation
        CompletableFuture<List<PlanNodeTrackingVO>> planNodeTrackingFuture = getPlanNodeTrackingFuture(planNodeTrackingReq, planStartTime, executorService, scenario);
        // convert product
        CompletableFuture<List<PlanNodeTrackingRes>> productPlanFuture = convertProductMap(planNodeTrackingFuture, scenario, executorService);
        CompletableFuture.allOf(planNodeTrackingFuture, productPlanFuture).join();
        List<PlanNodeTrackingRes> planNodeTrackingRes = productPlanFuture.join();

        // warehouse plan
        CompletableFuture<Void> wareHousePlanFuture = AsyncTaskUtils.runAsync(executorService, "delivery plan", () -> wareHousePlan(planNodeTrackingRes, planNodeTrackingReq));
        // delivery plan
        CompletableFuture<Void> deliveryPlanFuture = AsyncTaskUtils.runAsync(executorService, "delivery plan", () -> getDeliveryPlan(planNodeTrackingRes, planningHorizon, planNodeTrackingReq));
        // stock info
        CompletableFuture<Void> stockInfoFuture = AsyncTaskUtils.runAsync(executorService, "stock info", () -> getStock(planNodeTrackingRes, planNodeTrackingReq));
        CompletableFuture.allOf(wareHousePlanFuture, deliveryPlanFuture, stockInfoFuture).join();

        List<PlanNodeTrackingRes> filterResult = filterAndSortResult(planNodeTrackingRes, planNodeTrackingReq);
        PageInfo<PlanNodeTrackingRes> pageInfo = PageUtils.getPageInfo(filterResult, planNodeTrackingReq.getPageNum(), planNodeTrackingReq.getPageSize());
        return pageInfo;
    }

    private void setReqParams(PlanNodeTrackingReq planNodeTrackingReq) {
        String productCode = planNodeTrackingReq.getProductCode();
        if (StrUtil.isNotEmpty(productCode)) {
            List<String> ids = operationTaskExtDao.selectBomProduct(productCode);
            planNodeTrackingReq.setProductIds(ids);
        }
    }

    private List<PlanNodeTrackingRes> filterAndSortResult(List<PlanNodeTrackingRes> planNodeTrackingRes, PlanNodeTrackingReq planNodeTrackingReq) {
        String orderBy = planNodeTrackingReq.getOrderBy();
        if (StringUtils.isNotBlank(orderBy)) {
            com.yhl.platform.common.utils.CollectionUtils.sort(planNodeTrackingRes, orderBy);
        }
        return planNodeTrackingRes;
    }

    private void wareHousePlan(List<PlanNodeTrackingRes> planNodeTrackingRes, PlanNodeTrackingReq planNodeTrackingReq) {
        List<String> productCodes = StreamUtils.columnToList(planNodeTrackingRes, PlanNodeTrackingRes::getProductCode);
        if(CollectionUtils.isEmpty(productCodes)){
            return;
        }
        DynamicDataSourceContextHolder.setDataSource(planNodeTrackingReq.getScenario());
        String scenario = planNodeTrackingReq.getScenario();
        Date currentDate = DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd") + " 07:30:00", "yyyy-MM-dd HH:mm:ss");
        Date dayLastTime = DateUtils.getDayLastTime(currentDate);
        String subInventory = getRangeData(scenario, "SUB_INVENTORY", "INTERNAL");
        String salesOrganization = getRangeData(scenario, StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode(), "INTERNAL");
        List<WarehouseReleaseRecordVO> warehouseReleaseRecordVOS = operationTaskExtDao.selectWareHousePlan(productCodes, currentDate, dayLastTime, subInventory, salesOrganization);
        Map<String, BigDecimal> warehouseReleaseRecordMap = warehouseReleaseRecordVOS.stream().collect(Collectors.toMap(WarehouseReleaseRecordVO::getItemCode, WarehouseReleaseRecordVO::getSumQty, (v1, v2) -> v1));
        for (PlanNodeTrackingRes planNodeTrackingRe : planNodeTrackingRes) {
            BigDecimal qty = warehouseReleaseRecordMap.getOrDefault(planNodeTrackingRe.getProductCode(), BigDecimal.ZERO);
            planNodeTrackingRe.setOnTheDayQuantity(qty.setScale(0,RoundingMode.DOWN).toString());
        }
        DynamicDataSourceContextHolder.clearDataSource();
    }

    private String getRangeData(String scenario, String rangeType, String rangeCategory) {
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(scenario, rangeType, rangeCategory, null);
        return scenarioBusinessRange.getData().getRangeData();
    }

    private void getStock(List<PlanNodeTrackingRes> planNodeTrackingRes, PlanNodeTrackingReq planNodeTrackingReq) {
        List<String> productCodes = StreamUtils.columnToList(planNodeTrackingRes, PlanNodeTrackingRes::getProductCode);
        if(CollectionUtils.isEmpty(productCodes)){
            return;
        }
        DynamicDataSourceContextHolder.setDataSource(planNodeTrackingReq.getScenario());
        String subInventory = getRangeData(planNodeTrackingReq.getScenario(), "SUB_INVENTORY", "INTERNAL");
        String scenario = planNodeTrackingReq.getScenario();
        String userId = planNodeTrackingReq.getUserId();
        Map<String, String> standardStepMap = cacheGetService.getStandardStepMap(scenario);
        List<InventoryBatchDetailVO> inventoryBatchDetails = cacheGetService.getInventoryBatchDetails(scenario, userId, productCodes);
        Map<String, SubInventoryCargoLocationVO> cargoLocationMap = cacheGetService.getCargoLocationMap(scenario, userId, inventoryBatchDetails);
        List<NewStockPointVO> newStockPoints = cacheGetService.getNewStockPoints(scenario);
        List<String> productOrganizations = newStockPoints.stream().filter(e -> StringUtils.isNotEmpty(e.getOrganizeType()) && StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode().equals(e.getOrganizeType())).map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        List<String> saleOrganizations = newStockPoints.stream().filter(e -> StringUtils.isNotEmpty(e.getOrganizeType()) && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType())).map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        Map<String, List<InventoryBatchDetailVO>> finishInventoryMap =
                inventoryBatchDetails.stream().filter(t -> StringUtils.isEmpty(t.getOperationCode()) && saleOrganizations.contains(t.getStockPointCode()) && subInventory.equals(t.getSubinventory())).collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));
        Map<String, List<InventoryBatchDetailVO>> operationInventoryMap =
                inventoryBatchDetails.stream().filter(t -> productOrganizations.contains(t.getStockPointCode())).collect(Collectors.groupingBy(p -> String.join("-", p.getStockPointCode(), p.getProductCode(), p.getOperationCode())));
        String specialStockPoint = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT"));
        String specialStockPoint2 = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT_S2").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT_S2"));
        for (PlanNodeTrackingRes planNodeTrackingRe : planNodeTrackingRes) {
            String parentProductCode = planNodeTrackingRe.getProductCode();
            NewProductStockPointVO newProductStockPoint = new NewProductStockPointVO();
            String stockPointCode = planNodeTrackingRe.getStockPointCode();
            String semiProductCode = planNodeTrackingRe.getSemiProductCode();
            newProductStockPoint.setStockPointCode(stockPointCode);
            newProductStockPoint.setProductCode(semiProductCode);
            String cx = MasterPlanServiceImpl.getInventory(MasterPlanServiceImpl.FORMING_OPERATION, newProductStockPoint, standardStepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
            String hp = MasterPlanServiceImpl.getInventory(MasterPlanServiceImpl.MERGING_OPERATION, newProductStockPoint, standardStepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
            String bz = MasterPlanServiceImpl.getInventory(MasterPlanServiceImpl.PACKAGING_OPERATION, newProductStockPoint, standardStepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, parentProductCode);
            planNodeTrackingRe.setPackingQuantity(bz);
            planNodeTrackingRe.setMergeQuantity(hp);
            planNodeTrackingRe.setFormingQuantity(cx);
            if (finishInventoryMap.containsKey(parentProductCode)) {
                // 维护产品编码对应的成品库存
                List<InventoryBatchDetailVO> finishList = MasterPlanServiceImpl.getFinishInventory(finishInventoryMap.get(parentProductCode), cargoLocationMap);
                BigDecimal finishInventory = finishList.stream().map(t -> new BigDecimal(t.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
                planNodeTrackingRe.setFinishedQuantity(finishInventory.setScale(0, RoundingMode.DOWN).toString());
            } else {
                planNodeTrackingRe.setFinishedQuantity("0");
            }
        }

        DynamicDataSourceContextHolder.clearDataSource();
    }

    private void getDeliveryPlan(List<PlanNodeTrackingRes> planNodeTrackingRes, PlanningHorizonVO planningHorizon, PlanNodeTrackingReq planNodeTrackingReq) {
        List<String> productCodes = StreamUtils.columnToList(planNodeTrackingRes, PlanNodeTrackingRes::getProductCode);
        if(CollectionUtils.isEmpty(productCodes)){
            return;
        }
        DynamicDataSourceContextHolder.setDataSource(planNodeTrackingReq.getScenario());
        String scenario = planNodeTrackingReq.getScenario();
        String userId = planNodeTrackingReq.getUserId();
        Map<String, List<DeliveryPlanVO2>> deliveryPlanMap = cacheGetService.getDeliveryPlanMap(scenario, userId, productCodes, planningHorizon);
        Date currentDate = DateUtils.getDayFirstTime(new Date());
        for (PlanNodeTrackingRes planNodeTrackingRe : planNodeTrackingRes) {
            String productCode = planNodeTrackingRe.getProductCode();
            if (!deliveryPlanMap.containsKey(productCode)) {
                continue;
            }
            List<DeliveryPlanVO2> deliveryPlanVO2List = deliveryPlanMap.get(productCode);
            Integer currentQuantity = getDemandQuantity(deliveryPlanVO2List, currentDate, 1);
            Integer twoQuantity = getDemandQuantity(deliveryPlanVO2List, currentDate, 2);
            Integer thereQuantity = getDemandQuantity(deliveryPlanVO2List, currentDate, 3);
            Integer sevenQuantity = getDemandQuantity(deliveryPlanVO2List, currentDate, 7);
            planNodeTrackingRe.setCurrentQuantity(currentQuantity);
            planNodeTrackingRe.setTwoDaysQuantity(twoQuantity);
            planNodeTrackingRe.setThreeDaysQuantity(thereQuantity);
            planNodeTrackingRe.setSevenDaysQuantity(sevenQuantity);
        }
        DynamicDataSourceContextHolder.clearDataSource();
    }

    private Integer getDemandQuantity(List<DeliveryPlanVO2> deliveryPlanVO2List, Date currentDate, int maxNumberDay) {
        return deliveryPlanVO2List.stream().filter(o -> DateUtil.betweenDay(o.getDemandTime(), currentDate, true) <= maxNumberDay)
                .map(DeliveryPlanVO2::getDemandQuantity).mapToInt(Integer::intValue).sum();
    }


    private CompletableFuture<List<PlanNodeTrackingRes>> convertProductMap(CompletableFuture<List<PlanNodeTrackingVO>> planNodeTrackingFuture, String scenario, ExecutorService executorService) {
        return planNodeTrackingFuture.thenApplyAsync(operation -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<OperationPackingReportVO> operationPackingReportVOS = operationPackingReportService.selectAll();
            Map<String, List<OperationPackingReportVO>> reportOpeationMap = StreamUtils.mapListByColumn(operationPackingReportVOS, OperationPackingReportVO::getOperationId);
            Map<String, List<PlanNodeTrackingVO>> childOrderMap = operation.stream()
                    .filter(p -> StrUtil.isNotBlank(p.getOrderParentId()))
                    .collect(Collectors.groupingBy(PlanNodeTrackingVO::getOrderParentId));

            Map<String, List<PlanNodeTrackingVO>> productPlanMap = StreamUtils.mapListByColumn(operation, PlanNodeTrackingVO::getProductCode);
            Map<String, List<PlanNodeTrackingVO>> orderMap = StreamUtils.mapListByColumn(operation, PlanNodeTrackingVO::getOrderId);
            DynamicDataSourceContextHolder.clearDataSource();
            return productPlanMap.values().stream()
                    .filter(value -> !value.isEmpty())
                    .filter(value -> !value.get(0).getBomType().equals(AbstractMpsResult.CHILD_BOM))
                    .map(value -> buildPlanNodeTrackingRes(value, childOrderMap, productPlanMap, orderMap, reportOpeationMap))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }, executorService);
    }

    private PlanNodeTrackingRes buildPlanNodeTrackingRes(List<PlanNodeTrackingVO> value,
                                                         Map<String, List<PlanNodeTrackingVO>> childOrderMap,
                                                         Map<String, List<PlanNodeTrackingVO>> productPlanMap,
                                                         Map<String, List<PlanNodeTrackingVO>> orderMap,
                                                         Map<String, List<OperationPackingReportVO>> reportOpeationMap) {
        PlanNodeTrackingVO planNodeTracking = value.get(0);
        // 获取最早的排程订单
        PlanNodeTrackingVO earliestOrder = choseOperation(planNodeTracking, productPlanMap, value, childOrderMap);
        if (earliestOrder == null) {
            return null;
        }
        // 关键工序有计划的第一个订单任务得全工序
        List<PlanNodeTrackingVO> planNodeTrackingVOS = orderMap.get(earliestOrder.getOrderId());
        if (CollectionUtils.isEmpty(planNodeTrackingVOS)) {
            return null;
        }
        // 查找成型工序
        Optional<PlanNodeTrackingVO> formingOperationOpt = planNodeTrackingVOS.stream()
                .filter(p -> p.getStandardStepType().equals(StandardStepEnum.FORMING_PROCESS.getCode()) &&
                        StrUtil.isEmpty(p.getParentId()))
                .findFirst();
        if (!formingOperationOpt.isPresent()) {
            return null;
        }
        PlanNodeTrackingRes planNodeTrackingRes = new PlanNodeTrackingRes();
        planNodeTrackingRes.setOem(planNodeTracking.getOem());
        planNodeTrackingRes.setVehicleModelCode(planNodeTracking.getVehicleModelCode());
        // 成品代码
        planNodeTrackingRes.setProductCode(planNodeTracking.getProductCode());
        // 半品代码
        planNodeTrackingRes.setSemiProductCode(earliestOrder.getProductCode());
        // 齐套状态
        planNodeTrackingRes.setKitStatus(earliestOrder.getOrderKitStatus());
        // 订单id
        planNodeTrackingRes.setOrderId(earliestOrder.getOrderId());
        planNodeTrackingRes.setStockPointCode(earliestOrder.getStockPointCode());

        PlanNodeTrackingVO formingOperation = formingOperationOpt.get();
        String operationId = formingOperation.getId();

        // 设置成型工序计划时间
        String formingTimeInterval = String.format("%s ~ %s",
                DateUtils.dateToString(formingOperation.getStartTime(), "MM-dd HH:mm"),
                DateUtils.dateToString(formingOperation.getEndTime(), "MM-dd HH:mm"));
        planNodeTrackingRes.setFormingOperationPlanTime(formingTimeInterval);

        // 计算成型工序报工数量
        BigDecimal quantity = formingOperation.getQuantity();
        BigDecimal reportQuantity = calculateReportQuantity(planNodeTrackingVOS, operationId);
        String formingQty = String.format("%s / %s", reportQuantity.setScale(0, RoundingMode.DOWN), quantity.setScale(0, RoundingMode.DOWN));
        planNodeTrackingRes.setFormingOperationQuantity(formingQty);

        // 处理TREE_BOM类型的包装工序
        if (planNodeTracking.getBomType().equals(AbstractMpsResult.TREE_BOM)) {
            List<PlanNodeTrackingVO> parentOrderOperations = orderMap.get(earliestOrder.getOrderParentId());
            if (CollectionUtils.isNotEmpty(parentOrderOperations)) {
                planNodeTrackingVOS = parentOrderOperations;
            }
        }

        // 设置包装工序信息
        setPackingOperationInfo(planNodeTrackingVOS, planNodeTrackingRes, reportOpeationMap);

        return planNodeTrackingRes;
    }

    private BigDecimal calculateReportQuantity(List<PlanNodeTrackingVO> planNodeTrackingVOS, String operationId) {
        return planNodeTrackingVOS.stream()
                .filter(p -> StrUtil.isNotBlank(p.getParentId()) &&
                        p.getParentId().equals(operationId) &&
                        p.getFinishedQty() != null)
                .map(PlanNodeTrackingVO::getFinishedQty)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private void setPackingOperationInfo(List<PlanNodeTrackingVO> planNodeTrackingVOS, PlanNodeTrackingRes planNodeTrackingRes,
                                         Map<String, List<OperationPackingReportVO>> reportOpeationMap) {
        planNodeTrackingVOS.stream()
                .filter(p -> p.getStandardStepName().equals(MasterPlanServiceImpl.PACKAGING_OPERATION) &&
                        StrUtil.isEmpty(p.getParentId()))
                .findFirst()
                .ifPresent(packingOperation -> {
                    String id = packingOperation.getId();
                    if (reportOpeationMap.containsKey(id)) {
                        List<OperationPackingReportVO> operationPackingReportVOS = reportOpeationMap.get(id);
                        operationPackingReportVOS.sort(Comparator.comparing(OperationPackingReportVO::getStartTime));
                        List<String> packingTimeIntervalList = new ArrayList<>();
                        List<String> packingQtyList = new ArrayList<>();
                        for (OperationPackingReportVO operationPackingReportVO : operationPackingReportVOS) {
                            Date startTime = operationPackingReportVO.getStartTime();
                            Date endTime = operationPackingReportVO.getEndTime();
                            String packingTimeInterval = String.format("%s ~ %s",
                                    DateUtils.dateToString(startTime, "MM-dd HH:mm"),
                                    DateUtils.dateToString(endTime, "MM-dd HH:mm"));
                            BigDecimal finishedQuantity = operationPackingReportVO.getFinishedQuantity().setScale(0);
                            BigDecimal quantity = operationPackingReportVO.getQuantity().setScale(0);
                            String packingQty = String.format("%s / %s", finishedQuantity, quantity);
                            packingTimeIntervalList.add(packingTimeInterval);
                            packingQtyList.add(packingQty);
                        }
                        planNodeTrackingRes.setPackingOperationPlanTimeList(packingTimeIntervalList);
                        planNodeTrackingRes.setPackingOperationQuantityList(packingQtyList);
                    }
                    planNodeTrackingRes.setPackingOperationId(id);
                });
    }


    private PlanNodeTrackingVO choseOperation(PlanNodeTrackingVO planNodeTracking, Map<String, List<PlanNodeTrackingVO>> productPlanMap,
                                              List<PlanNodeTrackingVO> value, Map<String, List<PlanNodeTrackingVO>> childOrderMap) {
        String bomType = planNodeTracking.getBomType();
        if (bomType.equals(AbstractMpsResult.CHAIN_BOM)) {
            List<PlanNodeTrackingVO> firstPlan = value.stream().filter(p -> p.getStandardStepType().equals(StandardStepEnum.FORMING_PROCESS.getCode()))
                    .sorted(Comparator.comparing(PlanNodeTrackingVO::getStartTime))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(firstPlan)) {
                return null;
            }
            return firstPlan.get(0);
        }

        String orderId = value.get(0).getOrderId();
        // 找到父订单下的子订单
        List<PlanNodeTrackingVO> childOrders = childOrderMap.get(orderId);
        if (CollectionUtils.isEmpty(childOrders)) {
            return null;
        }

        List<String> childProductList = childOrders.stream()
                .map(PlanNodeTrackingVO::getProductCode)
                .distinct()
                .collect(Collectors.toList());

        return childProductList.stream()
                .map(productPlanMap::get)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .filter(p -> p.getStandardStepType().equals(StandardStepEnum.FORMING_PROCESS.getCode()))
                .max(Comparator.comparing(PlanNodeTrackingVO::getEndTime))
                .orElse(null);
    }


    private CompletableFuture<List<PlanNodeTrackingVO>> getPlanNodeTrackingFuture(PlanNodeTrackingReq planNodeTrackingReq, Date planStartTime, ExecutorService executorService, String scenario) {
        return AsyncTaskUtils.supplyAsync(executorService, "getPlanNodeTrackingFuture", () -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<PlanNodeTrackingVO> planNodeTrackingVOS = operationTaskExtDao.selectPlanNodeTrackingList(planNodeTrackingReq, planStartTime);
            DynamicDataSourceContextHolder.clearDataSource();
            return planNodeTrackingVOS;
        });
    }

    @Override
    public List<FulfillDetailVO> fulfillDetails(String orderId) {
        return operationTaskExtDao.fulfillDetails(orderId);
    }


    @Override
    public void doDueDateUpdate(MasterPlanReq masterPlanReq) {
        List<MasterPlanWorkOrderBodyVO> masterPlanWorkOrderBodyVOS = new ArrayList<>();
        String scenario = SystemHolder.getScenario();
        String userId = SystemHolder.getUserId();
        masterPlanReq.setUserId(userId);
        masterPlanReq.setScenario(scenario);
        masterPlanService.masterPlanWorkOrder(new Pagination(1, 10), masterPlanReq, masterPlanWorkOrderBodyVOS);
        if (CollectionUtils.isEmpty(masterPlanWorkOrderBodyVOS)) {
            log.info("没有需要更新计划开始时间数据");
            return;
        }
        log.info("开始更新交期");
        masterPlanWorkOrderBodyVOS = masterPlanWorkOrderBodyVOS.stream()
                .filter(p -> null != p.getDeliveryCoverDate())
                .collect(Collectors.toList());
        List<String> productCodes = StreamUtils.columnToList(masterPlanWorkOrderBodyVOS, MasterPlanWorkOrderBodyVO::getParentProductCode)
                .stream().distinct().collect(Collectors.toList());
        List<String> routingIds = StreamUtils.columnToList(masterPlanWorkOrderBodyVOS, MasterPlanWorkOrderBodyVO::getRoutingId)
                .stream().distinct().collect(Collectors.toList());
        List<String> orderIds = StreamUtils.columnToList(masterPlanWorkOrderBodyVOS, MasterPlanWorkOrderBodyVO::getTopWorkOrderId)
                .stream().distinct().collect(Collectors.toList());
        List<RoutingStepVO> routingStepVOS = operationTaskExtDao.selectResourceStep(routingIds);
        List<SafetyStockLevelVO> safetyStockLevelVOS = dfpFeign.selectSafetyStockLevelByProductCodeList(scenario, productCodes);
        List<String> saleOrganizationStockCode = getSaleOrganization(scenario);
        List<MdsCuringTimeVO> curingTimeVOS = newMdsFeign.selectCuringTimeList(scenario);
        List<MdsFinishedProductDeliveryVO> finishedProductDeliveryVOS = newMdsFeign.selectMdsFinishedProductDelivery(scenario);
        List<ProductionLeadTimeVO> productionLeadTime = productionLeadTimeService.selectByParams(new HashMap<>());
        List<MdsProductStockPointBaseVO> stockPointBaseVOS = newMdsFeign.selectProductStockPointBaseByParams(SystemHolder.getScenario(), ImmutableMap.of("productCodeList", productCodes));

        Map<String, List<ProductionLeadTimeVO>> leadTimeMap = productionLeadTime
                .stream().collect(Collectors.groupingBy(p -> StrUtil.join("&", p.getStockPointCode(), p.getOperationCode(), p.getLeadTimeType(), p.getMainLineGroup())));
        Map<String, List<ProductionLeadTimeVO>> lineGroup = productionLeadTime.stream().filter(p -> StrUtil.isNotEmpty(p.getMainLineGroup())).collect(Collectors.toList())
                .stream().collect(Collectors.groupingBy(p -> StrUtil.join("&", p.getMainLineGroup(), p.getOperationCode())));
        Map<String, List<RoutingStepVO>> stepMap = StreamUtils.mapListByColumn(routingStepVOS, RoutingStepVO::getRoutingId);
        Map<String, MdsFinishedProductDeliveryVO> finishedProductDeliveryVOMap = finishedProductDeliveryVOS
                .stream().filter(p -> saleOrganizationStockCode.contains(p.getStockPointCode()))
                .collect(Collectors.toMap(MdsFinishedProductDeliveryVO::getProductCode, v -> v, (k1, k2) -> k1));


        Map<String, MdsCuringTimeVO> curingTimeVOMap = StreamUtils.mapByColumn(curingTimeVOS,
                p -> StrUtil.join("&", p.getStockPointCode(), p.getProductCode()));
        Map<String, List<SafetyStockLevelVO>> safetyStockLecelMap = safetyStockLevelVOS.stream()
                .filter(p -> saleOrganizationStockCode.contains(p.getStockCode()))
                .collect(Collectors.groupingBy(SafetyStockLevelVO::getProductCode));
        //物料对应夹丝类型
        Map<String, String> clampTypeBaseMap = stockPointBaseVOS.stream()
                .filter(t -> t.getClampType() != null && !"/".equals(t.getClampType()) && !"无".equals(t.getClampType()))
                .collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getClampType, (v1, v2) -> v1));

        //物料对应调光类型
        Map<String, String> itemFlagBaseMap = stockPointBaseVOS.stream()
                .filter(t -> t.getItemFlag() != null && !"/".equals(t.getItemFlag()) && !"无".equals(t.getItemFlag()))
                .collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getItemFlag, (v1, v2) -> v1));
        //物料对应除膜
        Map<String, String> attr1BaseMap = stockPointBaseVOS.stream()
                .filter(t -> t.getAttr1() != null && !"/".equals(t.getAttr1()) && !"无".equals(t.getAttr1()))
                .collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getAttr1, (v1, v2) -> v1));
        // 物料对应HUD
        Map<String, String> hudMap = stockPointBaseVOS.stream()
                .filter(t -> t.getAttr1() != null && !"/".equals(t.getHud()) && !"无".equals(t.getHud()))
                .collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getHud, (v1, v2) -> v1));

        List<String> allIdMappingWorkOrders = operationTaskExtDao.selectAllIdMappingWorkOrders(orderIds);
        List<WorkOrderVO> workOrderVOS = workOrderService.selectByPrimaryKeys(allIdMappingWorkOrders);

        Map<String, WorkOrderVO> workOrderVOMap = StreamUtils.mapByColumn(workOrderVOS, WorkOrderVO::getId);
        Map<String, List<WorkOrderVO>> childOrderMap = workOrderVOS.stream().filter(p -> StrUtil.isNotEmpty(p.getParentId()))
                .collect(Collectors.groupingBy(WorkOrderVO::getParentId));

        List<WorkOrderVO> updateWorkOrderVOS = new ArrayList<>();
        for (MasterPlanWorkOrderBodyVO masterPlanWorkOrderBodyVO : masterPlanWorkOrderBodyVOS) {
            String deliveryCoverDate = masterPlanWorkOrderBodyVO.getDeliveryCoverDate();
            String productCode = masterPlanWorkOrderBodyVO.getParentProductCode();
            String stockPointCode = masterPlanWorkOrderBodyVO.getParentProductStockPoint();
            String plannedQuantity = masterPlanWorkOrderBodyVO.getTopWorkOrderQuantity();
            // 制造订单id
            String topWorkOrderId = masterPlanWorkOrderBodyVO.getTopWorkOrderId();
            Date demandTime = DateUtils.stringToDate(deliveryCoverDate, DateUtils.COMMON_DATE_STR3);
            String safetyKey = CharSequenceUtil.join("&", stockPointCode, productCode);


            // - 标准安全库存天数
            if (safetyStockLecelMap.containsKey(productCode)) {
                SafetyStockLevelVO safetyStockLevelVO = safetyStockLecelMap.get(productCode).get(0);
                int minStockDay = safetyStockLevelVO.getMinStockDay().intValue();
                demandTime = DateUtil.offsetDay(demandTime, -minStockDay);
            }
            // -固化时间
            if (curingTimeVOMap.containsKey(safetyKey)) {
                MdsCuringTimeVO mdsCuringTimeVO = curingTimeVOMap.get(safetyKey);
                String ghTime = mdsCuringTimeVO.getGhTime();
                if (StrUtil.isNotEmpty(ghTime) && BigDecimalUtils.toBigDecimal(ghTime).compareTo(BigDecimal.ONE) >= 1) {
                    demandTime = DateUtil.offsetHour(demandTime, -BigDecimalUtils.toBigDecimal(ghTime).intValue());

                }
            }
            if (finishedProductDeliveryVOMap.containsKey(productCode)) {
                MdsFinishedProductDeliveryVO mdsFinishedProductDeliveryVO = finishedProductDeliveryVOMap.get(productCode);
                String storageTime = mdsFinishedProductDeliveryVO.getStorageTime();
                if (StrUtil.isNotEmpty(storageTime) && BigDecimalUtils.toBigDecimal(storageTime).compareTo(BigDecimal.ONE) >= 1) {
                    demandTime = DateUtil.offsetHour(demandTime, -BigDecimalUtils.toBigDecimal(storageTime).intValue());
                }
            }

            String routingId = masterPlanWorkOrderBodyVO.getRoutingId();
            List<RoutingStepVO> routingSteps = stepMap.get(routingId);
            boolean whetherFirstDemand = true;
            for (RoutingStepVO routingStep : routingSteps) {
                String standardResourceCode = routingStep.getConnectionTask();
                Integer sequenceNoStep = routingStep.getSequenceNo();
                String standardStepType = routingStep.getStandardStepCode();
                BigDecimal productionTime = routingStep.getYield();

                if (StandardStepEnum.FORMING_PROCESS.getCode().equals(standardStepType)) {
                    if (StrUtil.isNotEmpty(standardResourceCode)) {
                        String key = StrUtil.join("&", standardResourceCode, sequenceNoStep);
                        BigDecimal leadTimeCount = BaseScheduleSupport.calculateLeadTime(key, lineGroup, ProductionLeadTimeEnum.POST_PRODUCTION_PROCESSING_TIME.getCode(),
                                productCode, clampTypeBaseMap, itemFlagBaseMap, attr1BaseMap, hudMap, whetherFirstDemand, new ArrayList<>());
                        demandTime = DateUtil.offsetHour(demandTime, -leadTimeCount.intValue());
                    }
                    break;
                }
                if (StrUtil.isNotEmpty(standardResourceCode)) {
                    String key = StrUtil.join("&", standardResourceCode, sequenceNoStep);
                    BigDecimal leadTimeCount = BaseScheduleSupport.calculateLeadTime(key, lineGroup, ProductionLeadTimeEnum.PRODUCTION_LEAD_TIME.getCode(),
                            productCode, clampTypeBaseMap, itemFlagBaseMap, attr1BaseMap, hudMap, whetherFirstDemand, new ArrayList<>());
                    demandTime = DateUtil.offsetHour(demandTime, -leadTimeCount.intValue());
                }
                // 扣减成型后工序得时间
                String key = StrUtil.join("&", stockPointCode, sequenceNoStep, ProductionLeadTimeEnum.PRODUCTION_PROCESSING_TIME.getCode(), standardResourceCode);
                if (leadTimeMap.containsKey(key)) {
                    BigDecimal beatQty = BigDecimalUtils.multiply(BigDecimalUtils.toBigDecimal(plannedQuantity), productionTime, 0);
                    demandTime = DateUtil.offsetSecond(demandTime, -BigDecimalUtils.toBigDecimal(beatQty).intValue());
                }
            }
            WorkOrderVO workOrderVO = workOrderVOMap.get(topWorkOrderId);
            String id = workOrderVO.getId();
            String newRemark = workOrderVO.getRemark() + ";发货计划更新交期";
            workOrderVO.setDueDate(demandTime);
            workOrderVO.setRemark(newRemark);
            updateWorkOrderVOS.add(workOrderVO);
            if (childOrderMap.containsKey(id)) {
                Date finalDemandTime = demandTime;
                List<WorkOrderVO> workOrderChild = childOrderMap.get(id);
                workOrderChild.forEach(child -> {
                    child.setDueDate(finalDemandTime);
                    child.setRemark(child.getRemark() + ";发货计划更新交期");
                });
                updateWorkOrderVOS.addAll(workOrderChild);
            }
        }
        log.info("更新交期制造订单数量：{}", updateWorkOrderVOS.size());
        if (CollectionUtils.isNotEmpty(updateWorkOrderVOS)) {
            WorkOrderConvertor instance = WorkOrderConvertor.INSTANCE;
            List<WorkOrderDTO> collect = updateWorkOrderVOS.stream().map(instance::vo2Dto).collect(Collectors.toList());
            workOrderService.doUpdateBatch(collect);
        }
        cacheSetService.refreshWorkOrderCache(scenario);
        log.info("结束更新交期");
    }

    private List<String> getSaleOrganization(String scenario) {
        List<String> newStockPointVOS = newMdsFeign.selectAllStockPoint(scenario)
                .stream().filter(p -> StrUtil.isNotEmpty(p.getOrganizeType()) &&
                        (p.getOrganizeType().equals(StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode())))
                .filter(p -> StrUtil.isNotEmpty(p.getStockPointType()) && p.getStockPointType().equals(StockPointTypeEnum.BC.getCode()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        return newStockPointVOS;
    }

}
