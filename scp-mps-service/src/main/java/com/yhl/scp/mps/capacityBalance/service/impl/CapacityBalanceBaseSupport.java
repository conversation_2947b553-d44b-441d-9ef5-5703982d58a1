package com.yhl.scp.mps.capacityBalance.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.oem.enums.OemBusinessTypeEnum;
import com.yhl.scp.dfp.oem.vo.OemTransportTimeVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.basic.routing.vo.BomRoutingStepInputBasicVO;
import com.yhl.scp.mds.basic.routing.vo.RoutingBasicVO;
import com.yhl.scp.mds.basic.routing.vo.RoutingStepBasicVO;
import com.yhl.scp.mds.basic.routing.vo.RoutingStepResourceBasicVO;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.extension.routing.vo.*;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.capacityBalance.dao.CapacityBalanceDao;
import com.yhl.scp.mps.capacityBalance.dto.CapacityBalanceAlgorithmDataDTO;
import com.yhl.scp.mps.capacityBalance.dto.CapacitySupplyRelationshipExceptionDTO;
import com.yhl.scp.mps.capacityBalance.enums.CapacityBalanceRule;
import com.yhl.scp.mps.capacityBalance.enums.LockStatusEnum;
import com.yhl.scp.mps.capacityBalance.service.ParamCapacityBalanceService;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.capacityBalance.vo.ParamCapacityBalanceVO;
import com.yhl.scp.mps.enums.SupplyModelEnum;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CapacityBalanceBaseSupport</code>
 * <p>
 * 产能平衡公共支持类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-24 10:36:40
 */
@Slf4j
public class CapacityBalanceBaseSupport {

    private static final String LIMIT_PRODUCT_CLASSIFY = "C0.0";
    private static final List<String> LIMIT_PRODUCT_TYPE_LIST = Arrays.asList("FG", "SA");
    @Resource
    protected IpsNewFeign ipsNewFeign;
    @Resource
    protected NewMdsFeign newMdsFeign;
    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private ParamCapacityBalanceService paramCapacityBalanceService;
    @Resource
    private CapacityBalanceDao capacityBalanceDao;

    protected static void replenishmentQty(Map<String, Map<String, Integer>> qty, Map<String, String> routingIdOfProductCodeMap, Map<String, List<String>> sourceProductCodeListMap, List<String> allInputProductIdList, Map<String, String> productIdMap, Map<String, List<String>> map) {
        for (Map.Entry<String, Map<String, Integer>> entry : qty.entrySet()) {
            Map<String, Integer> monthQtyMap = entry.getValue();
            for (String productId : allInputProductIdList) {
                List<String> sourceProductCodeList = sourceProductCodeListMap.get(productId);
                String newProductCode = productIdMap.get(productId);
                if (StringUtils.isNotEmpty(newProductCode) && CollectionUtils.isNotEmpty(sourceProductCodeList)) {
                    map.put(newProductCode, sourceProductCodeList);
                } else {
                    continue;
                }
                boolean a = routingIdOfProductCodeMap.containsKey(newProductCode);
                if (a) {
                    int sum = monthQtyMap.entrySet().stream()
                            .filter(t -> sourceProductCodeList.contains(t.getKey()))
                            .map(Map.Entry::getValue).mapToInt(Integer::intValue).sum();
                    int sourceQty = monthQtyMap.getOrDefault(newProductCode, 0);

                    monthQtyMap.put(newProductCode, sourceQty + sum);
                }
            }
        }
    }

    protected PlanningHorizonVO getPlanningHorizonVO(String mdsScenario) {
        return newMdsFeign.selectPlanningHorizon(mdsScenario);
    }

    protected List<DeliveryPlanVO2> getDeliveryPlanData(PlanningHorizonVO planningHorizonVO) {
        Date planStartTime = planningHorizonVO.getPlanStartTime();
        String demandTimeStart = DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR1);
        return capacityBalanceDao.selectDeliveryPlanPublished(demandTimeStart);
    }

    /**
     * 发货计划按天分解
     */
    protected Map<String, Map<String, Integer>> getDeliveryPlanDataMap(List<String> demandTimeList, Map<String, List<DeliveryPlanVO2>> demandTimeDayMap, List<String> prouctCodeList) {
        // 构建发货计划每天每个产品的数量
        Map<String, Map<String, Integer>> deliveryPlanDataMap = new HashMap<>();
        for (String day : demandTimeList) {
            Map<String, Integer> dayMap = new HashMap<>();
            if (demandTimeDayMap.containsKey(day)) {
                dayMap = demandTimeDayMap.get(day).stream().collect(Collectors.groupingBy(DeliveryPlanVO2::getProductCode, Collectors.summingInt(DeliveryPlanVO2::getDemandQuantity)));
            }
            // 补充一下当天没发货计划的数据
            for (String productCode : prouctCodeList) {
                if (!dayMap.containsKey(productCode)) {
                    dayMap.put(productCode, 0);
                }
            }
            deliveryPlanDataMap.put(day, dayMap);
        }
        return deliveryPlanDataMap;
    }

    /**
     * 获取startDateStr，endDateStr范围内日期列表
     */
    protected List<String> getDemandTimeList(String startDateStr, String endDateStr, String pattern) {
        // 将字符串转换为 LocalDate
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        LocalDate startDate = LocalDate.parse(startDateStr, formatter);
        LocalDate endDate = LocalDate.parse(endDateStr, formatter);

        // 创建一个列表来存储日期字符串
        List<String> dateStrings = new ArrayList<>();

        // 遍历日期并添加到列表中
        while (!startDate.isAfter(endDate)) {
            // 格式化日期为 "yyyyMMdd" 格式
            String dateString = startDate.format(formatter);
            dateStrings.add(dateString);
            startDate = startDate.plusDays(1);
        }
        return dateStrings;
    }

    /**
     * 需求预测数据过滤-按月过滤
     */
    protected List<DeliveryPlanVO2> cleanForecastFilterData(List<DeliveryPlanVO2> data, PlanningHorizonVO planningHorizonVO) {
        Date planStartTime = planningHorizonVO.getPlanStartTime();
        DateTime firstDayOfMonth = DateUtil.beginOfMonth(planStartTime);
        return data.stream().filter(t -> !t.getDemandTime().before(firstDayOfMonth)).collect(Collectors.toList());
    }

    /**
     * 获取库存点数据
     */
    protected List<NewStockPointVO> getStockPointMap(String mdsScenario) {
        // 查询库存点数据
        return newMdsFeign.selectAllStockPoint(mdsScenario);
    }

    /**
     * 实时库存扣减
     */
    protected Integer productInventory(Map<String, Integer> productInventoryMap, Integer demandQuantity, String code) {
        if (productInventoryMap.containsKey(code) && productInventoryMap.get(code) > 0) {
            if (productInventoryMap.get(code) < demandQuantity) {
                demandQuantity = demandQuantity - productInventoryMap.get(code);
                productInventoryMap.put(code, 0);
            } else {
                productInventoryMap.put(code, productInventoryMap.get(code) - demandQuantity);
                demandQuantity = 0;
            }
        }
        return demandQuantity;
    }

    /**
     * 工序净需求拆解-产能平衡按天计算使用
     */
    protected void decompositionProcessDay(List<String> dayList,
                                           List<String> routingStepIds,
                                           Map<String, Map<String, Integer>> qty,
                                           Map<String, Integer> operationQty,
                                           Map<String, Integer> primitiveOperationQty,
                                           Map<String, Integer> operationInventoryMap,
                                           Map<String, BigDecimal> lockQtyData,
                                           Map<String, String> routingIdOfProductCodeMap,
                                           Map<String, List<RoutingStepVO>> routingStepOfProductMap,
                                           Map<String, List<String>> demandOutsourcingPercentDays,
                                           Map<String, List<String>> processOutsourcingPercentDays,
                                           Map<String, NewProductStockPointVO> productCodeMap,
                                           List<CapacitySupplyRelationshipVO> outDataList,
                                           boolean weekFlag,
                                           Map<String, BigDecimal> productLeadTimeMap,
                                           Date now,
                                           String deliveryPlanStart,
                                           Map<String, String> routingStepIdMap,
                                           Map<String, StandardStepVO> standardStepMap,
                                           Map<String, List<CapacitySupplyRelationshipVO>> lockDataMap,
                                           List<String> logList) {
        for (String day : dayList) {
            Map<String, Integer> productMap = qty.get(day);
            for (Map.Entry<String, Integer> product : productMap.entrySet()) {
                String productCode = product.getKey();
                NewProductStockPointVO newProductStockPointVO = productCodeMap.get(productCode);
                if (newProductStockPointVO == null) {
                    continue;
                }
                BigDecimal value = BigDecimal.valueOf(product.getValue());
                if (value.intValue() > 100000) {
                    log.info("{}产品{}的原始需求量{}", day, productCode, value);
                }
                if (value.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                logList.add("工序净需求拆解需求量" + productCode + "#" + day + "#" + value);
                String routingId = routingIdOfProductCodeMap.get(productCode);
                // 该物品有对应工艺路径
                if (StringUtils.isEmpty(routingId)) {
                    continue;
                }
                List<RoutingStepVO> routingStepVOS = routingStepOfProductMap.get(routingId);
                if (CollectionUtils.isEmpty(routingStepVOS)) {
                    continue;
                }
                routingStepVOS = routingStepVOS.stream().filter(t -> YesOrNoEnum.YES.getCode().equals(t.getEnabled())).sorted(Comparator.comparing(RoutingStepVO::getSequenceNo).reversed()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(routingStepVOS)) {
                    continue;
                }
                RoutingStepVO finalStep = routingStepVOS.get(0);//最后一道工序
                List<String> stepIds = routingStepVOS.stream().map(RoutingStepVO::getId).collect(Collectors.toList());
                routingStepIds.addAll(stepIds);
                // 根据每一个步骤的成品率反推每一个步骤的净需求量
                for (RoutingStepVO routingStepVO : routingStepVOS) {

                    // 收集工艺路径步骤id
                    routingStepIdMap.put(productCode + "&" + routingStepVO.getSequenceNo(), routingStepVO.getId());

                    Integer sequenceNo = routingStepVO.getSequenceNo();
                    BigDecimal divide = value;
                    // 减掉工序在制
                    String inventoryKey = String.join("-", productCode, sequenceNo + "");
                    if (operationInventoryMap.containsKey(inventoryKey) && operationInventoryMap.get(inventoryKey) > 0) {
                        logList.add("工序在制" + productCode + "#" + day + "#" + sequenceNo + "#" + operationInventoryMap.get(inventoryKey));
                        if (BigDecimal.valueOf(operationInventoryMap.get(inventoryKey)).compareTo(divide) < 0) {
                            divide = divide.subtract(BigDecimal.valueOf(operationInventoryMap.get(inventoryKey)));
                            operationInventoryMap.put(inventoryKey, 0);
                        } else {
                            operationInventoryMap.put(inventoryKey, operationInventoryMap.get(inventoryKey) - divide.intValue());
                            divide = BigDecimal.ZERO;
                        }
                    }
                    if (routingStepVO.getId().equals(finalStep.getId())) {
                        String s1InventoryKey = String.join("-", productCode, "null");
                        if (operationInventoryMap.containsKey(s1InventoryKey) && operationInventoryMap.get(s1InventoryKey) > 0) {
                            logList.add("生产组织-末工序在制" + productCode + "#" + day + "#" + sequenceNo + "#" + operationInventoryMap.get(s1InventoryKey));
                            if (BigDecimal.valueOf(operationInventoryMap.get(s1InventoryKey)).compareTo(divide) < 0) {
                                divide = divide.subtract(BigDecimal.valueOf(operationInventoryMap.get(s1InventoryKey)));
                                operationInventoryMap.put(s1InventoryKey, 0);
                            } else {
                                operationInventoryMap.put(s1InventoryKey, operationInventoryMap.get(s1InventoryKey) - divide.intValue());
                                divide = BigDecimal.ZERO;
                            }
                        }
                    }
                    StandardStepVO standardStepVO = standardStepMap.get(routingStepVO.getStandardStepId());
                    BigDecimal yield = standardStepVO.getYield();
                    if (yield == null || yield.compareTo(BigDecimal.ZERO) == 0) {
                        // log.info(productCode + "工艺路径步骤" + sequenceNo + "的良品率为null或0");
                        yield = new BigDecimal("0.98");
                    }
                    divide = divide.divide(yield, 0, RoundingMode.UP);
                    value = divide;
                    logList.add("良品率计算" + productCode + "#" + day + "#" + sequenceNo + "#" + "#" + value);
                    String key = day + "#" + productCode + "-" + sequenceNo;
                    primitiveOperationQty.put(key, divide.intValue());
                    // 减掉被锁定的数量
                    // if (lockQtyData.containsKey(key)) {
                    //     BigDecimal lockQty = lockQtyData.get(key);
                    //     // 如果锁定数量已经比新的需求量大了，需要更新锁定的供应关系
                    //     if (lockQty.compareTo(divide) > 0) {
                    //         List<CapacitySupplyRelationshipVO> lockSupplyShipList = lockDataMap.get(key);
                    //         BigDecimal subtract = lockQty.subtract(divide);
                    //         lockSupplyShipList.sort(Comparator.comparing(CapacitySupplyRelationshipVO::getSupplyTime).reversed());
                    //         for (CapacitySupplyRelationshipVO lockSupplyShip : lockSupplyShipList) {
                    //             if (subtract.compareTo(lockSupplyShip.getSupplyQuantity()) > 0) {
                    //                 subtract = subtract.subtract(lockSupplyShip.getSupplyQuantity());
                    //                 lockSupplyShip.setSupplyQuantity(BigDecimal.ZERO);
                    //             } else {
                    //                 lockSupplyShip.setSupplyQuantity(lockSupplyShip.getSupplyQuantity().subtract(subtract));
                    //                 subtract = BigDecimal.ZERO;
                    //             }
                    //             if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                    //                 break;
                    //             }
                    //         }
                    //         BigDecimal finalDivide = divide;
                    //         lockSupplyShipList.forEach(t->t.setDemandQuantity(finalDivide));
                    //         divide = BigDecimal.ZERO;
                    //     } else {
                    //         divide = divide.subtract(lockQtyData.get(key));
                    //     }
                    // }
                    int outQty = 0;// 需求委外和工序委外总和
                    // 当天存在工序委外
                    BigDecimal demandValue = new BigDecimal(divide.toString());
                    if (demandOutsourcingPercentDays.containsKey(productCode) && demandOutsourcingPercentDays.get(productCode).contains(day)) {
                        divide = BigDecimal.ZERO;
                        // 委外数量
                        outQty = demandValue.intValue();
                    }
                    demandValue = new BigDecimal(divide.toString());
                    String processKey = productCode + "-" + sequenceNo;
                    if (processOutsourcingPercentDays.containsKey(processKey) && processOutsourcingPercentDays.get(processKey).contains(day)) {
                        divide = BigDecimal.ZERO;
                        // 委外数量
                        outQty = demandValue.intValue();
                    }
                    // 将该月工序委外和需求委外数量合并成一条委外数据
                    if (outQty > 0) {
                        // 生成委外供应关系
                        CapacitySupplyRelationshipVO relationshipVO = new CapacitySupplyRelationshipVO();
                        relationshipVO.setProductCode(productCode);
                        relationshipVO.setProductName(newProductStockPointVO.getProductName());
                        relationshipVO.setProductType(newProductStockPointVO.getProductType());
                        relationshipVO.setVehicleModelCode(newProductStockPointVO.getVehicleModelCode());
                        relationshipVO.setForecastTime(DateUtils.stringToDate(day, "yyyy-MM-dd"));
                        relationshipVO.setDemandQuantity(BigDecimal.valueOf(primitiveOperationQty.get(key)));
                        relationshipVO.setSupplyQuantity(BigDecimal.valueOf(outQty));
                        relationshipVO.setSupplyTime(DateUtils.stringToDate(day, "yyyy-MM-dd"));
                        relationshipVO.setLockStatus(LockStatusEnum.UNLOCKED.getCode());
                        relationshipVO.setSupplyModel(SupplyModelEnum.OUTSOURCED.getCode());
                        relationshipVO.setOperationCode(sequenceNo.toString());
                        relationshipVO.setOperationName(routingStepVO.getStandardStepName());
                        relationshipVO.setRule(CapacityBalanceRule.highestPriority.getCode());
                        outDataList.add(relationshipVO);
                    }
                    operationQty.put(key, divide.intValue() + operationQty.getOrDefault(key, 0));
                    // 考虑提前期
                    if (weekFlag) {
                        Date date = DateUtils.stringToDate(day, "yyyy-MM-dd");
                        String leadKey = productCode + "-" + sequenceNo;
                        // 该产品工序有提前期
                        if (productLeadTimeMap.containsKey(leadKey)) {
                            BigDecimal leadTime = productLeadTimeMap.get(leadKey);
                            DateTime offsetSecond = DateUtil.offsetSecond(date, -leadTime.intValue());
                            String newDay = DateUtils.dateToString(offsetSecond, "yyyy-MM-dd");
                            // 提前到的日期最早不超过当前日期
                            if (offsetSecond.before(now)) {
                                newDay = deliveryPlanStart;
                            }
                            String newQtyKey = newDay + "#" + productCode + "-" + sequenceNo;
                            if (newQtyKey.equals(key)) {
                                continue;
                            }
                            if (operationQty.containsKey(newQtyKey)) {
                                operationQty.put(newQtyKey, operationQty.get(newQtyKey) + divide.intValue());
                                primitiveOperationQty.put(newQtyKey, primitiveOperationQty.get(newQtyKey) + divide.intValue());
                            } else {
                                operationQty.put(newQtyKey, divide.intValue());
                                primitiveOperationQty.put(newQtyKey, divide.intValue());
                            }
                            operationQty.put(key, operationQty.getOrDefault(key, 0) - divide.intValue());
                            primitiveOperationQty.put(key, primitiveOperationQty.getOrDefault(key, 0) - divide.intValue());
                        }
                    }
                }
            }
        }
    }

    protected Map<String, StandardStepVO> getStandardStepMap(String scenario) {
        return newMdsFeign.selectStandardStepAll(scenario).stream()
                .collect(Collectors.toMap(StandardStepVO::getId, Function.identity()));
    }

    /**
     * 递归查询工艺路径
     */
    protected void getRoutingNew(String scenario,
                                 List<String> prouctCodeList,
                                 List<String> inputProductIdList,
                                 Map<String, NewProductStockPointVO> productCodeMap,
                                 Map<String, String> inputProductIdMap,
                                 List<String> allInputProductIdList,
                                 Map<String, String> routingIdOfProductCodeMap,
                                 Map<String, List<RoutingStepVO>> routingStepOfProductMap,
                                 Map<String, List<String>> sourceProductCodeMap,
                                 List<CapacitySupplyRelationshipExceptionDTO> exceptionDTOList) {
        List<NewProductStockPointVO> newProductStockPointVOS;
        boolean flag = false;
        if (CollectionUtils.isNotEmpty(inputProductIdList)) {
            newProductStockPointVOS = newMdsFeign.selectProductStockPointByIds(scenario, inputProductIdList);
            flag = true;
        } else {
            newProductStockPointVOS = newMdsFeign.selectByProductCode(scenario, prouctCodeList);
            List<String> collect = newProductStockPointVOS.stream().map(NewProductStockPointVO::getProductCode).collect(Collectors.toList());
            List<String> missing = prouctCodeList.stream().filter(t -> !collect.contains(t)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(missing)) {
                log.info("未找到产品：{}", JSON.toJSONString(missing));
            }
            for (String productCode : missing) {
                CapacitySupplyRelationshipExceptionDTO exceptionDTO = new CapacitySupplyRelationshipExceptionDTO();
                exceptionDTO.setProductCode(productCode);
                exceptionDTO.setExceptionReason("在物料主数据中未找到对应本厂编码");
                exceptionDTOList.add(exceptionDTO);
            }
        }
        // 过滤非采购件
        newProductStockPointVOS = newProductStockPointVOS.stream().filter(t -> LIMIT_PRODUCT_TYPE_LIST.contains(t.getProductType())
                && !LIMIT_PRODUCT_CLASSIFY.equals(t.getProductClassify())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(newProductStockPointVOS)) {
            List<String> productIds = newProductStockPointVOS.stream().map(NewProductStockPointVO::getId).collect(Collectors.toList());
            if (flag) {
                List<String> newProductCodes = newProductStockPointVOS.stream().map(NewProductStockPointVO::getProductCode).collect(Collectors.toList());
                prouctCodeList.addAll(newProductCodes);
                inputProductIdMap.putAll(newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getId, NewProductStockPointVO::getProductCode)));
            }
            allInputProductIdList.addAll(productIds);
            Map<String, NewProductStockPointVO> pointVOMap =
                    newProductStockPointVOS.stream().collect(Collectors.toMap(t -> t.getStockPointCode() + "-" + t.getProductCode(), Function.identity(), (v1, v2) -> v1));
            List<RoutingVO> routingByProductIdList = newMdsFeign.getRoutingByProductIdList(scenario, productIds).stream().filter(t -> YesOrNoEnum.YES.getCode().equals(t.getEnabled())).collect(Collectors.toList());
            Map<String, List<RoutingVO>> routingMap = routingByProductIdList.stream().collect(Collectors.groupingBy(RoutingBasicVO::getProductCode));
            Map<String, NewProductStockPointVO> stockPointVOMap = newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (v1, v2) -> v2));
            for (NewProductStockPointVO productStockPointVO : stockPointVOMap.values()) {
                String productCode = productStockPointVO.getProductCode();
                if (!routingMap.containsKey(productCode)) {
                    CapacitySupplyRelationshipExceptionDTO exceptionDTO = new CapacitySupplyRelationshipExceptionDTO();
                    exceptionDTO.setVehicleModelCode(productStockPointVO.getVehicleModelCode());
                    exceptionDTO.setProductCode(productCode);
                    exceptionDTO.setProductName(productStockPointVO.getProductName());
                    exceptionDTO.setExceptionReason("未找到工艺路径");
                    exceptionDTOList.add(exceptionDTO);
                } else if (routingMap.get(productCode).size() > 1) {
                    CapacitySupplyRelationshipExceptionDTO exceptionDTO = new CapacitySupplyRelationshipExceptionDTO();
                    exceptionDTO.setVehicleModelCode(productStockPointVO.getVehicleModelCode());
                    exceptionDTO.setProductCode(productCode);
                    exceptionDTO.setProductName(productStockPointVO.getProductName());
                    exceptionDTO.setExceptionReason("找到多条工艺路径");
                    exceptionDTOList.add(exceptionDTO);
                    // 防止下面转换map报错
                    routingByProductIdList.removeAll(routingMap.get(productCode));
                }
            }
            if (CollectionUtils.isNotEmpty(routingByProductIdList)) {
                List<String> routingIds = routingByProductIdList.stream().map(RoutingVO::getId).collect(Collectors.toList());
                routingIdOfProductCodeMap.putAll(routingByProductIdList.stream().collect(Collectors.toMap(RoutingVO::getProductCode, RoutingVO::getId)));
                for (RoutingVO routingVO : routingByProductIdList) {
                    String productKey = routingVO.getStockPointCode() + "-" + routingVO.getProductCode();
                    if (pointVOMap.containsKey(productKey)) {
                        productCodeMap.put(routingVO.getProductCode(), pointVOMap.get(productKey));
                    } else {
                        log.info("未找到产品：" + productKey);
                    }
                }

                List<RoutingStepVO> routingStepByRoutingIds = newMdsFeign.getRoutingStepByRoutingIds(scenario, routingIds);
                if (CollectionUtils.isNotEmpty(routingStepByRoutingIds)) {
                    routingStepOfProductMap.putAll(routingStepByRoutingIds.stream().filter(t -> YesOrNoEnum.YES.getCode().equals(t.getEnabled()))
                            .collect(Collectors.groupingBy(RoutingStepVO::getRoutingId)));

                    List<String> allStepIds = routingStepByRoutingIds.stream().map(RoutingStepVO::getId).collect(Collectors.toList());
                    Map<String, Object> params = new HashMap<>();
                    params.put("routingStepIds", allStepIds);
                    params.put("productType", ProductTypeEnum.SA);
                    List<BomRoutingStepInputVO> bomRoutingStepInputVOS = newMdsFeign.selectBomRoutingStepInputByParams(scenario, params);
                    if (CollectionUtils.isNotEmpty(bomRoutingStepInputVOS)) {
                        sourceProductCodeMap.putAll(bomRoutingStepInputVOS.stream()
                                .collect(Collectors.groupingBy(BomRoutingStepInputBasicVO::getInputProductId,
                                        Collectors.mapping(BomRoutingStepInputVO::getSourceProductCode, Collectors.toList()))));
                        List<String> inputIds = bomRoutingStepInputVOS.stream().map(BomRoutingStepInputVO::getInputProductId).distinct().collect(Collectors.toList());
                        // 递归查询
                        this.getRoutingNew(scenario, prouctCodeList, inputIds, productCodeMap, inputProductIdMap,
                                allInputProductIdList, routingIdOfProductCodeMap, routingStepOfProductMap,
                                sourceProductCodeMap, exceptionDTOList);
                    }
                }
            }
        }
    }

    /**
     * 组装每个产品的工序
     */
    protected Map<String, RoutingStepVO> getOperationOfProductMap(List<String> prouctCodeList,
                                                                  Map<String, String> routingIdOfProductCodeMap,
                                                                  Map<String, List<RoutingStepVO>> routingStepOfProductMap,
                                                                  List<String> routingStepIds,
                                                                  Map<String, Set<String>> operationOfProductMap) {
        Map<String, RoutingStepVO> routingStepMap = new HashMap<>();
        for (String productCode : prouctCodeList) {
            String routingId = routingIdOfProductCodeMap.get(productCode);
            // 该物品有对应工艺路径
            if (StringUtils.isNotEmpty(routingId)) {
                List<RoutingStepVO> routingStepVOS = routingStepOfProductMap.get(routingId);
                if (CollectionUtils.isNotEmpty(routingStepVOS)) {
                    routingStepVOS = routingStepVOS.stream().sorted(Comparator.comparing(RoutingStepVO::getSequenceNo).reversed()).collect(Collectors.toList());
                    List<String> stepIds = routingStepVOS.stream().map(RoutingStepVO::getId).collect(Collectors.toList());
                    routingStepIds.addAll(stepIds);
                    Map<Integer, List<RoutingStepVO>> collect = routingStepVOS.stream().collect(Collectors.groupingBy(RoutingStepBasicVO::getSequenceNo));
                    for (Map.Entry<Integer, List<RoutingStepVO>> entry : collect.entrySet()) {
                        List<RoutingStepVO> stepVOS = entry.getValue();
                        if (stepVOS.size() > 1) {
                            System.out.println(111);
                        }
                    }
                    routingStepMap.putAll(routingStepVOS.stream().collect(Collectors.toMap(t -> productCode + "-" + t.getSequenceNo(), Function.identity())));
                    for (RoutingStepVO routingStepVO : routingStepVOS) {
                        Integer sequenceNo = routingStepVO.getSequenceNo();
                        if (CollectionUtils.isNotEmpty(operationOfProductMap.get(productCode))) {
                            operationOfProductMap.get(productCode).add(sequenceNo.toString());
                        } else {
                            Set<String> strings = new HashSet<>();
                            strings.add(sequenceNo.toString());
                            operationOfProductMap.put(productCode, strings);
                        }
                    }
                }
            }
        }
        return routingStepMap;
    }

    /**
     * 获取工艺路径步骤候选资源
     */
    protected List<String> getRoutingStepResource(List<String> routingStepIds, String mdsScenario, CapacityBalanceAlgorithmDataDTO algorithmDataDTO) {
        // 根据工序步骤id获取候选资源
        routingStepIds = routingStepIds.stream().distinct().collect(Collectors.toList());
        List<RoutingStepResourceVO> routingStepResourceVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(routingStepIds)) {
            routingStepResourceVOS = newMdsFeign.selectRoutingStepResourceByRoutingStepIds(mdsScenario, routingStepIds);
            routingStepResourceVOS = routingStepResourceVOS.stream()
                    .filter(t -> YesOrNoEnum.YES.getCode().equals(t.getEnabled()))
                    .collect(Collectors.toList());
        }

        List<String> routingStepResourceIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(routingStepResourceVOS)) {
            routingStepResourceIds = routingStepResourceVOS.stream().map(RoutingStepResourceBasicVO::getId).collect(Collectors.toList());
            algorithmDataDTO.setRoutingStepResourceVOList(routingStepResourceVOS);
        }
        return routingStepResourceIds;
    }

    /**
     * 按月生成，只生成发货计划开始3个月的
     */
    protected void getCandidateResourceTimeNew(CapacityBalanceAlgorithmDataDTO algorithmDataDTO, String mdsScenario, List<String> logList) {
        Map<String, List<ProductCandidateResourceTimeVO>> productAndOperationOfResourceMap = new HashMap<>();
        Map<String, ProductCandidateResourceTimeVO> resourceOperationMap = new HashMap<>();
        List<ProductCandidateResourceTimeVO> addResourceTimeList = new ArrayList<>();
        List<String> errorStr = new ArrayList<>();
        // 根据候选资源id查询它每个月的优先级
        if (CollectionUtils.isNotEmpty(algorithmDataDTO.getRoutingStepResourceIds())) {
            List<ProductCandidateResourceTimeVO> productCandidateResourceTimeVOS = newMdsFeign.selectProductCandidateResourceTimeByCandidateResourceId(mdsScenario, algorithmDataDTO.getRoutingStepResourceIds());
            Map<String, List<RoutingStepResourceVO>> routingStepResourceMap = algorithmDataDTO.getRoutingStepResourceVOList().stream().collect(Collectors.groupingBy(t -> String.join("-", t.getRoutingCode(), t.getRoutingStepCode())));
            // 补充某些月份没有优先级
            if (CollectionUtils.isNotEmpty(algorithmDataDTO.getMonthList())) {
                Map<String, List<ProductCandidateResourceTimeVO>> productCandidateResourceTimeOfMonthMap = productCandidateResourceTimeVOS.stream()
                        .filter(t -> YesOrNoEnum.YES.getCode().equals(t.getEnabled()))
                        .collect(Collectors.groupingBy(t -> String.join("-", t.getProductCode(), t.getOperationCode())));
                List<String> productCodeList = algorithmDataDTO.getProductCodeList();
                Map<String, Set<String>> operationOfProductMap = algorithmDataDTO.getOperationOfProductMap();
                for (String product : productCodeList) {
                    Set<String> operationCodeList = operationOfProductMap.getOrDefault(product, new HashSet<>());
                    if (CollectionUtils.isEmpty(operationCodeList)) {
                        log.info("没有{}产品对应的工序", product);
                        logList.add("组装产品资源生产关系-没有" + product + "产品对应的工序");
                        continue;
                    }
                    for (String operationCode : operationCodeList) {
                        String key = product + "-" + operationCode;
                        if (!productCandidateResourceTimeOfMonthMap.containsKey(key)) {
                            // 该月产品资源生产关系为空，则用工艺路径候选资源补充
                            List<ProductCandidateResourceTimeVO> candidateResourceTimeVOS = handResource(key, routingStepResourceMap, errorStr);
                            addResourceTimeList.addAll(candidateResourceTimeVOS);
                            productCandidateResourceTimeVOS.addAll(candidateResourceTimeVOS);
                        }
                    }

                }
            }
            // 按工序分组，构建工序
            productAndOperationOfResourceMap = productCandidateResourceTimeVOS.stream().collect(Collectors.groupingBy(t -> t.getProductCode() + "-" + t.getOperationCode()));
            resourceOperationMap = productCandidateResourceTimeVOS.stream().collect(Collectors.toMap(ProductCandidateResourceTimeVO::getResourceCode, Function.identity(), (v1, v2) -> v1));
        }
        if (CollectionUtils.isNotEmpty(errorStr)) {
            // throw new BusinessException("获取数据失败："+String.join(";", errorStr));
            log.error("获取数据失败：{}", String.join(";", errorStr));
        }
        addResourceTimeList.forEach(t -> t.setId(null));
        algorithmDataDTO.setProductAndOperationOfResourceMap(productAndOperationOfResourceMap);
        algorithmDataDTO.setResourceOperationMap(resourceOperationMap);
        algorithmDataDTO.setAddResourceTimeList(addResourceTimeList);

    }

    private List<ProductCandidateResourceTimeVO> handResource(String product, Map<String, List<RoutingStepResourceVO>> routingStepResourceMap, List<String> errorStr) {
        List<ProductCandidateResourceTimeVO> timeVOList = new ArrayList<>();
        if (routingStepResourceMap.containsKey(product)) {
            List<RoutingStepResourceVO> routingStepResourceVOS = routingStepResourceMap.get(product);

            routingStepResourceVOS.forEach(t -> {
                ProductCandidateResourceTimeVO timeVO = new ProductCandidateResourceTimeVO();
                timeVO.setCandidateResourceId(t.getId());
                timeVO.setProductCode(t.getRoutingCode());
                timeVO.setOperationCode(t.getRoutingStepCode());
                timeVO.setOperationName(t.getStandardStepName());
                timeVO.setResourceCode(t.getPhysicalResourceCode());
                timeVO.setResourceName(t.getPhysicalResourceName());
                timeVO.setBeat(t.getUnitProductionTime().doubleValue());
                timeVO.setEnabled(YesOrNoEnum.YES.getCode());
                timeVO.setPriority(t.getPriority() == null ? 999 : t.getPriority());
                timeVOList.add(timeVO);
            });
        } else {
            errorStr.add(product + "产品资源生产关系或工序候选资源不存在");
        }
        return timeVOList;
    }

    /**
     * 获取规则
     */
    protected void getRule(CapacityBalanceAlgorithmDataDTO algorithmDataDTO) {
        List<String> rule = new ArrayList<>();
        List<ParamCapacityBalanceVO> paramCapacityBalanceVOS = paramCapacityBalanceService.selectAll();
        if (CollectionUtils.isNotEmpty(paramCapacityBalanceVOS)) {
            String rules = paramCapacityBalanceVOS.get(0).getRule();

            String[] split = rules.split("-");
            rule = new ArrayList<>(Arrays.asList(split));
        }
        algorithmDataDTO.setRule(rule);
    }

    /**
     * 获取零件风险等级
     */
    protected void getMaterialRiskLevel(String dfpScenario, List<String> prouctCodeList, CapacityBalanceAlgorithmDataDTO algorithmDataDTO) {
        Map<String, PartRiskLevelVO> materialRiskLevelMap = new HashMap<>();
        List<PartRiskLevelVO> partRiskLevelVOS = dfpFeign.selectMaterialRiskLeveByProductCodeList(dfpScenario, prouctCodeList);
        if (CollectionUtils.isNotEmpty(partRiskLevelVOS)) {
            materialRiskLevelMap = partRiskLevelVOS.stream().collect(Collectors.toMap(t -> t.getOemCode() + "-" + t.getProductCode(), Function.identity()));
        }
        algorithmDataDTO.setMaterialRiskLevelMap(materialRiskLevelMap);
    }

    /**
     * 获取主机厂信息
     */
    protected void getOemInfo(List<String> oemCodeList, String scenario2, CapacityBalanceAlgorithmDataDTO algorithmDataDTO) {
        Map<String, Object> params = new HashMap<>();
        params.put("oemCodes", oemCodeList);
        Map<String, OemVO> oemVOMap = new HashMap<>();
        List<OemVO> oemVOList = dfpFeign.selectOemByParams(scenario2, params);
        List<String> safetyProductCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(oemVOList)) {
            oemVOMap = oemVOList.stream().collect(Collectors.toMap(OemVO::getOemCode, Function.identity()));
            // 不计算安全库存水位的本厂编码

            for (Map.Entry<String, List<String>> entry : algorithmDataDTO.getProductOfOemMap().entrySet()) {
                String productCode = entry.getKey();
                List<String> productOfOemCodeList = entry.getValue();
                List<OemVO> mtsOemList = oemVOList.stream().filter(t -> productOfOemCodeList.contains(t.getOemCode()) &&
                                OemBusinessTypeEnum.MTS.getCode().equals(t.getBusinessType()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(mtsOemList)) {
                    safetyProductCodeList.add(productCode);
                }
            }
        }
        algorithmDataDTO.setOemVOMap(oemVOMap);
        algorithmDataDTO.setSafetyProductCodeList(safetyProductCodeList);
    }

    /**
     * 获取主机厂运输时间
     */
    protected void getOemTransportTime(List<String> oemCodeList, CapacityBalanceAlgorithmDataDTO algorithmDataDTO) {
        List<OemTransportTimeVO> oemTransportTimeVOS = capacityBalanceDao.selectOemTransportTime(oemCodeList);
        Map<String, OemTransportTimeVO> oemTransportTimeMap = oemTransportTimeVOS
                .stream().collect(Collectors.toMap(OemTransportTimeVO::getOemCode, Function.identity(),
                        BinaryOperator.maxBy(
                                Comparator.comparing(
                                        OemTransportTimeVO::getPriority,
                                        Comparator.nullsLast(Comparator.naturalOrder())
                                ))));
        algorithmDataDTO.setOemTransportTimeVOMap(oemTransportTimeMap);
    }


    protected  String getRangeData(String scenario) {
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(scenario, "SUB_INVENTORY", "INTERNAL", null);
        return scenarioBusinessRange.getData().getRangeData();
    }


    /**
     * 锁定数据处理
     * @param capacitySupplyRelationshipVOS
     * @param algorithmDataDTO
     * @param qty
     * @return
     */
    protected static Map<String, BigDecimal> getLockQtyData(List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOS,
                                                            CapacityBalanceAlgorithmDataDTO algorithmDataDTO,
                                                            Map<String,Map<String,Integer>> qty,
                                                            String pattern){
        Map<String, BigDecimal> lockQtyData = new HashMap<>();
        Map<String, List<CapacitySupplyRelationshipVO>> lockData = new HashMap<>();
        Map<String, List<CapacitySupplyRelationshipVO>> lockDataMap = new HashMap<>();
        for (CapacitySupplyRelationshipVO capacitySupplyRelationshipVO : capacitySupplyRelationshipVOS) {
            String month;
            if (SupplyModelEnum.OUTSOURCED.getCode().equals(capacitySupplyRelationshipVO.getSupplyModel())){
                month = DateUtils.dateToString(capacitySupplyRelationshipVO.getForecastTime(), pattern);
            }else {
                month = DateUtils.dateToString(capacitySupplyRelationshipVO.getSupplyTime(), pattern);
            }
            String demandMonth = DateUtils.dateToString(capacitySupplyRelationshipVO.getForecastTime(), pattern);
            String key = demandMonth+"#"+capacitySupplyRelationshipVO.getProductCode()+"-"+capacitySupplyRelationshipVO.getOperationCode();
/*            if (qty != null && qty.containsKey(demandMonth)){
                //本次计算的当月需求量
                Integer integer = qty.get(demandMonth).get(capacitySupplyRelationshipVO.getProductCode());
                if (integer != null && BigDecimal.valueOf(integer).compareTo(capacitySupplyRelationshipVO.getDemandQuantity()) < 0){
                    capacitySupplyRelationshipVO.setDemandQuantity(BigDecimal.valueOf(integer));
                    if (BigDecimal.valueOf(integer).compareTo(capacitySupplyRelationshipVO.getSupplyQuantity()) < 0){
                        capacitySupplyRelationshipVO.setSupplyQuantity(BigDecimal.valueOf(integer));
                    }
                }
            }*/
            if (lockQtyData.containsKey(key)){
                lockQtyData.put(key, lockQtyData.get(key).add(capacitySupplyRelationshipVO.getSupplyQuantity()));
            }else {
                lockQtyData.put(key, capacitySupplyRelationshipVO.getSupplyQuantity());
            }
            if (lockDataMap.containsKey(key)){
                lockDataMap.get(key).add(capacitySupplyRelationshipVO);
            }else {
                List<CapacitySupplyRelationshipVO> vos = new ArrayList<>();
                vos.add(capacitySupplyRelationshipVO);
                lockDataMap.put(key, vos);
            }
            if (!lockData.containsKey(month)){
                List<CapacitySupplyRelationshipVO> vos = new ArrayList<>();
                vos.add(capacitySupplyRelationshipVO);
                lockData.put(month, vos);
            }else {
                lockData.get(month).add(capacitySupplyRelationshipVO);
            }
        }
        algorithmDataDTO.setLockData(lockData);
        algorithmDataDTO.setLockDataMap(lockDataMap);
        return lockQtyData;
    }


    /**
     * 需要进行的需求量扣减锁定数量
     * @param lockQtyMap
     * @param algorithmDataDTO
     */
    protected void deductionLockData(Map<String, BigDecimal> lockQtyMap, CapacityBalanceAlgorithmDataDTO algorithmDataDTO) {
        if (lockQtyMap == null || algorithmDataDTO == null || algorithmDataDTO.getOperationQty() == null) {
            return;
        }
        Map<String, Integer> operationQty = algorithmDataDTO.getOperationQty();
        Map<String, Integer> primitiveOperationQty = algorithmDataDTO.getPrimitiveOperationQty();
        Map<String, List<CapacitySupplyRelationshipVO>> lockSupplyDataMap = algorithmDataDTO.getLockDataMap();
        for (Map.Entry<String, BigDecimal> entry : lockQtyMap.entrySet()) {
            String key = entry.getKey();
            int lockQty = entry.getValue().intValue();
            if (operationQty.containsKey(key)) {
                // 需要进行产能平衡计算
                Integer demandQty = operationQty.get(key);
                if (demandQty > lockQty) {
                    operationQty.put(key, demandQty - lockQty);
                } else {
                    // 需要减少的供应数量
                    BigDecimal subtract = new BigDecimal(lockQty).subtract(new BigDecimal(demandQty));
                    List<CapacitySupplyRelationshipVO> lockSupplyShipList = lockSupplyDataMap.get(key);
                    Integer primitiveDemandQty = primitiveOperationQty.get(key);
                    // 更新锁定的供应关系的需求量
                    for (CapacitySupplyRelationshipVO lockSupplyShip : lockSupplyShipList) {
                        if (subtract.compareTo(lockSupplyShip.getSupplyQuantity()) > 0) {
                            subtract = subtract.subtract(lockSupplyShip.getSupplyQuantity());
                            lockSupplyShip.setSupplyQuantity(BigDecimal.ZERO);
                        } else {
                            lockSupplyShip.setSupplyQuantity(lockSupplyShip.getSupplyQuantity().subtract(subtract));
                            subtract = BigDecimal.ZERO;
                        }
                        if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                            break;
                        }
                    }
                    lockSupplyShipList.forEach(lockSupplyShip ->
                            lockSupplyShip.setDemandQuantity(new BigDecimal(primitiveDemandQty)));
                    operationQty.put(key, 0);
                }
            }
        }
    }

}
