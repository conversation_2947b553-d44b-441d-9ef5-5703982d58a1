package com.yhl.scp.mps.common.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPlanOrderQuery;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.*;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipDao;
import com.yhl.scp.mps.capacityBalance.service.CapacityBalanceAlgorithmExecuteService;
import com.yhl.scp.mps.capacityBalance.service.CapacityBalanceVersionService;
import com.yhl.scp.mps.capacityBalance.service.CapacitySupplyRelationshipService;
import com.yhl.scp.mps.capacityBalance.vo.CapacityBalanceVersionVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.common.infrastructure.dao.CommonBasicDao;
import com.yhl.scp.mps.demand.service.OutsourceTransferDemandDetailService;
import com.yhl.scp.mps.demand.vo.OutsourceTransferDemandDetailVO;
import com.yhl.scp.mps.domain.dispatch.IAmsSchedule;
import com.yhl.scp.mps.feedback.service.MesFeedbackService;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.fixtureRelation.service.ProductFixtureRelationService;
import com.yhl.scp.mps.fixtureRelation.vo.ProductFixtureRelationVO;
import com.yhl.scp.mps.highValueMaterials.dto.MpsHighValueMaterialsDTO;
import com.yhl.scp.mps.highValueMaterials.service.MpsHighValueMaterialsService;
import com.yhl.scp.mps.highValueMaterials.vo.MpsHighValueMaterialsVO;
import com.yhl.scp.mps.model.service.MoldChangeTimeService;
import com.yhl.scp.mps.nakedGlass.dto.NakedGlassDTO;
import com.yhl.scp.mps.nakedGlass.service.NakedGlassService;
import com.yhl.scp.mps.nakedGlass.vo.NakedGlassVO;
import com.yhl.scp.mps.operationPublished.service.DemandPublishedService;
import com.yhl.scp.mps.operationPublished.service.OperationInputPublishedService;
import com.yhl.scp.mps.operationPublished.service.OperationPublishedService;
import com.yhl.scp.mps.operationPublished.service.WorkOrderPublishedService;
import com.yhl.scp.mps.operationPublished.vo.DemandPublishedVO;
import com.yhl.scp.mps.operationPublished.vo.OperationInputPublishedVO;
import com.yhl.scp.mps.operationPublished.vo.OperationPublishedVO;
import com.yhl.scp.mps.operationPublished.vo.WorkOrderPublishedVO;
import com.yhl.scp.mps.plan.dto.MasterPlanRelationLogDTO;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import com.yhl.scp.mps.plan.service.*;
import com.yhl.scp.mps.plan.vo.*;
import com.yhl.scp.mps.product.dto.ChainLineInventoryLogDTO;
import com.yhl.scp.mps.product.service.ChainLineInventoryLogService;
import com.yhl.scp.mps.product.service.ProductAdvanceBatchRuleService;
import com.yhl.scp.mps.product.vo.ProductAdvanceBatchRuleVO;
import com.yhl.scp.mps.reportingFeedback.service.MpsProReportingFeedbackService;
import com.yhl.scp.mps.resource.service.ResourceService;
import com.yhl.scp.mps.rule.dto.AlgorithmConstraintRuleDTO;
import com.yhl.scp.mps.rule.enums.AlgorithmConstraintRuleEnum;
import com.yhl.scp.mps.rule.service.AlgorithmConstraintRuleService;
import com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.dao.SubInventoryCargoLocationDao;
import com.yhl.scp.mps.subInventoryCargoLocation.service.SubInventoryCargoLocationService;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import com.yhl.scp.sds.extension.feedback.vo.FeedbackProductionVO;
import com.yhl.scp.sds.extension.order.vo.OperationInputVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.extension.pegging.dto.DemandDTO;
import com.yhl.scp.sds.extension.pegging.dto.FulfillmentDTO;
import com.yhl.scp.sds.extension.pegging.dto.SupplyDTO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.extension.pegging.vo.FulfillmentVO;
import com.yhl.scp.sds.extension.pegging.vo.SupplyVO;
import com.yhl.scp.sds.feedback.service.FeedbackProductionService;
import com.yhl.scp.sds.order.convertor.OperationConvertor;
import com.yhl.scp.sds.order.convertor.WorkOrderConvertor;
import com.yhl.scp.sds.order.service.OperationInputService;
import com.yhl.scp.sds.order.service.OperationService;
import com.yhl.scp.sds.order.service.WorkOrderService;
import com.yhl.scp.sds.pegging.service.DemandService;
import com.yhl.scp.sds.pegging.service.FulfillmentService;
import com.yhl.scp.sds.pegging.service.SupplyService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>DfpFeignController</code>
 * <p>
 * DfpFeignController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-22 14:40:17
 */
@Slf4j
@Api(tags = "Feign")
@RestController
public class MpsFeignController implements MpsFeign {

    private static final List<String> objectTypeEnums = Arrays.asList(
            "com.yhl.scp.mps.enums.ObjectTypeEnum",
            "com.yhl.scp.mps.basic.enums.ObjectTypeEnum",
            "com.yhl.scp.ams.basic.enums.ObjectTypeEnum");
    @Resource
    protected SubInventoryCargoLocationDao subInventoryCargoLocationDao;
    @Resource
    MasterPlanVersionService masterPlanVersionService;
    @Resource
    MasterPlanIssuedDataService masterPlanIssuedDataService;
    @Resource
    private MpsHighValueMaterialsService highValueMaterialsService;
    @Resource
    private SubInventoryCargoLocationService subInventoryCargoLocationService;
    @Resource
    private ResourceService resourceService;
    @Resource
    private CapacityBalanceVersionService capacityBalanceVersionService;
    @Resource
    private CapacitySupplyRelationshipService capacitySupplyRelationshipService;
    @Resource
    private MpsProReportingFeedbackService mpsProReportingFeedbackService;
    @Resource
    private DemandService demandService;
    @Resource
    private FulfillmentService fulfillmentService;
    @Resource
    private SupplyService supplyService;
    @Resource
    private MoldChangeTimeService moldChangeTimeService;
    @Resource
    private ChainLineInventoryLogService chainLineInventoryLogService;
    @Resource
    private ProductAdvanceBatchRuleService productAdvanceBatchRuleService;
    @Resource
    private NakedGlassService nakedGlassService;
    @Resource
    private MasterPlanService masterPlanService;
    @Resource
    private AlgorithmConstraintRuleService algorithmConstraintRuleService;
    @Resource
    private MasterPlanRelationService masterPlanRelationService;
    @Resource
    private MasterPlanRelationLogService masterPlanRelationLogService;
    @Resource
    private WorkOrderPublishedService workOrderPublishedService;
    @Resource
    private OperationPublishedService operationPublishedService;
    @Resource
    private DemandPublishedService demandPublishedService;
    @Resource
    private CapacityBalanceAlgorithmExecuteService algorithmExecuteService;
    @Resource
    private CapacitySupplyRelationshipDao capacitySupplyRelationshipDao;
    @Resource
    private OperationInputPublishedService operationInputPublishedService;
    @Resource
    private MasterPlanPublishedLogService masterPlanPublishedLogService;
    @Resource
    private OutsourceTransferDemandDetailService outsourceTransferDemandDetailService;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private OperationService operationService;
    @Resource
    private OperationInputService operationInputService;
    @Resource
    private OperationTaskExtDao operationTaskExtDao;
    @Resource
    private MesFeedbackService mesFeedbackService;
    @Resource
    private ProductFixtureRelationService productFixtureRelationService;
    @Resource
    private CommonBasicDao commonBasicDao;
    @Resource
    private FeedbackProductionService feedbackProductionService;
    @Autowired
    private MdsFeign mdsFeign;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private IAmsSchedule amsSchedule;

    /**
     * 从指定的枚举类中收集枚举值到Map中
     *
     * @param enumClass 枚举类
     * @param targetMap 目标Map
     */
    private static void collectEnumsFromClass(Class<?> enumClass, Map<String, String> targetMap) {
        try {
            Object[] enumConstants = enumClass.getEnumConstants();
            for (Object enumConstant : enumConstants) {
                // 使用反射调用getCode和getMappingValue方法
                Method getCodeMethod = enumConstant.getClass().getMethod("getCode");
                Method getMappingValueMethod = enumConstant.getClass().getMethod("getMappingValue");

                String code = (String) getCodeMethod.invoke(enumConstant);
                String mappingValue = (String) getMappingValueMethod.invoke(enumConstant);

                // 避免覆盖已有的值，如果需要覆盖则移除这个检查
                targetMap.put(code, mappingValue);
            }
        } catch (Exception e) {
            // 如果出现异常则跳过该枚举类
        }
    }

    @Override
    public BaseResponse<Void> createNewData(String scenario, List<MpsHighValueMaterialsDTO> mpsHighValueMaterialsDTOs) {
        return highValueMaterialsService.createNewData(scenario, mpsHighValueMaterialsDTOs);
    }

    @Override
    public List<MpsHighValueMaterialsVO> queryByParams(String scenario, Map<String, Object> params) {
        return highValueMaterialsService.selectByParams(params);
    }

    public BaseResponse<Void> syncSubInventoryCargoLocation(String scenario, List<MesSubInventoryCargoLocation> subInventoryCargoLocations) {
        return subInventoryCargoLocationService.syncSubInventoryCargoLocation(scenario, subInventoryCargoLocations);
    }

    @Override
    public List<SubInventoryCargoLocationVO> queryByParams1(String scenario, Map<String, Object> params) {
        return subInventoryCargoLocationService.selectByParams(params);
    }

    @Override
    public List<SubInventoryCargoLocationVO> queryByFreightSpaces(String scenario, List<String> spaceList, String stockPointType) {
        return subInventoryCargoLocationService.selectByBatchCodeAndStockType(spaceList, stockPointType);
    }

    @Override
    public CapacityBalanceVersionVO selectLatestCapacityBalanceVersionCode(String scenario) {
        return capacityBalanceVersionService.selectLatestVersionCode();
    }

    @Override
    public List<CapacitySupplyRelationshipVO> selectCapacitySupplyRelationshipByVersionCode(String scenario, String versionCode) {
        return capacitySupplyRelationshipService.selectByParams(ImmutableMap.of("versionId", versionCode));
    }

    @Override
    public List<MasterPlanIssuedDataVO> getMasterPlanIssuedDataByVersionId(String masterPlanVersionId) {
        return masterPlanIssuedDataService.selectByParams(ImmutableMap.of("masterPlanVersionId", masterPlanVersionId));
    }

    @Override
    public MasterPlanVersionVO getLatestPublishedMpsVersion() {
        return masterPlanVersionService.getLatestPublishedMpsVersion();
    }

    @Override
    public MasterPlanVersionVO getLatestPublishedMpsVersionPublished() {
        return masterPlanVersionService.getLatestPublishedMpsVersionPublished();
    }

    @Override
    public BaseResponse<Void> doSyncReportFeedback(String scenario, List<MesReportFeedback> feedbacks) {
        return mpsProReportingFeedbackService.handleReportingFeedback(scenario, feedbacks);
    }

    @Override
    public List<DemandVO> getDemandListByParams(String scenario, Map<String, Object> params) {
        return demandService.selectByParams(params);
    }

    @Override
    public BaseResponse<Void> addDemand(List<DemandDTO> demandDTOList) {
        demandService.doCreateBatch(demandDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> updateDemand(String scenario, List<DemandDTO> demandDTOList) {
        demandService.doUpdateBatch(demandDTOList, false);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public List<FulfillmentVO> getFulfillmentListByParams(String scenario, Map<String, Object> params) {
        return fulfillmentService.selectByParams(params);
    }

    @Override
    public BaseResponse<Void> addFulfillment(List<FulfillmentDTO> fulfillmentDTOList) {
        fulfillmentService.doCreateBatch(fulfillmentDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> updateFulfillment(String scenario, List<FulfillmentDTO> fulfillmentDTOList) {
        fulfillmentService.doUpdateBatch(fulfillmentDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> deleteFulfillmentBySupplyId(List<String> idList) {
        fulfillmentService.doDeleteBySupplyIds(idList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> deleteFulfillmentByDemandId(List<String> idList) {
        fulfillmentService.doDeleteByDemandIds(idList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public List<SupplyVO> getSupplyListByParams(String scenario, Map<String, Object> params) {
        return supplyService.selectByParams(params);
    }

    @Override
    public BaseResponse<Void> addSupply(String scenario, List<SupplyDTO> supplyDTOList) {
        supplyService.doCreateBatch(supplyDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> updateSupply(String scenario, List<SupplyDTO> supplyDTOList) {
        supplyService.doUpdateBatch(supplyDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> syncMoldChangeTime(String scenario, List<MesMoldChangeTime> moldChangeTimes) {
        return moldChangeTimeService.syncMoldChangeTime(scenario, moldChangeTimes);

    }

    @Override
    public List<ProductAdvanceBatchRuleVO> selectAllProductAdvanceBatchRule() {
        return productAdvanceBatchRuleService.selectAll();
    }

    @Override
    public BaseResponse<Void> handleChainLine(String scenario, List<ChainLineInventoryLogDTO> chainLineInventoryLogDTOList) {
        BaseResponse<Void> voidBaseResponse = chainLineInventoryLogService.handleChainLine(chainLineInventoryLogDTOList);
        return voidBaseResponse;
    }

    @Override
    public BaseResponse<Void> syncNakedGlassData(String scenario, List<NakedGlassDTO> list) {
        return nakedGlassService.syncNakedGlassData(list);
    }

    @Override
    public List<NakedGlassVO> selectNakedGlassByFinishedProducts(List<String> productCodes) {
        return nakedGlassService.selectNakedGlassByFinishedProducts(productCodes);
    }

    @Override
    public List<CapacitySupplyRelationshipVO> selectCapacitySupplyRelationshipByParams(String scenario, Map<String, Object> params) {
        return capacitySupplyRelationshipService.selectByParams(params);
    }

    @Override
    public List<CapacitySupplyRelationshipVO> selectCapacityBalanceWeeklyDataConsistentCheck(String scenario) {
        return capacitySupplyRelationshipService.dataConsistentCheckWeekly();
    }

    @Override
    public List<CapacitySupplyRelationshipVO> selectCapacityBalanceMonthlyDataConsistentCheck(String scenario) {
        return capacitySupplyRelationshipService.dataConsistentCheckMonthly();
    }

    @Override
    public AlgorithmConstraintRuleDTO getAlgorithmConstraintRuleMap(@RequestHeader("scenario") String scenario) {
        Map<String, Pair<String, String>> algorithmConstraintRuleMap = algorithmConstraintRuleService.getAlgorithmConstraintRuleMap();
        AlgorithmConstraintRuleDTO dto = new AlgorithmConstraintRuleDTO();
        if (algorithmConstraintRuleMap.containsKey(AlgorithmConstraintRuleEnum.COMMON_RULE_13.getCode())) {
            Pair<String, String> pair = algorithmConstraintRuleMap.get(AlgorithmConstraintRuleEnum.COMMON_RULE_13.getCode());
            dto.setRuleValue(pair.getRight());
        }
        return dto;
    }

    @Override
    public BaseResponse<Void> handlePlanOrder(String scenario, List<ErpPlanOrderQuery> list) {
        return masterPlanRelationService.sync(scenario, list);
    }

    @Override
    public BaseResponse<Void> syncMasterPlanRelation(String scenario, List<MasterPlanRelationLogDTO> list) {
        return masterPlanRelationLogService.syncMasterPlanRelation(list);
    }

    @Override
    public List<MasterPlanWorkOrderBodyVO> getMasterPlan(MasterPlanReq masterPlanReq) {
        return masterPlanService.getMasterPlan(masterPlanReq);
    }

    @Override
    public List<DeliveryPlanGeneralViewVO> getDeliveryPlanGeneralViewVO(MasterPlanReq masterPlanReq) {
        return masterPlanService.getDeliveryPlanGeneralView(masterPlanReq);
    }

    @Override
    public List<WorkOrderPublishedVO> selectWorkOrderPublishedByParams(String scenario, Map<String, Object> params) {
        return workOrderPublishedService.selectByParams(params);
    }

    @Override
    public List<OperationPublishedVO> selectOperationPublishedByParams(String scenario, Map<String, Object> params) {
        return operationPublishedService.selectByParams(params);
    }

    @Override
    public List<DemandPublishedVO> selectDemandPublishedByParams(String scenario, Map<String, Object> params) {
        return demandPublishedService.selectByParams(params);
    }

    @Override
    public List<OperationInputPublishedVO> selectOperationInputPublishedByParams(String scenario, Map<String, Object> params) {
        return operationInputPublishedService.selectByParams(params);

    }

    public void doRefreshCapacityBalance(String scenario, String userId, String planPeriod, String capacityPeriod) {
        DynamicDataSourceContextHolder.setDataSource(scenario);
        if (StringUtils.isEmpty(scenario) || StringUtils.isEmpty(planPeriod) || StringUtils.isEmpty(capacityPeriod)) {
            return;
        }
        //执行产能平衡计算
        algorithmExecuteService.capacityBalanceExecuteLock(capacityPeriod, scenario, userId);
        //发布版本
        capacityBalanceVersionService.publishVersionLock();
        DynamicDataSourceContextHolder.clearDataSource();
    }

    @Override
    public String selectMasterPlanPublishedLogIdByOperatorId() {
        return masterPlanPublishedLogService.selectNewestLogIdByOperatorId(null);
    }

    @Override
    public List<OutsourceTransferDemandDetailVO> selectOutsourceTransferDemandDetailByParams(String scenario, Map<String, Object> params) {
        return outsourceTransferDemandDetailService.selectByParams(params);
    }

    @Override
    public String selectWeekMaxVersionTime(String scenario) {
        return capacityBalanceVersionService.selectWeekMaxVersionTime();
    }

    @Override
    public List<WorkOrderVO> selectWorkOrderByParams(String scenario, Map<String, Object> params) {
        return workOrderService.selectByParams(params);
    }

    @Override
    public List<OperationVO> selectOperationByParams(String scenario, Map<String, Object> params) {
        return operationService.selectVOByParams(params);
    }

    @Override
    public List<DemandVO> selectDemandByParams(String scenario, Map<String, Object> params) {
        return demandService.selectByParams(params);
    }

    @Override
    public List<OperationInputVO> selectOperationInputByParams(String scenario, Map<String, Object> params) {
        return operationInputService.selectByParams(params);
    }

    @Override
    public List<MasterPlanTaskVO> selectByMasterReq(String scenario, MasterPlanReq masterPlanReq) {
        return operationTaskExtDao.selectByMasterReq(masterPlanReq);
    }

    @Override
    public void handleMesFeedBack(String scenario, List<MesFeedBack> mesFeedBacks) {
        mesFeedbackService.doHandleMesFeedBack(mesFeedBacks);
    }

    @Override
    public BaseResponse<Void> synMoldToolingGroupDir(String tenantId, String scenario) {
        return productFixtureRelationService.synMoldToolingGroupDir(tenantId, scenario);
    }

    @Override
    public BaseResponse<Void> handleMoldToolingGroupDir(String scenario, List<MesMoldToolingGroupDir> o) {
        return productFixtureRelationService.handleMoldToolingGroupDir(o);
    }

    @Override
    public Map<String, String> selectMoldToolingRelationsByProductCodes(String scenario, List<String> productCodes) {
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("productCodes", productCodes);
        params.put("enabled", YesOrNoEnum.YES.getCode());
        params.put("toolingType", "TOOLING_MOLD");
        List<ProductFixtureRelationVO> fixtureRelations = productFixtureRelationService.selectByParams(params);
        if (CollectionUtils.isEmpty(fixtureRelations)) {
            return MapUtil.newHashMap();
        }
        return fixtureRelations.stream().collect(
                Collectors.toMap(ProductFixtureRelationVO::getProductCode,
                        ProductFixtureRelationVO::getStandardResourceId,
                        (v1, v2) -> v1));
    }

    @Override
    public List<ProductFixtureRelationVO> selectMoldToolingRelationCountByProductCodes(String scenario, List<String> productCodes) {
        Map<String, Object> params = MapUtil.newHashMap();
        if (CollectionUtils.isNotEmpty(productCodes)) {
            params.put("productCodes", productCodes);
        }
        params.put("enabled", YesOrNoEnum.YES.getCode());
        return Optional.ofNullable(productFixtureRelationService.selectByParams(params)).orElse(Lists.newArrayList());
    }

    @Override
    public List<LinkedHashMap<String, Object>> selectByManualSql(String scenario, Map<String, String> map) {
        return commonBasicDao.selectByManualSql(map.get("sqlContext"));
    }

    @Override
    public List<FeedbackProductionVO> selectFeedbackProductionByOperationIds(String scenario, List<String> operationIds) {
        return feedbackProductionService.selectByOperationIds(operationIds);
    }

    @Override
    public Map<String, String> getObjectTypeEnumMap() {
        Map<String, String> allEnums = new HashMap<>();
        // 遍历所有可能的枚举类
        for (String enumClassName : objectTypeEnums) {
            try {
                Class<?> enumClass = Class.forName(enumClassName);
                collectEnumsFromClass(enumClass, allEnums);
            } catch (ClassNotFoundException e) {
                // 如果找不到类则跳过
            }
        }
        return allEnums;
    }

    @Override
    public Map<String, Class<?>> getObjectTypeClassMap() {
        Map<String, Class<?>> allObjects = new HashMap<>();

        Map<String, String> allEnums = getObjectTypeEnumMap();

        for (Map.Entry<String, String> entry : allEnums.entrySet()) {
            String key = entry.getKey();
            String className = entry.getValue();
            try {
                Class<?> objectClass = Class.forName(className);
                allObjects.put(key, objectClass);
            } catch (ClassNotFoundException e) {
                // 如果找不到类则跳过
            }
        }
        return allObjects;
    }

    @Override
    @Transactional
    public BaseResponse<List<OperationVO>> planAdjustGetData(String scenario, String taskId) {
        try {
            List<OperationVO> operation = getOperation(scenario, taskId);
            return BaseResponse.success(operation);
        } catch (Exception e) {
            return BaseResponse.error(e.getMessage());
        }
    }

    private List<OperationVO> getOperation(String scenario, String taskId) {
        // 获取计划周期
        PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();
        if (planningHorizon == null) {
            throw new BusinessException("无法获取计划周期信息");
        }
        Date planStartTime = planningHorizon.getPlanStartTime();
        Date planEndTime = planningHorizon.getPlanEndTime();

        // 获取工单数据
        List<WorkOrderVO> workOrderVOS = Optional.ofNullable(workOrderService.selectAll())
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .filter(p -> p.getEndTime() != null)
                .filter(p -> p.getEndTime().getTime() >= planStartTime.getTime() &&
                        p.getEndTime().getTime() <= planEndTime.getTime())
                .collect(Collectors.toList());

        // 提取产品ID
        List<String> productIds = workOrderVOS.stream()
                .filter(Objects::nonNull)
                .map(WorkOrderVO::getProductId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 如果没有产品ID，直接返回空列表
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.emptyList();
        }

        // 查询产品信息
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(com.google.common.collect.Lists.newArrayList("id", "product_code", "vehicle_model_code"))
                .queryParam(ImmutableMap.of("ids", productIds))
                .build();

        List<NewProductStockPointVO> productStockPointVOS = Optional.ofNullable(
                        newMdsFeign.selectProductListByParamOnDynamicColumns(scenario, feignDynamicParam))
                .orElse(Collections.emptyList());

        // 提取产品编码
        List<String> productCodes = productStockPointVOS.stream()
                .filter(Objects::nonNull)
                .map(NewProductStockPointVO::getProductCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 获取提前生产批次规则和风险等级信息
        List<ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOS = Optional.ofNullable(
                        productAdvanceBatchRuleService.selectAll())
                .orElse(Collections.emptyList());

        List<PartRiskLevelVO> partRiskLevelVOS = Optional.ofNullable(
                        dfpFeign.selectMaterialRiskLeveByProductCodeList(null, productCodes))
                .orElse(Collections.emptyList());

        // 构建不同维度的规则映射
        Map<String, ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(Objects::nonNull)
                .filter(p -> StrUtil.isNotEmpty(p.getProductCode()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getProductCode, v -> v, (k1, k2) -> k1));

        Map<String, ProductAdvanceBatchRuleVO> productTypeAdvanceBatchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(Objects::nonNull)
                .filter(p -> StrUtil.isNotEmpty(p.getProductType()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getProductType, v -> v, (k1, k2) -> k1));

        Map<String, ProductAdvanceBatchRuleVO> riskLevelAdvanceBatchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(Objects::nonNull)
                .filter(p -> StrUtil.isNotEmpty(p.getRiskLevel()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getRiskLevel, v -> v, (k1, k2) -> k1));

        Map<String, PartRiskLevelVO> partRiskLevelVOMap = partRiskLevelVOS.stream()
                .filter(Objects::nonNull)
                .filter(p -> StrUtil.isNotEmpty(p.getProductCode()))
                .collect(Collectors.toMap(PartRiskLevelVO::getProductCode, v -> v, (k1, k2) -> k1));

        // 构建产品ID到提前天数的映射
        Map<String, Integer> dayMap = buildDayMap(
                productStockPointVOS,
                productAdvanceBatchRuleVOMap,
                productTypeAdvanceBatchRuleVOMap,
                riskLevelAdvanceBatchRuleVOMap,
                partRiskLevelVOMap
        );

        // 按产品ID分组工单
        Map<String, List<WorkOrderVO>> workOrderMap = workOrderVOS.stream()
                .filter(Objects::nonNull)
                .filter(p -> StrUtil.isNotEmpty(p.getProductId()))
                .collect(Collectors.groupingBy(WorkOrderVO::getProductId));

        // 筛选需要调整的工单
        List<String> workOrderAdjustOperation = filterWorkOrdersForAdjustment(
                dayMap, workOrderMap, planStartTime);

        // 执行计划调整
        if (CollectionUtils.isNotEmpty(workOrderAdjustOperation)) {
            log.info("开始执行计划调整");
            amsSchedule.doAdjustPlan(workOrderAdjustOperation, taskId);
            log.info("执行计划调整结束");
        }

        // 查询操作信息
        List<OperationVO> operationVOS = operationService.selectVOByParams(
                ImmutableMap.of("orderIds", workOrderAdjustOperation));

        return Optional.ofNullable(operationVOS).orElse(Collections.emptyList());
    }

    /**
     * 构建产品ID到提前天数的映射
     */
    private Map<String, Integer> buildDayMap(
            List<NewProductStockPointVO> productStockPointVOS,
            Map<String, ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOMap,
            Map<String, ProductAdvanceBatchRuleVO> productTypeAdvanceBatchRuleVOMap,
            Map<String, ProductAdvanceBatchRuleVO> riskLevelAdvanceBatchRuleVOMap,
            Map<String, PartRiskLevelVO> partRiskLevelVOMap) {

        String lowRiskLevel = "低";
        Map<String, Integer> dayMap = new HashMap<>();

        for (NewProductStockPointVO productStockPointVO : productStockPointVOS) {
            if (productStockPointVO == null) {
                continue;
            }

            String id = productStockPointVO.getId();
            String productCode = productStockPointVO.getProductCode();
            String vehicleModelCode = productStockPointVO.getVehicleModelCode();

            if (StrUtil.isEmpty(id)) {
                continue;
            }

            // 根据物料代码获取
            if (StrUtil.isNotEmpty(productCode) && productAdvanceBatchRuleVOMap.containsKey(productCode)) {
                BigDecimal maximumNumberDays = productAdvanceBatchRuleVOMap.get(productCode).getMaximumNumberDays();
                if (maximumNumberDays != null) {
                    dayMap.put(id, maximumNumberDays.intValue());
                    continue;
                }
            }

            // 根据车型代码获取
            if (StrUtil.isNotEmpty(vehicleModelCode) && productTypeAdvanceBatchRuleVOMap.containsKey(vehicleModelCode)) {
                BigDecimal maximumNumberDays = productTypeAdvanceBatchRuleVOMap.get(vehicleModelCode).getMaximumNumberDays();
                if (maximumNumberDays != null) {
                    dayMap.put(id, maximumNumberDays.intValue());
                    continue;
                }
            }

            // 根据风险等级获取
            if (StrUtil.isNotEmpty(productCode) && partRiskLevelVOMap.containsKey(productCode)) {
                String materialRiskLevel = partRiskLevelVOMap.get(productCode).getMaterialRiskLevel();
                if (StrUtil.isNotEmpty(materialRiskLevel) && riskLevelAdvanceBatchRuleVOMap.containsKey(materialRiskLevel)) {
                    BigDecimal maximumNumberDays = riskLevelAdvanceBatchRuleVOMap.get(materialRiskLevel).getMaximumNumberDays();
                    if (maximumNumberDays != null) {
                        dayMap.put(id, maximumNumberDays.intValue());
                        continue;
                    }
                }
            }

            // 默认使用低风险等级规则
            ProductAdvanceBatchRuleVO productAdvanceBatchRuleVO = riskLevelAdvanceBatchRuleVOMap.get(lowRiskLevel);
            if (productAdvanceBatchRuleVO != null && productAdvanceBatchRuleVO.getMaximumNumberDays() != null) {
                dayMap.put(id, productAdvanceBatchRuleVO.getMaximumNumberDays().intValue());
            }
        }

        return dayMap;
    }

    /**
     * 筛选需要调整的工单
     */
    private List<String> filterWorkOrdersForAdjustment(
            Map<String, Integer> dayMap,
            Map<String, List<WorkOrderVO>> workOrderMap,
            Date planStartTime) {

        List<String> workOrderAdjustOperation = new ArrayList<>();

        for (Map.Entry<String, Integer> entry : dayMap.entrySet()) {
            String productId = entry.getKey();
            Integer days = entry.getValue();

            if (StrUtil.isEmpty(productId) || days == null) {
                continue;
            }

            List<WorkOrderVO> workOrders = workOrderMap.get(productId);
            if (CollectionUtils.isEmpty(workOrders)) {
                continue;
            }

            Date endTime = DateUtil.offsetDay(planStartTime, days);
            List<String> ids = workOrders.stream()
                    .filter(Objects::nonNull)
                    .filter(p -> p.getEndTime() != null)
                    .filter(p -> p.getEndTime().getTime() >= planStartTime.getTime() &&
                            p.getEndTime().getTime() <= endTime.getTime())
                    .map(WorkOrderVO::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            workOrderAdjustOperation.addAll(ids);
        }

        return workOrderAdjustOperation;
    }

    @Override
    public List<SubInventoryCargoLocationVO> subInventoryCargoLocation(String scenario, List<String> freightSpaces) {
        return subInventoryCargoLocationDao.selectByBatchCodeAndStockType(freightSpaces, StockPointTypeEnum.BC.getCode());
    }

    @Override
    public void batchUpdateOperation(List<OperationVO> operation) {
        operationService.doUpdateBatch(OperationConvertor.INSTANCE.vo2Dtos(operation));
    }

    @Override
    public void batchUpdateWorkOrder(List<WorkOrderVO> workOrderVOS) {
        workOrderService.doUpdateBatch(WorkOrderConvertor.INSTANCE.vo2Dtos(workOrderVOS));
    }
}
