package com.yhl.scp.mps.capacityBalance.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.*;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.routing.vo.RoutingStepResourceBasicVO;
import com.yhl.scp.mds.extension.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.mds.extension.organization.vo.ProductionOrganizationVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.product.dto.ResourceCalendarQueryDTO;
import com.yhl.scp.mps.capacityBalance.convertor.CapacityLoadConvertor;
import com.yhl.scp.mps.capacityBalance.domain.entity.CapacityLoadDO;
import com.yhl.scp.mps.capacityBalance.domain.service.CapacityLoadDomainService;
import com.yhl.scp.mps.capacityBalance.dto.CapacityBalanceAlgorithmDataDTO;
import com.yhl.scp.mps.capacityBalance.dto.CapacityLoadDTO;
import com.yhl.scp.mps.capacityBalance.enums.CapacityBalanceTypeEnum;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacityLoadDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacityLoadPO;
import com.yhl.scp.mps.capacityBalance.service.CapacityLoadService;
import com.yhl.scp.mps.capacityBalance.vo.BeatAverageVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO2;
import com.yhl.scp.mps.capacityBalance.vo.CapacityShiftVO;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CapacityLoadServiceImpl</code>
 * <p>
 * 产能负荷应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 15:40:31
 */
@Slf4j
@Service
public class CapacityLoadServiceImpl extends AbstractService implements CapacityLoadService {

    @Resource
    private CapacityLoadDao capacityLoadDao;

    @Resource
    private CapacityLoadDomainService capacityLoadDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private CapacitySupplyRelationshipDao capacitySupplyRelationshipDao;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private OperationTaskExtDao operationTaskExtDao;
    @Autowired
    private MdsFeign mdsFeign;

    @Override
    public BaseResponse<Void> doCreate(CapacityLoadDTO capacityLoadDTO) {
        // 0.数据转换
        CapacityLoadDO capacityLoadDO = CapacityLoadConvertor.INSTANCE.dto2Do(capacityLoadDTO);
        CapacityLoadPO capacityLoadPO = CapacityLoadConvertor.INSTANCE.dto2Po(capacityLoadDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        capacityLoadDomainService.validation(capacityLoadDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(capacityLoadPO);
        capacityLoadDao.insert(capacityLoadPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(CapacityLoadDTO capacityLoadDTO) {
        // 0.数据转换
        CapacityLoadDO capacityLoadDO = CapacityLoadConvertor.INSTANCE.dto2Do(capacityLoadDTO);
        CapacityLoadPO capacityLoadPO = CapacityLoadConvertor.INSTANCE.dto2Po(capacityLoadDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        capacityLoadDomainService.validation(capacityLoadDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(capacityLoadPO);
        capacityLoadDao.update(capacityLoadPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<CapacityLoadDTO> list) {
        List<CapacityLoadPO> newList = CapacityLoadConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        capacityLoadDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<CapacityLoadDTO> list) {
        List<CapacityLoadPO> newList = CapacityLoadConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        capacityLoadDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return capacityLoadDao.deleteBatch(idList);
        }
        return capacityLoadDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public CapacityLoadVO selectByPrimaryKey(String id) {
        CapacityLoadPO po = capacityLoadDao.selectByPrimaryKey(id);
        return CapacityLoadConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "CAPACITY_LOAD")
    public List<CapacityLoadVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        // PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "CAPACITY_LOAD")
    public List<CapacityLoadVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        if (StringUtils.isEmpty(sortParam)) {
            sortParam = "plant_code,operation_code,operation_name,resource_group_code,resource_code";
        }
/*        if (StringUtils.isEmpty(queryCriteriaParam)) {
            queryCriteriaParam = "version_Id is null";
        }else {
            queryCriteriaParam = queryCriteriaParam + "and version_Id is null";
        }*/
        List<CapacityLoadVO> dataList = capacityLoadDao.selectByCondition(sortParam, queryCriteriaParam);
        CapacityLoadServiceImpl target = springBeanUtils.getBean(CapacityLoadServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<CapacityLoadVO> selectByParams(Map<String, Object> params) {
        List<CapacityLoadPO> list = capacityLoadDao.selectByParams(params);
        return CapacityLoadConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<CapacityLoadVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.CAPACITY_LOAD.getCode();
    }

    @Override
    public List<CapacityLoadVO> invocation(List<CapacityLoadVO> dataList, Map<String, Object> params,
                                           String invocation) {
        String userId = SystemHolder.getUserId();

        List<CapacityLoadVO> filteredDataList = dataList;
        List<BeatAverageVO> beatAverages = capacitySupplyRelationshipDao.selectBeatAverage(new HashMap<>());
        Map<String, BigDecimal> averageBeatMap = beatAverages.stream().collect(Collectors
                .toMap(x -> String.join("-", x.getResourceCode(), x.getYearMonth()),
                        x -> x.getAverageBeat().divide(BigDecimal.ONE, 2, RoundingMode.HALF_UP), (v1, v2) -> v2));

        List<CapacityLoadVO> newDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(filteredDataList)) {
            List<Date> dateList = filteredDataList.stream().sorted(Comparator.comparing(CapacityLoadVO::getForecastTime))
                    .map(CapacityLoadVO::getForecastTime).distinct().collect(Collectors.toList());

            List<Date> intervalDates = DateUtils.getIntervalDates(dateList.get(0), dateList.get(dateList.size() - 1));
            Map<String, List<Date>> collect =
                    intervalDates.stream().collect(Collectors.groupingBy(t -> DateUtils.dateToString(t, DateUtils.YEAR_MONTH)));
            Set<String> month = collect.keySet();

            Map<String, List<CapacityLoadVO>> map =
                    filteredDataList.stream().collect(Collectors.groupingBy(t -> t.getPlantCode()
                            + "-" + t.getResourceGroupCode() + "-" + t.getResourceCode()));
            for (Map.Entry<String, List<CapacityLoadVO>> entry : map.entrySet()) {
                CapacityLoadVO vo = new CapacityLoadVO();
                vo.setPlantCode(entry.getKey().split("-")[0]);
                vo.setResourceGroupCode(entry.getKey().split("-")[1]);
                vo.setResourceCode(entry.getKey().split("-")[2]);
                vo.setOperationCode(entry.getValue().get(0).getOperationCode());
                vo.setOperationName(entry.getValue().get(0).getOperationName());
                List<CapacityLoadVO2> vo2List = new ArrayList<>();
                for (CapacityLoadVO capacityLoadVO : entry.getValue()) {
                    CapacityLoadVO2 capacityLoadVO2 = new CapacityLoadVO2();
                    BeanUtils.copyProperties(capacityLoadVO, capacityLoadVO2);
                    String key = capacityLoadVO.getResourceCode() + "-"
                            + DateUtils.dateToString(capacityLoadVO.getForecastTime(), DateUtils.YEAR_MONTH);
                    capacityLoadVO2.setAverageBeat(averageBeatMap.getOrDefault(key, BigDecimal.ZERO));
                    capacityLoadVO2.setTotalCapacity(capacityLoadVO2.getAverageBeat().compareTo(BigDecimal.ZERO) <= 0
                            ? BigDecimal.ZERO : capacityLoadVO2.getAvailableCapacity()
                            .divide(capacityLoadVO2.getAverageBeat(), 0, RoundingMode.CEILING));
                    capacityLoadVO2.setColor(getColor(capacityLoadVO.getCapacityUtilization()));
                    vo2List.add(capacityLoadVO2);
                }
                Map<String, CapacityLoadVO2> vo2Map =
                        vo2List.stream().collect(Collectors.toMap(t -> DateUtils.dateToString(t.getForecastTime(),
                                DateUtils.YEAR_MONTH), Function.identity(), (v1, v2) -> v1));
                for (String string : month) {
                    Date date = DateUtils.stringToDate(string, DateUtils.YEAR_MONTH);
                    if (!vo2Map.containsKey(string)) {
                        CapacityLoadVO2 capacityLoadVO2 = new CapacityLoadVO2(date, null, BigDecimal.ZERO,
                                BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO,
                                "0", 0);
                        vo2List.add(capacityLoadVO2);
                    }
                }
                // 按月份升序排序
                vo2List.sort(Comparator.comparing(CapacityLoadVO2::getForecastTime));
                vo.setCapacityLoadVO2List(vo2List);
                newDataList.add(vo);
            }
        }

        Comparator<CapacityLoadVO> comparator =
                Comparator.comparing((CapacityLoadVO vo) -> Optional.ofNullable(vo.getPlantCode()).orElse(""))
                        .thenComparing((CapacityLoadVO vo) -> Optional.ofNullable(vo.getOperationCode()).orElse(""))
                        .thenComparing((CapacityLoadVO vo) -> Optional.ofNullable(vo.getOperationName()).orElse(""))
                        .thenComparing((CapacityLoadVO vo) -> Optional.ofNullable(vo.getResourceGroupCode()).orElse(""))
                        .thenComparing((CapacityLoadVO vo) -> Optional.ofNullable(vo.getResourceCode()).orElse(""));

        return newDataList.stream().sorted(comparator).collect(Collectors.toList());
    }

    private String getColor(BigDecimal capacityUtilization) {
        if (capacityUtilization.compareTo(new BigDecimal("0.8")) >= 0 && capacityUtilization.compareTo(BigDecimal.ONE) < 0) {
            // 返回黄色
            return "1";
        } else if (capacityUtilization.compareTo(BigDecimal.ONE) >= 0) {
            // 返回红色
            return "2";
        } else {
            // 返回白的
            return "0";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCapacityLoadBasedOnVersionNew() {

        List<CapacityLoadVO> capacityLoadVOS = capacitySupplyRelationshipDao.selectLoad();
        if (CollectionUtils.isEmpty(capacityLoadVOS)) {
            return;
        }
        // 过滤特殊工艺
        capacityLoadVOS = filterSpecialOperation(capacityLoadVOS);
        if (capacityLoadVOS == null) return;
        // 删掉没有版本号的数据，下面会重新生成
        List<CapacityLoadPO> capacityLoadPOS = capacityLoadDao.selectLatestData();
        Map<String, CapacityLoadPO> map = capacityLoadPOS.stream()
                .collect(Collectors.toMap(t -> t.getResourceCode() + "#" + DateUtils.dateToString(t.getForecastTime(), DateUtils.YEAR_MONTH), Function.identity(), (v1, v2) -> v2));
        List<String> monthList = capacityLoadVOS.stream().map(CapacityLoadVO::getForecastMonth).distinct().collect(Collectors.toList());
        CapacityBalanceAlgorithmDataDTO algorithmDataDTO = new CapacityBalanceAlgorithmDataDTO();
        getResourceNew(SystemHolder.getScenario(), monthList, algorithmDataDTO, DateUtils.YEAR_MONTH);
        List<CapacityLoadPO> updateList = new ArrayList<>();
        List<CapacityLoadPO> insertList = new ArrayList<>();
        // 每个月每个设备的总产能 key = resourceCode+"-"+month;
        Map<String, Integer> resourceCapacityMap = algorithmDataDTO.getResourceCapacityMap();
        for (CapacityLoadVO capacityLoadVO : capacityLoadVOS) {
            String forecastMonth = capacityLoadVO.getForecastMonth();
            String resourceCode = capacityLoadVO.getResourceCode();
            String key = capacityLoadVO.getResourceCode() + "#" + forecastMonth;
            if (map.containsKey(key)) {
                CapacityLoadPO capacityLoadPO = map.get(key);
                if (capacityLoadPO.getDemandQuantity().compareTo(capacityLoadVO.getDemandQuantity()) != 0) {
                    capacityLoadPO.setDemandQuantity(capacityLoadVO.getDemandQuantity());
                    capacityLoadPO.setProductionCapacity(capacityLoadVO.getProductionCapacity());
                    capacityLoadPO.setCapacityUtilization(capacityLoadPO.getProductionCapacity().divide(capacityLoadPO.getAvailableCapacity(), 2, RoundingMode.HALF_UP));
                    updateList.add(capacityLoadPO);
                }
            } else {
                CapacityLoadPO po = new CapacityLoadPO();
                BeanUtils.copyProperties(capacityLoadVO, po);
                po.setForecastTime(DateUtils.stringToDate(forecastMonth, DateUtils.YEAR_MONTH));
                String capacityKey = resourceCode + "-" + forecastMonth;
                BigDecimal availableCapacity = BigDecimal.valueOf(resourceCapacityMap.getOrDefault(capacityKey, 0));
                BigDecimal capacityUtilization = BigDecimal.ZERO;
                if (availableCapacity.compareTo(BigDecimal.ZERO) == 0) {
                    log.info("该月{}的{}的产能为0，无法计算占用率", forecastMonth, resourceCode);
                } else {
                    capacityUtilization = po.getProductionCapacity().divide(availableCapacity, 2, RoundingMode.HALF_UP);
                }
                po.setAvailableCapacity(availableCapacity);
                po.setCapacityUtilization(capacityUtilization);
                if (algorithmDataDTO.getPhysicalresourceMap().containsKey(resourceCode)) {
                    po.setResourceName(algorithmDataDTO.getPhysicalresourceMap().get(resourceCode).getPhysicalResourceName());
                    po.setResourceGroupCode(algorithmDataDTO.getPhysicalresourceMap().get(resourceCode).getStandardResourceCode());
                    po.setResourceGroupName(algorithmDataDTO.getPhysicalresourceMap().get(resourceCode).getStandardResourceName());
                    po.setPlantCode(algorithmDataDTO.getPhysicalresourceMap().get(resourceCode).getProductionLine());
                }
                insertList.add(po);

            }
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            BasePOUtils.updateBatchFiller(updateList);
            capacityLoadDao.updateBatch(updateList);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            BasePOUtils.insertBatchFiller(insertList);
            capacityLoadDao.insertBatch(insertList);
        }
    }

    private List<CapacityLoadVO> filterSpecialOperation(List<CapacityLoadVO> capacityLoadVOS) {
        // 过滤掉特殊工艺对应的供应关系
        List<CollectionValueVO> specialTechnology = ipsFeign.getByCollectionCode("SPECIAL_TECHNOLOGY");
        List<String> specialTechnologyList =
                specialTechnology.stream().map(CollectionValueVO::getValueMeaning).collect(Collectors.toList());

        capacityLoadVOS = capacityLoadVOS.stream().filter(t -> !specialTechnologyList.contains(t.getOperationCode())).collect(Collectors.toList());
        // 获取资源对应工序
        if (CollectionUtils.isEmpty(capacityLoadVOS)) {
            return null;
        }
        return capacityLoadVOS;
    }

    @Override
    public void doSaveVersionCapacityLoad(String versionId) {
        // 最新数据没有版本id，原数据更新版本id，始终保留一版最新数据
        List<CapacityLoadPO> capacityLoadPOList = capacityLoadDao.selectLatestData();
        if (CollectionUtils.isEmpty(capacityLoadPOList)) {
            return;
        }
        for (CapacityLoadPO capacityLoadPO : capacityLoadPOList) {
            capacityLoadPO.setVersionId(versionId);
            capacityLoadPO.setId(UUIDUtil.getUUID());
        }
        BasePOUtils.insertBatchFiller(capacityLoadPOList);
        capacityLoadDao.insertBatch(capacityLoadPOList);
    }


    @Override
    public void getResourceNew(String defaultScenario, List<String> monthList,
                               CapacityBalanceAlgorithmDataDTO algorithmDataDTO, String pattern) {
        Map<String, Integer> resourceCapacityMap = new HashMap<>();
        Map<String, List<ResourceCalendarVO>> resourceCalendarMap = new HashMap<>();
        Map<String, PhysicalResourceVO> physicalresourceMap = new HashMap<>();
        Map<String, StandardResourceVO> standardResourceMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(monthList)) {
            // 过滤出正常日历类型
            List<CollectionValueVO> shiftType = ipsFeign.getByCollectionCode("SHIFT_TYPE");
            List<String> specialTechnologyList =
                    shiftType.stream().filter(t -> "正常".equals(t.getDescription())).map(CollectionValueVO::getCollectionValue).collect(Collectors.toList());

            List<StandardResourceVO> standardResourceVOList = newMdsFeign.selectStandardResourceVOS(defaultScenario);

            if (com.yhl.platform.common.utils.CollectionUtils.isNotEmpty(standardResourceVOList)) {
                standardResourceMap =
                        standardResourceVOList.stream().collect(Collectors.toMap(StandardResourceVO::getId,
                                Function.identity()));
                List<String> organizationIdList =
                        standardResourceVOList.stream().map(StandardResourceVO::getOrganizationId).distinct().collect(Collectors.toList());
                // 查询生产组织
                List<ProductionOrganizationVO> productionOrganizationVOS =
                        newMdsFeign.selectOrganizationByPrimaryIds(defaultScenario, organizationIdList);
                Map<String, ProductionOrganizationVO> organizationVOMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(productionOrganizationVOS)) {
                    organizationVOMap =
                            productionOrganizationVOS.stream().collect(Collectors.toMap(ProductionOrganizationVO::getId, Function.identity()));
                }

                List<String> standardResourceIdList =
                        standardResourceVOList.stream().map(StandardResourceVO::getId).collect(Collectors.toList());
                List<PhysicalResourceVO> physicalResourceVOList =
                        newMdsFeign.selectPhysicalResourceByStandResourceIds(defaultScenario, standardResourceIdList);
                if (CollectionUtils.isNotEmpty(algorithmDataDTO.getResourceCodes())) {
                    physicalResourceVOList =
                            physicalResourceVOList.stream().filter(t ->
                                            algorithmDataDTO.getResourceCodes().contains(t.getPhysicalResourceCode()))
                                    .collect(Collectors.toList());
                }
                if (com.yhl.platform.common.utils.CollectionUtils.isNotEmpty(physicalResourceVOList)) {
                    List<String> resourceIds =
                            physicalResourceVOList.stream().map(PhysicalResourceVO::getId).collect(Collectors.toList());
                    List<String> resourceCodes =
                            physicalResourceVOList.stream().map(PhysicalResourceVO::getPhysicalResourceCode).collect(Collectors.toList());
                    Map<String, PhysicalResourceVO> resourceIdMap =
                            physicalResourceVOList.stream().collect(Collectors.toMap(PhysicalResourceVO::getPhysicalResourceCode, Function.identity()));
                    Map<String, PhysicalResourceVO> resourceMap =
                            physicalResourceVOList.stream().collect(Collectors.toMap(PhysicalResourceVO::getId,
                                    Function.identity()));
                    log.info("所有资源数据{}", JacksonUtils.toJson(resourceCodes));

                    Map<String, String> physicalResourceOfStandIdMap =
                            physicalResourceVOList.stream().collect(Collectors.toMap(PhysicalResourceVO::getPhysicalResourceCode, PhysicalResourceVO::getStandardResourceId));

                    monthList = monthList.stream().sorted().collect(Collectors.toList());
                    Date startDate = DateUtils.stringToDate(monthList.get(0), pattern);
                    Date endDate = DateUtils.stringToDate(monthList.get(monthList.size() - 1), pattern);
                    // 从********移动到********
                    endDate = DateUtils.moveMonth(endDate, 1);
                    if (CollectionUtils.isNotEmpty(algorithmDataDTO.getRoutingStepResourceVOList())) {
                        resourceIds = algorithmDataDTO.getRoutingStepResourceVOList().stream().map(RoutingStepResourceBasicVO::getPhysicalResourceId).distinct().collect(Collectors.toList());
                        ;
                    }
                    ResourceCalendarQueryDTO calendarQueryDTO = new ResourceCalendarQueryDTO();
                    calendarQueryDTO.setPhysicalResourceIds(resourceIds);
                    calendarQueryDTO.setStartDate(startDate);
                    calendarQueryDTO.setEndDate(endDate);
                    List<ResourceCalendarVO> resourceCalendarVOS =
                            newMdsFeign.selectByResourceIdsAndDate(defaultScenario, calendarQueryDTO);

                    // 只根据物理资源id查数据量太大了
                    // List<ResourceCalendarVO> resourceCalendarVOS = newMdsFeign.selectByPhysicalResourceIds
                    // (defaultScenario, resourceIds);
                    if (CollectionUtils.isNotEmpty(resourceCalendarVOS)) {
                        resourceCalendarVOS =
                                resourceCalendarVOS.stream().filter(t -> specialTechnologyList.contains(t.getCalendarType())).collect(Collectors.toList());
                        resourceCalendarMap =
                                resourceCalendarVOS.stream().collect(Collectors.groupingBy(t -> t.getPhysicalResourceId() + "-" + DateUtils.dateToString(t.getWorkDay(), pattern)));
                    }
                    Map<String, PhysicalResourceVO> productionEfficiencyMap =
                            physicalResourceVOList.stream().collect(Collectors.toMap(PhysicalResourceVO::getPhysicalResourceCode, Function.identity(), (v1, v2) -> v1));
                    // 设备每天的产能=每天设备的开机时间*设备利用率
                    for (String month : monthList) {
                        for (String resourceId : resourceIds) {
                            PhysicalResourceVO physicalResourceVO = resourceMap.get(resourceId);
                            String resourceCode = physicalResourceVO.getPhysicalResourceCode();
                            String resourceQuantityCoefficient = physicalResourceVO.getResourceQuantityCoefficient();
                            String key = resourceId + "-" + month;
                            String capacityKey = resourceCode + "-" + month;
                            List<ResourceCalendarVO> resourceCalendarVOList = resourceCalendarMap.get(key);
                            BigDecimal oee =
                                    productionEfficiencyMap.get(resourceCode) == null || productionEfficiencyMap.get(resourceCode).getProductionEfficiency() == null ? BigDecimal.ONE : productionEfficiencyMap.get(resourceCode).getProductionEfficiency();
                            if (CollectionUtils.isNotEmpty(resourceCalendarVOList)) {
                                // 这里对一个设备的一个月各个班次的工作时间求和
                                Map<String, BigDecimal> resourceWorkTimeMap =
                                        resourceCalendarVOList.stream().collect(Collectors.toMap(ResourceCalendarVO::getPhysicalResourceId, ResourceCalendarVO::getWorkHours, BigDecimal::add));

                                // 这里工作时间单位是小时
                                BigDecimal workTime = resourceWorkTimeMap.get(resourceId) == null ? BigDecimal.ZERO :
                                        resourceWorkTimeMap.get(resourceId);

                                BigDecimal multiply = workTime.multiply(BigDecimal.valueOf(3600)).multiply(oee).multiply(new BigDecimal(resourceQuantityCoefficient));

                                resourceCapacityMap.put(capacityKey, multiply.intValue());
                            } else {// Integer.parseInt(month.substring(5,7))
                                if ("yyyy-MM-dd".equals(pattern)) {
                                    // 按天算，没有生产日历默认一天都可用
                                    BigDecimal multiply = new BigDecimal(86400).multiply(oee).multiply(new BigDecimal(resourceQuantityCoefficient));
                                    resourceCapacityMap.put(capacityKey, multiply.intValue());
                                } else {
                                    // 按月算
                                    // 产能平衡计算可能会计算3~5年的数据，生产日历可能没有维护那么旧的，没有生产日历的月份，可用产能=(当月天数)*24*3600*设备利用率
                                    int yearNum = Integer.parseInt(month.substring(0, 4));
                                    int monthNum = Integer.parseInt(month.substring(4, 6));
                                    int days = DateUtils.getMonthDayCount(yearNum, monthNum);
                                    BigDecimal multiply = new BigDecimal(86400).multiply(new BigDecimal(days)).multiply(oee).multiply(new BigDecimal(resourceQuantityCoefficient));
                                    resourceCapacityMap.put(capacityKey, multiply.intValue());
                                }

                            }
                        }
                    }
                    for (PhysicalResourceVO physicalResourceVO : physicalResourceVOList) {
                        String standResourceId =
                                physicalResourceOfStandIdMap.get(physicalResourceVO.getPhysicalResourceCode());
                        StandardResourceVO standardResourceVO = standardResourceMap.get(standResourceId);
                        physicalResourceVO.setStandardResourceCode(standardResourceVO.getStandardResourceCode());
                        physicalResourceVO.setStandardResourceName(standardResourceVO.getStandardResourceName());
                        if (organizationVOMap.containsKey(standardResourceVO.getOrganizationId())) {
                            physicalResourceVO.setProductionLine(organizationVOMap.get(standardResourceVO.getOrganizationId()).getOrganizationCode());
                        }
                    }

                    physicalresourceMap = resourceIdMap;
                }
            }
        }


        algorithmDataDTO.setResourceCalendarMap(resourceCalendarMap);
        algorithmDataDTO.setPhysicalresourceMap(physicalresourceMap);
        algorithmDataDTO.setResourceCapacityMap(resourceCapacityMap);
    }

    @Override
    public List<CapacityLoadVO> selectLoadByOperation(Pagination pagination, String sortParam,
                                                      String queryCriteriaParam) {
        List<CapacityLoadVO> result = new ArrayList<>();
        List<CapacityLoadVO> capacityLoadVOS = this.selectByPage(pagination, sortParam, queryCriteriaParam);
        if (CollectionUtils.isEmpty(capacityLoadVOS)) {
            return result;
        }
        List<Date> dateList =
                capacityLoadVOS.get(0).getCapacityLoadVO2List().stream().map(CapacityLoadVO2::getForecastTime).collect(Collectors.toList());
        Map<String, List<CapacityLoadVO>> operationMap =
                capacityLoadVOS.stream().collect(Collectors.groupingBy(t -> t.getOperationCode() + "-" + t.getOperationName()));

        for (Map.Entry<String, List<CapacityLoadVO>> entry : operationMap.entrySet()) {
            CapacityLoadVO capacityLoadVO = entry.getValue().get(0);
            List<CapacityLoadVO2> capacityLoadVO2List = new ArrayList<>();
            for (Date date : dateList) {
                CapacityLoadVO2 vo2 = new CapacityLoadVO2();
                vo2.setForecastTime(date);
                List<CapacityLoadVO2> vo2List =
                        entry.getValue().stream().flatMap(t -> t.getCapacityLoadVO2List().stream()).filter(t -> date.equals(t.getForecastTime())).collect(Collectors.toList());
                // 工序总需求量
                BigDecimal operationDemandQuantity =
                        vo2List.stream().map(CapacityLoadVO2::getDemandQuantity).reduce(BigDecimal.ZERO,
                                BigDecimal::add);
                // 工序月度总产量
                BigDecimal operationTotalCapacity =
                        vo2List.stream().map(CapacityLoadVO2::getTotalCapacity).reduce(BigDecimal.ZERO,
                                BigDecimal::add);
                // 工序负荷率
                BigDecimal operationCapacityUtilization = operationTotalCapacity.compareTo(BigDecimal.ZERO) == 0 ?
                        BigDecimal.ZERO : operationDemandQuantity.divide(operationTotalCapacity, 4,
                        RoundingMode.HALF_UP);
                vo2.setDemandQuantity(operationDemandQuantity);
                vo2.setTotalCapacity(operationTotalCapacity);
                vo2.setCapacityUtilization(operationCapacityUtilization);
                vo2.setColor(getColor(operationCapacityUtilization));
                capacityLoadVO2List.add(vo2);
            }

            CapacityLoadVO vo = new CapacityLoadVO();
            BeanUtils.copyProperties(capacityLoadVO, vo);
            vo.setResourceCode(null);
            vo.setResourceName(null);
            vo.setCapacityLoadVO2List(capacityLoadVO2List);
            result.add(vo);
        }
        return result.stream().sorted(Comparator.comparing(t -> t.getOperationCode() + t.getOperationName())).collect(Collectors.toList());
    }

    @Override
    public void doDeleteByVersionIds(List<String> versionIds) {
        if (CollectionUtils.isEmpty(versionIds)) {
            return;
        }
        capacityLoadDao.deleteByVersion(versionIds);
    }

    @Override
    public List<LabelValue<String>> selectStandardResourceDropdown(String organizationCode) {
        String userId = SystemHolder.getUserId();
        List<StandardResourceVO> standardResourceVOS = operationTaskExtDao.selectStandardResource(organizationCode,
                userId);
        return standardResourceVOS.stream()
                .map(item -> new LabelValue<>(item.getStandardResourceCode()
                        + "-" + item.getStandardResourceName(), item.getStandardResourceCode()))
                .collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> selectPhysicalResourceDropdown(String organizationCode, String standardResourceCode) {
        String userId = SystemHolder.getUserId();
        List<PhysicalResourceVO> physicalResourceVOS = operationTaskExtDao.selectPhysicalResource(organizationCode,
                null, standardResourceCode);
        return physicalResourceVOS.stream()
                .map(item -> new LabelValue<>(item.getPhysicalResourceCode()
                        + "-" + item.getPhysicalResourceName(), item.getPhysicalResourceCode()))
                .collect(Collectors.toList());
    }

    @Override
    public List<CapacityShiftVO> capacityShift(String line) {
        List<String> lineList = new ArrayList<>();
        if (StrUtil.isNotEmpty(line)) {
            lineList.add(line);
        } else {
            lineList = newMdsFeign.getPlannerPhysicalResource(SystemHolder.getUserId())
                    .stream().map(PhysicalResourceVO::getPhysicalResourceCode).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(lineList)){
            return ListUtil.empty();
        }
        List<CapacityLoadVO> capacityLoadVOS = capacityLoadDao.selectShiftLine(lineList);
        if (CollectionUtils.isEmpty(capacityLoadVOS)) {
            return ListUtil.empty();
        }
        capacityLoadVOS.sort(Comparator.comparing(CapacityLoadVO::getResourceCode).thenComparing(CapacityLoadVO::getForecastTime));
        Map<String, BeatAverageVO> beatAverageVOMap = capacitySupplyRelationshipDao.selectBeatAverage(new HashMap<>())
                .stream().collect(Collectors.toMap(p ->
                                StrUtil.join("&", p.getResourceCode(), p.getYearMonth()),
                        Function.identity(), (v1, v2) -> v1));
        List<PhysicalResourceVO> physicalResourceList = newMdsFeign.selectPhysicalResourceByParams(SystemHolder.getScenario(), ImmutableMap.of("physicalResourceCodes",
                StreamUtils.columnToList(capacityLoadVOS, CapacityLoadVO::getResourceCode)));
        Map<String, PhysicalResourceVO> resourceMap = StreamUtils.mapByColumn(physicalResourceList, PhysicalResourceVO::getPhysicalResourceCode);
        List<String> resourceIds = StreamUtils.columnToList(physicalResourceList, PhysicalResourceVO::getId);
        List<ResourceCalendarVO> resourceCalendarVOS = operationTaskExtDao.selectResourceCalendarsByParams(ImmutableMap.of("physicalResourceIds", resourceIds));
        Map<String, List<ResourceCalendarVO>> calendarMap = StreamUtils.mapListByColumn(resourceCalendarVOS,
                p -> StrUtil.join("&", DateUtils.dateToString(p.getWorkDay(), DateUtils.YEAR_MONTH), p.getPhysicalResourceId()));
        List<CapacityShiftVO> capacityShiftVOS = new ArrayList<>();
        for (CapacityLoadVO capacityLoadVO : capacityLoadVOS) {
            String resourceCode = capacityLoadVO.getResourceCode();
            PhysicalResourceVO physicalResourceVO = resourceMap.get(resourceCode);
            if (Objects.isNull(physicalResourceVO)) {
                continue;
            }
            String id = physicalResourceVO.getId();
            Date forecastTime = capacityLoadVO.getForecastTime();
            String preMonth = DateUtils.dateToString(DateUtil.offsetMonth(forecastTime, -1), DateUtils.YEAR_MONTH);
            String time = DateUtils.dateToString(forecastTime, DateUtils.YEAR_MONTH);
            // 当前资源+月份的生产日历
            String key = StrUtil.join("&", time, id);
            List<ResourceCalendarVO> resourceCalendar = new ArrayList<>();
            if (calendarMap.containsKey(key)) {
                resourceCalendar.addAll(calendarMap.get(key));
                resourceCalendar.sort(Comparator.comparing(ResourceCalendarVO::getStartTime));
                // 生产日历考虑节假日，增减班次给出建议即可，按一个班次作为增减单位
                //触发条件：生产日历中存在空的可用时间或者存在班次为节假日类型的班次；当月产能超100%
                for (int i = 1; i < resourceCalendar.size(); i++) {
                    ResourceCalendarVO preResourceCalendarVO = resourceCalendar.get(i - 1);
                    ResourceCalendarVO resourceCalendarVO = resourceCalendar.get(i);
                    Date preEndTime = preResourceCalendarVO.getEndTime();
                    Date startTime = resourceCalendarVO.getStartTime();
                    String calendarType = resourceCalendarVO.getCalendarType();
                    // 判断时段是否连续，不连续则构建增减班次（一整天不相等，时段间隔不相等）
                    if (preEndTime.getTime() != startTime.getTime()) {
                        // 判断前一个时间的结束时间是否是当天的最后时刻及当天的开始时间是起始日期
                        Boolean check = checkTime(preEndTime, startTime);
                        if (check) {
                            CapacityShiftVO capacityShiftVO = getCapacityShiftVO(preEndTime, startTime, resourceCode, beatAverageVOMap, time);
                            capacityShiftVOS.add(capacityShiftVO);
                        }
                    }
                    // 法定节假日构建增减班次
                    if (calendarType.equals("PUBLIC_HOLIDAY")) {
                        CapacityShiftVO capacityShiftVO = getCapacityShiftVO(startTime, resourceCalendarVO.getEndTime(),
                                resourceCode, beatAverageVOMap, time);
                        capacityShiftVOS.add(capacityShiftVO);
                    }
                    if (i == 1) {
                        Date start = DateUtil.beginOfDay(preResourceCalendarVO.getWorkDay());
                        String preKey = StrUtil.join("&", preMonth, id);
                        if (calendarMap.containsKey(preKey)) {
                            Optional<ResourceCalendarVO> latestCalendarOpt = calendarMap.get(preKey).stream()
                                    .max(Comparator.comparing(ResourceCalendarVO::getEndTime));
                            if (latestCalendarOpt.isPresent()) {
                                Date endTime = latestCalendarOpt.get().getEndTime();
                                start = endTime.after(start) ? endTime : start;
                            }
                        }
                        Date preStartTime = preResourceCalendarVO.getStartTime();
                        if (start.getTime() != preStartTime.getTime()) {
                            CapacityShiftVO capacityShiftVO = getCapacityShiftVO(start, preStartTime, resourceCode, beatAverageVOMap, time);
                            capacityShiftVOS.add(capacityShiftVO);
                        }
                        if (preResourceCalendarVO.getCalendarType().equals("PUBLIC_HOLIDAY")) {
                            CapacityShiftVO capacityShiftVO = getCapacityShiftVO(preResourceCalendarVO.getStartTime(), preResourceCalendarVO.getEndTime(),
                                    resourceCode, beatAverageVOMap, time);
                            capacityShiftVOS.add(capacityShiftVO);
                        }
                    }
                    if (i == resourceCalendar.size() - 1) {
                        int startDay = DateUtil.dayOfMonth(resourceCalendarVO.getStartTime());
                        int endDay = DateUtil.dayOfMonth(resourceCalendarVO.getEndTime());
                        // 开始结束时间跨天不判断是否连续
                        if(startDay == endDay){
                            // 补全最后一个时段
                            Date end = DateUtil.beginOfMinute(DateUtil.endOfDay(resourceCalendarVO.getEndTime()));
                            Date lastEndDay = resourceCalendarVO.getEndTime();
                            if (end.getTime() != lastEndDay.getTime()) {
                                CapacityShiftVO capacityShiftVO = getCapacityShiftVO(lastEndDay, end, resourceCode, beatAverageVOMap, time);
                                capacityShiftVOS.add(capacityShiftVO);
                            }
                        }
                    }
                }
            }
            Date startOfMonth = DateUtil.beginOfMonth(forecastTime);
            Date endOfMonth = DateUtil.endOfMonth(forecastTime);
            List<Date> intervalDates = DateUtils.getIntervalDates(startOfMonth, endOfMonth);
            Map<String, List<ResourceCalendarVO>> resourceCalendarMap = resourceCalendar.stream()
                    .collect(Collectors.groupingBy(p -> DateUtils.dateToString(p.getWorkDay(), DateUtils.COMMON_DATE_STR3)));
            for (Date intervalDate : intervalDates) {
                String day = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                if (!resourceCalendarMap.containsKey(day)) {
                    // 当天没有生产日历
                    Date start = DateUtil.beginOfDay(intervalDate);
                    Date end = DateUtil.endOfDay(intervalDate);
                    CapacityShiftVO capacityShiftVO = getCapacityShiftVO(start, end, resourceCode, beatAverageVOMap, time);
                    capacityShiftVOS.add(capacityShiftVO);
                }
            }
        }
        return capacityShiftVOS;
    }

    public Boolean checkTime(Date preEndTime, Date startTime) {
        // 获取preEndTime的结束时间，去除秒
        Date dayEnd = DateUtil.beginOfMinute(DateUtil.endOfDay(preEndTime));
        // 获取startTime的开始时间
        Date dayBegin = DateUtil.beginOfDay(startTime);
        return dayEnd.getTime() != preEndTime.getTime() || startTime.getTime() != dayBegin.getTime();
    }

    private CapacityShiftVO getCapacityShiftVO(Date start,
                                               Date end,
                                               String resourceCode,
                                               Map<String, BeatAverageVO> beatAverageVOMap,
                                               String time) {
        String join = StrUtil.join("&", resourceCode, time);
        CapacityShiftVO capacityShiftVO = new CapacityShiftVO();
        capacityShiftVO.setResourceCode(resourceCode);
        capacityShiftVO.setOpenTimePeriod(DateUtils.dateToString(start, DateUtils.COMMON_DATE_STR1) + "~" + DateUtils.dateToString(end, DateUtils.COMMON_DATE_STR1));
        BeatAverageVO beatAverageVO = beatAverageVOMap.get(join);
        if (Objects.nonNull(beatAverageVO)) {
            long timeInterval = DateUtil.between(start, end, DateUnit.SECOND);
            BigDecimal count = BigDecimalUtils.divide(BigDecimalUtils.toBigDecimal(timeInterval), beatAverageVO.getAverageBeat(), 0);
            capacityShiftVO.setCount(count.toString());
        } else {
            capacityShiftVO.setCount("无节拍");
        }
        return capacityShiftVO;
    }


    @Override
    public void doRefreshCapacityLoad(List<String> needRefreshResourceCodeList) {
        String pattern = DateUtils.COMMON_DATE_STR3;
        if (CollectionUtils.isEmpty(needRefreshResourceCodeList)) {
            return;
        }
        List<CapacityLoadVO> capacityLoadVOS = capacitySupplyRelationshipDao.selectWeekLoad(needRefreshResourceCodeList);
        if (CollectionUtils.isEmpty(capacityLoadVOS)) {
            return;
        }
        // 过滤特殊工艺
        capacityLoadVOS = filterSpecialOperation(capacityLoadVOS);
        if (capacityLoadVOS == null) return;

        Map<String, Object> params = new HashMap<>();
        params.put("resourceCodes", needRefreshResourceCodeList);
        params.put("versionId", CapacityBalanceTypeEnum.WEEK.getCode());
        // 已有负荷数据
        List<CapacityLoadPO> oldCapacityLoadPOS = capacityLoadDao.selectByParams(params);

        List<String> dayList = capacityLoadVOS.stream().map(t -> DateUtils.dateToString(t.getForecastTime(),
                        pattern)).distinct()
                .collect(Collectors.toList());
        CapacityBalanceAlgorithmDataDTO algorithmDataDTO = new CapacityBalanceAlgorithmDataDTO();
        algorithmDataDTO.setResourceCodes(needRefreshResourceCodeList);
        getResourceNew(SystemHolder.getScenario(), dayList, algorithmDataDTO, pattern);
        Map<String, CapacityLoadPO> map = oldCapacityLoadPOS.stream()
                .collect(Collectors.toMap(t -> t.getResourceCode() + "#" + DateUtils.dateToString(t.getForecastTime(), pattern),
                        Function.identity(), (v1, v2) -> v2));

        Map<String, CapacityLoadVO> newMap = capacityLoadVOS.stream()
                .collect(Collectors.toMap(t -> t.getResourceCode() + "#" + DateUtils.dateToString(t.getForecastTime(), pattern),
                        Function.identity(), (v1, v2) -> v2));
        for (Map.Entry<String, CapacityLoadPO> entry : map.entrySet()) {
            String key = entry.getKey();
            CapacityLoadPO capacityLoadPO = entry.getValue();
            CapacityLoadVO newCapacityLoadVO = newMap.get(key);
            if (newCapacityLoadVO != null && capacityLoadPO != null) {
                boolean b = capacityLoadPO.getDemandQuantity().compareTo(newCapacityLoadVO.getDemandQuantity()) != 0;
                if (b) {
                    log.info("资源：{}，时间：{}，需求量有变化, old{}, new{}", key,
                            DateUtils.dateToString(capacityLoadPO.getForecastTime(), pattern),
                            capacityLoadPO.getDemandQuantity(), newCapacityLoadVO.getDemandQuantity());
                }
            }
        }

        List<CapacityLoadPO> updateList = new ArrayList<>();
        List<CapacityLoadPO> insertList = new ArrayList<>();
        // 每天产能
        Map<String, Integer> resourceCapacityMap = algorithmDataDTO.getResourceCapacityMap();

        for (CapacityLoadVO sourceCapacityLoadVO : capacityLoadVOS) {
            String forecastMonth = DateUtils.dateToString(sourceCapacityLoadVO.getForecastTime(), pattern);
            String resourceCode = sourceCapacityLoadVO.getResourceCode();
            String key = sourceCapacityLoadVO.getResourceCode() + "#" + forecastMonth;
            if (map.containsKey(key)) {
                CapacityLoadPO capacityLoadPO = map.get(key);
                if (capacityLoadPO.getDemandQuantity().compareTo(sourceCapacityLoadVO.getDemandQuantity()) != 0) {
                    capacityLoadPO.setDemandQuantity(sourceCapacityLoadVO.getDemandQuantity());
                    capacityLoadPO.setProductionCapacity(sourceCapacityLoadVO.getProductionCapacity());
                    capacityLoadPO.setCapacityUtilization(capacityLoadPO.getProductionCapacity().divide(capacityLoadPO.getAvailableCapacity(), 2, RoundingMode.HALF_UP));
                    updateList.add(capacityLoadPO);
                }
            } else {
                CapacityLoadPO po = new CapacityLoadPO();
                BeanUtils.copyProperties(sourceCapacityLoadVO, po);
                po.setForecastTime(DateUtils.stringToDate(forecastMonth, pattern));
                String capacityKey = resourceCode + "-" + forecastMonth;
                BigDecimal availableCapacity = BigDecimal.valueOf(resourceCapacityMap.getOrDefault(capacityKey, 0));
                BigDecimal capacityUtilization = BigDecimal.ZERO;
                if (availableCapacity.compareTo(BigDecimal.ZERO) == 0) {
                    log.info("该月{}的{}的产能为0，无法计算占用率", forecastMonth, resourceCode);
                } else {
                    capacityUtilization = po.getProductionCapacity().divide(availableCapacity, 2, RoundingMode.HALF_UP);
                }
                po.setAvailableCapacity(availableCapacity);
                po.setCapacityUtilization(capacityUtilization);
                if (algorithmDataDTO.getPhysicalresourceMap().containsKey(resourceCode)) {
                    po.setResourceName(algorithmDataDTO.getPhysicalresourceMap().get(resourceCode).getPhysicalResourceName());
                    po.setResourceGroupCode(algorithmDataDTO.getPhysicalresourceMap().get(resourceCode).getStandardResourceCode());
                    po.setResourceGroupName(algorithmDataDTO.getPhysicalresourceMap().get(resourceCode).getStandardResourceName());
                    po.setPlantCode(algorithmDataDTO.getPhysicalresourceMap().get(resourceCode).getProductionLine());
                }
                insertList.add(po);

            }
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            BasePOUtils.updateBatchFiller(updateList);
            capacityLoadDao.updateBatch(updateList);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            BasePOUtils.insertBatchFiller(insertList);
            capacityLoadDao.insertBatch(insertList);
        }
    }
}
