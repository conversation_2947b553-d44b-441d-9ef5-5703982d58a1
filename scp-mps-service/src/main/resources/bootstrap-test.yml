nacos-server-addr: 10.112.17.132:8848
spring:
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos-server-addr}
        namespace: bpim-test
      config:
        server-addr: ${nacos-server-addr}
        namespace: bpim-test
        group: DEFAULT_GROUP
        prefix: scp-mps-service
        file-extension: yaml
        shared-configs:
          - dataId: datasource.yaml
            group: DEFAULT_GROUP
            refresh: true
          - dataId: redis.yaml
            group: DEFAULT_GROUP
            refresh: true
          - dataId: minio.yaml
            group: DEFAULT_GROUP
            refresh: true
          - dataId: rabbitmq.yaml
            group: DEFAULT_GROUP
            refresh: true
          - dataId: logging.yaml
            group: DEFAULT_GROUP
            refresh: true