package com.yhl.scp.mps.capacityBalance.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mps.capacityBalance.dto.CapacityBalanceAlgorithmDataDTO;
import com.yhl.scp.mps.capacityBalance.dto.CapacityLoadDTO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityShiftVO;

import java.util.List;

/**
 * <code>CapacityLoadService</code>
 * <p>
 * 产能负荷应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 15:40:10
 */
public interface CapacityLoadService extends BaseService<CapacityLoadDTO, CapacityLoadVO> {

    /**
     * 查询所有
     *
     * @return list {@link CapacityLoadVO}
     */
    List<CapacityLoadVO> selectAll();

    /**
     * 根据最新产能供应关系计算产能负荷
     * @param
     */
    void saveCapacityLoadBasedOnVersionNew();

    /**
     * 发布时保存版本
     * @param versionId
     */
    void doSaveVersionCapacityLoad(String versionId);

    /**
     * 获取设备，产能， today 计算开始时间
     * @param defaultScenario
     * @param monthList
     * @param algorithmDataDTO
     */
    void getResourceNew(String defaultScenario, List<String> monthList, CapacityBalanceAlgorithmDataDTO algorithmDataDTO, String pattern);

    /**
     * 按工序维度汇总
     * @param pagination
     * @param sortParam
     * @param queryCriteriaParam
     * @return
     */
    List<CapacityLoadVO> selectLoadByOperation(Pagination pagination, String sortParam, String queryCriteriaParam);

    /**
     * 根据版本id删除数据
     * @param versionIds
     */
    void doDeleteByVersionIds(List<String> versionIds);

    /**
     * 产线组下拉
     * @param organizationCode
     * @return
     */
    List<LabelValue<String>> selectStandardResourceDropdown(String organizationCode);

    /**
     * 产线下拉
     * @param organizationCode
     * @param standardResourceCode
     * @return
     */
    List<LabelValue<String>> selectPhysicalResourceDropdown(String organizationCode, String standardResourceCode);


    List<CapacityShiftVO> capacityShift(String line);


    /**
     * 刷新周产能平衡下的资源负荷数据
     * @param needRefreshResourceCodeList
     */
    void doRefreshCapacityLoad(List<String> needRefreshResourceCodeList);

}
