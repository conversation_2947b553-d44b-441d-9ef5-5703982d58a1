package com.yhl.scp.mps.capacityBalance.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepResourceVO;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mps.capacityBalance.dto.CapacitySupplyRelationshipDTO;
import com.yhl.scp.mps.capacityBalance.dto.CapacitySupplyRelationshipEditorDTO;
import com.yhl.scp.mps.capacityBalance.dto.OutsourcingAdjustmentDTO;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;

import java.util.List;
import java.util.Map;

/**
 * <code>CapacitySupplyRelationshipService</code>
 * <p>
 * 产能供应关系应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 15:41:17
 */
public interface CapacitySupplyRelationshipService extends BaseService<CapacitySupplyRelationshipDTO, CapacitySupplyRelationshipVO> {

    /**
     * 查询所有
     *
     * @return list {@link CapacitySupplyRelationshipVO}
     */
    List<CapacitySupplyRelationshipVO> selectAll();

    /**
     * 保存一版最新产能供应关系
     *
     * @param versionId
     * @return
     */
    void doSaveCapacitySupplyRelationshipBasedOnVersion(String versionId);

    /**
     * 汇总数据批量锁定解锁
     * @param dtoList
     * @param lockStatus
     */
    void lockStatusBatchNew(List<CapacitySupplyRelationshipVO> dtoList, String lockStatus);


    /**
     * 汇总数据单行编辑
     * @param dto
     */
    void doLineEditNew(CapacitySupplyRelationshipEditorDTO dto);


    /**
     * 汇总数据批量编辑
     * @param dtoList
     */
    void doBatchEdit(List<CapacitySupplyRelationshipEditorDTO> dtoList);

    /**
     * 委外调整
     *
     * @param outsourceTransferSummaryDTO 委外调整参数
     */
    void doOutsourcingAdjustment(OutsourcingAdjustmentDTO outsourceTransferSummaryDTO);


    /**
     * 根据最新产能供应关系计算设备生产关系优先级
     * @param capacitySupplyRelationshipVOList
     */
    List<ProductCandidateResourceTimeVO> calculateThePriority(List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOList,
                                                              String scenario);

    /**
     * 根据最新产能供应关系计算设备生产关系优先级-产能平衡发布使用
     */
    void saveEquipmentProductionRelation(String scenario);

    /**
     * 根据本厂编码+工序查询候选资源
     * @param
     * @return
     */
    List<LabelValue<String>> resourceDropDown(List<String> routingStepIds);

    /**
     * 根据本厂编码，供应时间，查询产能明细，默认只查委外的
     * @param paramMap
     * @return
     */
	List<CapacitySupplyRelationshipVO> selectForSupplyCalculate(Map<String, Object> paramMap);

    /**
     * 按月汇总查询
     * @param dto
     * @return
     */
    PageInfo<CapacitySupplyRelationshipVO> selectCollect(CapacitySupplyRelationshipEditorDTO dto);

    /**
     * 根据版本号按月汇总查询
     * @param dto
     * @return
     */
    PageInfo<CapacitySupplyRelationshipVO> selectCollectByVersion(CapacitySupplyRelationshipEditorDTO dto);


    void deleteByVersionIds(List<String> versionIds);

    void doUpdateVersionIds(String versionId, List<String> list);

    List<CapacitySupplyRelationshipVO> dataConsistentCheckWeekly();

    List<CapacitySupplyRelationshipVO> dataConsistentCheckMonthly();

    void checkSpecialOperation(String operationCode);

    RoutingStepResourceVO getNewRoutingStepResource(String routingStepResourceId, String resourceId);

}
