package com.yhl.scp.mps.capacityBalance.dto;

import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.oem.vo.OemTransportTimeVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.mds.extension.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepResourceVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.resource.vo.ResourceVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * <code>CapacityBalanceAlgorithmDataDTO</code>
 * <p>
 * 产能平衡算法数据DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-25 14:09:37
 */
@ApiModel(value = "产能平衡算法数据DTO")
@Data
public class CapacityBalanceAlgorithmDataDTO implements Serializable {
    //拆解后的工序净需求（减掉了锁定和委外的数据） key = day+"-"+productCode+"-"+sequenceNo;
    private Map<String, Integer> operationQty;
    //拆解后的原始工序净需求 这个里面是根据发货计划和一致性业务预测拆解出来的原始需求量(如果有委外的数据，primitiveOperationQty和operationQty就不一样了)
    private Map<String, Integer> primitiveOperationQty;

    //设备相关信息
    private Map<String, ResourceVO> resourceMap;
    private Map<String, PhysicalResourceVO> physicalresourceMap;

    //设备对应的日历信息
    private Map<String, List<ResourceCalendarVO>> resourceCalendarMap;
    //设备每日产能 key = resourceCode+"-"+day
    private Map<String, Integer> resourceCapacityMap;
    //工序候选资源(产品资源生产关系数据)
    private Map<String, List<ProductCandidateResourceTimeVO>> productAndOperationOfResourceMap;
    //工艺路径中候选资源数据
    private List<RoutingStepResourceVO> routingStepResourceVOList;
    //资源对应工序
    private Map<String, ProductCandidateResourceTimeVO> resourceOperationMap;

    //每月已锁定数据(按月)
    private Map<String, List<CapacitySupplyRelationshipVO>> lockData;
    // 锁定数据（key为demandMonth#productCode-operationCode）
    private Map<String, List<CapacitySupplyRelationshipVO>> lockDataMap;

    //产能计算规则
    private List<String> rule;
    //要计算的日期
    private List<String> monthList;
    private List<String> dayList;
    //要计算的物料
    private List<String> productCodeList;
    //物料对应的工序
    private Map<String, Set<String>> operationOfProductMap;
    //产品相关信息
    private Map<String, NewProductStockPointVO> productMap;
    //零件风险等级
    private Map<String, PartRiskLevelVO> materialRiskLevelMap;
    //计划周期（一致性业务预测月份）
    private String planPeriod;

    //一致性业务预测版本号
    private String versionCode;
    //主机厂信息
    private Map<String, OemVO> oemVOMap;
    //特殊工艺对应供应关系
    private List<CapacitySupplyRelationshipVO> specialOperationShipData;
    //特殊工艺对应产能负荷
    private List<CapacityLoadVO> specialOperationLoadData;
    //候选资源id集合
    private List<String> routingStepResourceIds;
    //委外数据对应供应关系
    private List<CapacitySupplyRelationshipVO> outShipData;
    //补充的候选资源优先级
    private List<ProductCandidateResourceTimeVO> addResourceTimeList;

    //当月需求委外天数百分比， key = month-productCode
    private Map<String, BigDecimal> demandOutsourcingPercentage;
    //当月工序委外天数百分比， key = month-productCode+operationCode
    private Map<String, BigDecimal> processOutsourcingPercentage;
    //当月需求委外天数， key = month-productCode
    private Map<String, List<String>> demandOutsourcingPercentDays;
    //当月工序委外天数， key = month-productCode+operationCode
    private Map<String,  List<String>> processOutsourcingPercentDays;
    //本厂编码-主机厂编码对应关系
    private Map<String, List<String>> productOfOemMap;
    //工序在制
    private Map<String, Integer> operationInventoryMap;
    //实时库存
    private Map<String, Integer> productInventoryMap;
    //保存月份-本厂编码-安全库存
    private Map<String, Map<String, Integer>> endingInventoryMap;
    //本厂编码-原编码对应关系
    private Map<String, String> productOfSourceProductMap;
    private Map<String, List<String>> productOfSourceProductListMap;
    //产能平衡执行时间(暂时不用)
    private Date now;
    //发货计划开始时间(暂时不用)
    private String deliveryPlanStart;
    //产能平衡计算类型（月/周）
    private String type;
    // 工艺路径步骤（物料编码，工艺路径步骤id）
    private Map<String, String> routingStepIdMap;
    //异常信息
    private List<CapacitySupplyRelationshipExceptionDTO> exceptionPOList;
    // 产品每道工艺路径步骤完整信息
    private Map<String, RoutingStepVO> routingStepVOMap;
    // 不参与安全库存水位计算的产品编码
    private List<String> safetyProductCodeList;
    // 主机厂运输时间
    private Map<String, OemTransportTimeVO> oemTransportTimeVOMap;
    // 需求预测第一天
    private String cleanForecastFirstDay;

    private List<String> resourceCodes;

    public CapacityBalanceAlgorithmDataDTO() {
        this.operationQty = new HashMap<>();
        this.primitiveOperationQty = new HashMap<>();
        this.resourceMap = new HashMap<>();
        this.physicalresourceMap = new HashMap<>();
        this.resourceCalendarMap = new HashMap<>();
        this.resourceCapacityMap = new HashMap<>();
        this.productAndOperationOfResourceMap = new HashMap<>();
        this.routingStepResourceVOList = new ArrayList<>();
        this.resourceOperationMap = new HashMap<>();
        this.lockData = new HashMap<>();
        this.rule = new ArrayList<>();
        this.monthList = new ArrayList<>();
        this.productCodeList = new ArrayList<>();
        this.operationOfProductMap = new HashMap<>();
        this.productMap = new HashMap<>();
        this.materialRiskLevelMap = new HashMap<>();
        this.planPeriod = null;
        this.versionCode = null;
        this.oemVOMap = new HashMap<>();
        this.specialOperationShipData = new ArrayList<>();
        this.specialOperationLoadData = new ArrayList<>();
        this.routingStepResourceIds = new ArrayList<>();
        this.outShipData = new ArrayList<>();
        this.addResourceTimeList = new ArrayList<>();
        this.demandOutsourcingPercentage = new HashMap<>();
        this.processOutsourcingPercentage = new HashMap<>();
        this.productOfOemMap = new HashMap<>();
        this.operationInventoryMap = new HashMap<>();
        this.productInventoryMap = new HashMap<>();
        this.endingInventoryMap = new HashMap<>();
        this.productOfSourceProductMap = new HashMap<>();
        this.now = null;
        this.deliveryPlanStart = null;
        this.routingStepIdMap = new HashMap<>();
        this.exceptionPOList = new ArrayList<>();
        this.routingStepVOMap = new HashMap<>();
        this.safetyProductCodeList = new ArrayList<>();
        this.oemTransportTimeVOMap = new HashMap<>();
        this.cleanForecastFirstDay = null;
        this.resourceCodes = new ArrayList<>();
    }
}
