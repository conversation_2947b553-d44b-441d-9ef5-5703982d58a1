package com.yhl.scp.mps.capacityBalance.service;

import com.yhl.scp.mps.capacityBalance.dto.CapacityBalanceAlgorithmDataDTO;

import java.util.List;

/**
 * <code>capacityBalanceAlgorithmDataService</code>
 * <p>
 * 产能平衡算法参数获取
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-20 14:46:23
 */
public interface CapacityBalanceAlgorithmDataService {
    /**
     * 组装产能计算参数
     * @return
     */
    CapacityBalanceAlgorithmDataDTO capacityBalanceAlgorithmData(String scenario, String capacityPeriod, boolean changeSynchro, List<String> log);

}
