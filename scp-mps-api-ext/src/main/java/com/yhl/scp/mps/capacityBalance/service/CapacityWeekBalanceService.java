package com.yhl.scp.mps.capacityBalance.service;

import com.yhl.platform.common.Pagination;
import com.yhl.scp.mps.capacityBalance.dto.CapacityLoadDTO;
import com.yhl.scp.mps.capacityBalance.dto.CapacitySupplyRelationshipWeekEditorDTO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>CapacityWeekBalance</code>
 * <p>
 * 周产能平衡页面查询相关接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-25 13:43:11
 */
public interface CapacityWeekBalanceService {

    /**
     * 设备负荷-设备-周次维度
     * @param pagination
     * @param sortParam
     * @param queryCriteriaParam
     * @return
     */
    List<CapacityLoadVO> selectResourceByPage(Pagination pagination, String sortParam, String queryCriteriaParam);

    /**
     * 设备负荷-工序-周次维度
     * @param pagination
     * @param sortParam
     * @param queryCriteriaParam
     * @return
     */
    List<CapacityLoadVO> selectOperationByPage(Pagination pagination, String sortParam, String queryCriteriaParam);

    /**
     * 周产能供应关系
     * @param pagination
     * @param sortParam
     * @param queryCriteriaParam
     * @return
     */
    List<CapacitySupplyRelationshipVO> selectSupplyByPage(Pagination pagination, String sortParam, String queryCriteriaParam);

    List<CapacityLoadVO> selectOverloadByPage(String resourceCode, Integer utilization);

    /**
     *
     * @param response 消息响应体
     * @param utilization 符合标准率
     */
    void exportData(HttpServletResponse response, Integer utilization);

    /**
     * 发送预警信息
     *
     * @param dtoList
     */
    void sendMessage(List<CapacityLoadDTO> dtoList);


    /**
     * 周产能平衡数据单行编辑
     * @param dto
     */
    void doLineEdit(CapacitySupplyRelationshipWeekEditorDTO dto);


    /**
     * 周产能平衡数据批量编辑
     * @param dto
     */
    void doBatchEdit(CapacitySupplyRelationshipWeekEditorDTO dto);

    /**
     * 批量解锁-锁定
     * @param dto
     */
    void doLockBatch(CapacitySupplyRelationshipWeekEditorDTO dto);

}
