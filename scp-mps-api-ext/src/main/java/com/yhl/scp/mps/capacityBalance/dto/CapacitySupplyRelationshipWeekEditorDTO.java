package com.yhl.scp.mps.capacityBalance.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <code>CapacitySupplyRelationshipWeekEditorDTO</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 14:42:58
 */
@ApiModel(value = "周产能供应关系-编辑DTO")
@Data
public class CapacitySupplyRelationshipWeekEditorDTO implements Serializable {


    private static final long serialVersionUID = 6980095496639883398L;

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "主键ID集合")
    private List<String> ids;

    @ApiModelProperty(value = "新供应数量")
    private BigDecimal newSupplyQuantity;

    @ApiModelProperty(value = "供应时间")
    private Date newSupplyTime;

    @ApiModelProperty(value = "新资源Id")
    private String newResourceId;

}
