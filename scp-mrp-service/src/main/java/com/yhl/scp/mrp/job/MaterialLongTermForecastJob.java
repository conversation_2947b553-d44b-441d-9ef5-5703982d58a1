package com.yhl.scp.mrp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mrp.material.forecast.service.MaterialLongTermForecastService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <code>MaterialLongTermForecastJob</code>
 * <p>
 * 长期预测定时（下发SRM）周一上午十点
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-28 17:28:14
 */
@Component
@Slf4j
public class MaterialLongTermForecastJob {

    @Resource
    private MaterialLongTermForecastService materialLongTermForecastService;

    
    @Resource
    private IpsNewFeign ipsNewFeign;

    @XxlJob("materialLongTermForecastJobHandler")
//    @Scheduled(cron = "0 0 10 ? * MON")
    public ReturnT<String> materialPurchaseRequestJobHandler() {
        XxlJobHelper.log("长期预测下发SRM开始");
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MRP.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在MRP模块信息");
            return ReturnT.SUCCESS;
        }
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("接收調度中心参数...[{}]", jobParam);
        MaterialLongTermForecastJob job = SpringBeanUtils.getBean("materialLongTermForecastJob");
        for (Scenario scenario : scenarios) {
        	DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            job.doJob();
            DynamicDataSourceContextHolder.clearDataSource();
        }
        XxlJobHelper.log("长期预测下发SRM结束");
        return ReturnT.SUCCESS;
    }

    @BusinessMonitorLog(businessCode = "长期预测下发SRM", moduleCode = "MRP", businessFrequency = "DAY")
    public void doJob() {
        try {
            materialLongTermForecastService.issue(new ArrayList<>(), "",false);
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.log("长期预测下发SRM异常", e.getMessage());
            throw e;
        }
    }


}
