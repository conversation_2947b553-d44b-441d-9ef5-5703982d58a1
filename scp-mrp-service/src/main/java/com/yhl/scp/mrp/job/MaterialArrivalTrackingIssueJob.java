package com.yhl.scp.mrp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * <code>MaterialArrivalTrackingIssueJob</code>
 * <p>
 * 到货跟踪下发定时任务（每天早上八点）
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31 15:32:45
 */
@Component
@Slf4j
public class MaterialArrivalTrackingIssueJob {

    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @XxlJob("materialArrivalTrackingIssueJobHandler")
    public ReturnT<String> materialArrivalTrackingIssueJobHandler() {
        XxlJobHelper.log("到货跟踪下发定时下发SRM开始");
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MRP.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在MRP模块信息");
            return ReturnT.SUCCESS;
        }
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("接收調度中心参数...[{}]", jobParam);
        for (Scenario scenario : scenarios) {
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            try {
                materialArrivalTrackingService.issue(new ArrayList<>(),false);
            } catch (Exception e) {
                e.printStackTrace();
                XxlJobHelper.log("到货跟踪下发定时下发SRM异常", e.getMessage());
                return ReturnT.FAIL;
            }
            DynamicDataSourceContextHolder.clearDataSource();
        }
        XxlJobHelper.log("到货跟踪下发定时下发SRM结束");
        return ReturnT.SUCCESS;
    }
}
