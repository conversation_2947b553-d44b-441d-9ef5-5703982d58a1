package com.yhl.scp.mrp.MrpFeign;

import cn.hutool.core.map.MapUtil;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.*;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMaterialDeliveryNote;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMaterialPurchaseStorage;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesReturnedPurchase;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.srm.SrmSupplierPurchase;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassDetailService;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassShippedDetailService;
import com.yhl.scp.mrp.inventory.service.InventoryOceanFreightService;
import com.yhl.scp.mrp.inventory.service.InventoryQuayDetailService;
import com.yhl.scp.mrp.inventory.vo.InventoryOceanFreightShippedMapVO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.arrival.service.MaterialDeliveryNoteService;
import com.yhl.scp.mrp.material.arrival.service.MaterialPurchaseStorageService;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanNeedFeedbackDTO;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanNeedFeedbackDetailsDTO;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanNeedFeedbackDetailsService;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanNeedService;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanReplaceService;
import com.yhl.scp.mrp.material.plan.service.impl.NoGlassMrpServiceImpl;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanReplaceVO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequestService;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequirementDetailService;
import com.yhl.scp.mrp.material.purchase.service.MaterialReturnedPurchaseService;
import com.yhl.scp.mrp.material.transactions.service.MaterialTransactionsService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.order.service.PurchaseOrderInfoService;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassAFactoryReportService;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassAReportService;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassBDedicatedReportService;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassBFactoryReportService;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassAFactoryReportVO;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassAReportVO;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBDedicatedReportVO;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBFactoryReportVO;
import com.yhl.scp.mrp.substitutionRelationship.service.GlassSubstitutionRelationshipService;
import com.yhl.scp.mrp.substitutionRelationship.vo.GlassSubstitutionRelationshipVO;
import com.yhl.scp.mrp.supplier.service.MaterialSupplierPurchaseService;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MrpFeignController</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-12 14:22:27
 */
@Slf4j
@Api(tags = "Feign")
@RestController
public class MrpFeignController implements MrpFeign{

    @Resource
    private MaterialSupplierPurchaseService materialSupplierPurchaseService;

	@Resource
	private MaterialPlanReplaceService materialPlanReplaceService;


	@Resource
	private InventoryOceanFreightService inventoryOceanFreightService;

	@Resource
	private MaterialPurchaseStorageService materialPurchaseStorageService;

	@Resource
	private MaterialDeliveryNoteService materialDeliveryNoteService;

	@Resource
	private MaterialPurchaseRequestService materialPurchaseRequestService;

	@Resource
	private MaterialPurchaseRequirementDetailService materialPurchaseRequirementDetailService;


    @Resource
    private InventoryFloatGlassDetailService inventoryFloatGlassDetailService;

    @Resource
    private MaterialSupplierPurchaseService supplierPurchaseService;

    @Resource
    private PurchaseOrderInfoService purchaseOrderInfoService;

    @Resource
    private InventoryFloatGlassShippedDetailService inventoryFloatGlassShippedDetailService;

	@Resource
	private MaterialPlanNeedService materialPlanNeedService;

	@Resource
	private NoGlassMrpServiceImpl noGlassMrpService;

	@Resource
	private InventoryQuayDetailService inventoryQuayDetailService;

	@Resource
	private MaterialReturnedPurchaseService materialReturnedPurchaseService;

	@Resource
	private MaterialArrivalTrackingService materialArrivalTrackingService;

	@Resource
	private GlassSubstitutionRelationshipService glassSubstitutionRelationshipService;

	@Resource
	private MaterialTransactionsService materialTransactionsService;

	@Resource
	private MaterialPlanNeedFeedbackDetailsService materialPlanNeedFeedbackDetailsService;

    @Resource
    private MaterialGrossDemandService materialGrossDemandService;

	@Resource
	private VehicleInventoryClassBDedicatedReportService vehicleInventoryClassBDedicatedReportService;

	@Resource
	private VehicleInventoryClassBFactoryReportService vehicleInventoryClassBFactoryReportService;

	@Resource
	private VehicleInventoryClassAReportService vehicleInventoryClassADedicatedReportService;

	@Resource
	private VehicleInventoryClassAFactoryReportService vehicleInventoryClassAFactoryReportService;

	@Override
    public List<MaterialSupplierPurchaseVO> selectMaterialSupplierPurchaseVOByStockPointCode(String scenario, List<String> StockPointCodeList) {
        return materialSupplierPurchaseService.selectMaterialSupplierPurchaseVOByStockPointCode(StockPointCodeList);
    }

	@Override
	public List<MaterialSupplierPurchaseVO> selectMaterialSupplierPurchaseByProductCodeList(String scenario,
			List<String> productCodeList) {
		HashMap<String, Object> queryMap = MapUtil.of("materialCodeList", productCodeList);
		queryMap.put("enabled", YesOrNoEnum.YES.getCode());
		DynamicDataSourceContextHolder.setDataSource(scenario);
		List<MaterialSupplierPurchaseVO> mspVOList = materialSupplierPurchaseService.selectByParams(queryMap);
		DynamicDataSourceContextHolder.clearDataSource();
		return mspVOList;
	}

	@Override
	public List<MaterialPlanReplaceVO> selectAllReplacePlan(String scenario) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		List<MaterialPlanReplaceVO> materialPlanReplaceVOS = materialPlanReplaceService.selectAll();
		DynamicDataSourceContextHolder.clearDataSource();
		return materialPlanReplaceVOS;
	}


	@Override
	public BaseResponse<Void> syncMaterialPurchaseStorage(String scenario, List<MesMaterialPurchaseStorage> materialPurchaseStorages) {
		return materialPurchaseStorageService.syncMaterialPurchaseStorage(scenario, materialPurchaseStorages);
	}

	@Override
	public BaseResponse<Void> handleSupplierPurchase(String scenario, List<SrmSupplierPurchase> srmSupplierPurchases) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		BaseResponse<Void> voidBaseResponse =
				materialSupplierPurchaseService.handleSupplierPurchase(srmSupplierPurchases,scenario);
		DynamicDataSourceContextHolder.clearDataSource();
		return voidBaseResponse;
	}

	@Override
	public BaseResponse<Void> handleInventoryOceanFreight(String scenario, List<InventoryOceanFreightShippedMapVO> o) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		BaseResponse<Void> voidBaseResponse = inventoryOceanFreightService.handleInventoryOceanFreight(o);
		DynamicDataSourceContextHolder.clearDataSource();
		return voidBaseResponse;
	}

	@Override
	public BaseResponse<Void> handleMaterialDeliveryNote(String scenario, List<MesMaterialDeliveryNote> mesMaterialDeliveryNotes) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		BaseResponse<Void> voidBaseResponse =
				materialDeliveryNoteService.handleMaterialDeliveryNote(mesMaterialDeliveryNotes,scenario);
		DynamicDataSourceContextHolder.clearDataSource();
		return voidBaseResponse;
	}

	@Override
	public BaseResponse<Void> handlePrQuery(String scenario, List<ErpPrQuery> erpPrQuery) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		BaseResponse<Void> voidBaseResponse = materialPurchaseRequestService.handleMaterialPurchaseRequest(erpPrQuery);
		DynamicDataSourceContextHolder.clearDataSource();
		return voidBaseResponse;
	}

	@Override
	public BaseResponse<Void> handlePrCreate(String scenario, List<ErpPrCreate> erpPrCreateList) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		BaseResponse<Void> voidBaseResponse = materialPurchaseRequirementDetailService.handleMaterialPurchaseRequirementDetail(erpPrCreateList);
		DynamicDataSourceContextHolder.clearDataSource();
		return voidBaseResponse;
	}
	@Override
	public BaseResponse<Void> handlePoCreate(String scenario, List<ErpPoCreate> o) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		BaseResponse<Void> voidBaseResponse = inventoryFloatGlassShippedDetailService.handlePoCreate(scenario,o);
		DynamicDataSourceContextHolder.clearDataSource();
		return voidBaseResponse;
	}
	@Override
	public BaseResponse<Void> handlePoCreateQuay(String scenario, List<ErpPoCreate> o) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		BaseResponse<Void> voidBaseResponse = inventoryOceanFreightService.handlePoCreate(scenario,o);
		DynamicDataSourceContextHolder.clearDataSource();
		return voidBaseResponse;
	}
    @Override
    public BaseResponse<Void> handleOriginalFilmFFInventory(String scenario, List<ErpOriginalFilmFFInventory> o) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		BaseResponse<Void> sync = inventoryFloatGlassDetailService.sync(scenario, o);
		DynamicDataSourceContextHolder.clearDataSource();
		return sync;
    }

    @Override
    public BaseResponse<Void> handlePurchaseOrder(String scenario, List<ErpPurchaseOrder> o) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		BaseResponse<Void> sync = purchaseOrderInfoService.sync(scenario, o);
		DynamicDataSourceContextHolder.clearDataSource();
		return sync;
	}

    @Override
    public BaseResponse<Void> handleOriginalFilmFFOnway(String scenario, List<ErpOriginalFilmFFOnway> o) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		BaseResponse<Void> sync = inventoryFloatGlassShippedDetailService.sync(scenario, o);
		DynamicDataSourceContextHolder.clearDataSource();
		return sync;
    }

	@Override
	public BaseResponse<Void> receiveData(String scenario,List<MaterialPlanNeedFeedbackDTO> list) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		BaseResponse<Void> sync = materialPlanNeedService.doReceiveData(scenario, list);
		DynamicDataSourceContextHolder.clearDataSource();
		return sync;
	}

	@Override
	public BaseResponse<Void> handleReturnedPurchase(String scenario,List<MesReturnedPurchase> list) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		BaseResponse<Void> sync = materialReturnedPurchaseService.handleReturnedPurchase(list);
		DynamicDataSourceContextHolder.clearDataSource();
		return sync;
	}

	@Override
	public BaseResponse<Void> handlePrCancel(String scenario, ErpResponse response) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		BaseResponse<Void> sync = materialArrivalTrackingService.handlePrCancel(response);
		DynamicDataSourceContextHolder.clearDataSource();
		return sync;
	}

	@Override
	public BaseResponse<Void> handlePoClosed(String scenario, ErpResponse response) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		BaseResponse<Void> sync = materialArrivalTrackingService.handlePoClosed(response);
		DynamicDataSourceContextHolder.clearDataSource();
		return sync;
	}

	@Override
	public void syncGlassSubstitutionRelationship(String scenario) {
		glassSubstitutionRelationshipService.syncGlassSubstitutionRelationshipByBom(scenario);
	}

	public BaseResponse<Void> handleMaterialTransactions(String scenario, List<ErpMaterialTransactions> list) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		BaseResponse<Void> sync = materialTransactionsService.handleMaterialTransactions(list);
		DynamicDataSourceContextHolder.clearDataSource();
		return sync;
	}

	@Override
	public void addMaterialPlanNeedFeedbackDetails(String scenario, List<MaterialPlanNeedFeedbackDetailsDTO> list) {
		materialPlanNeedFeedbackDetailsService.doCreateBatch(list);
	}

	@Override
	public List<GlassSubstitutionRelationshipVO> selectGlassSubstitutionRelationshipByParams(String scenario, Map<String, Object> params) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipVOList = glassSubstitutionRelationshipService.selectByParams(params);
		DynamicDataSourceContextHolder.clearDataSource();
		return glassSubstitutionRelationshipVOList;
	}

    @Override
    public List<MaterialGrossDemandVO> selectMaterialGrossDemandDataConsistentCheck(String scenario) {
        return materialGrossDemandService.dataConsistentCheck();
    }

	@Override
	public List<VehicleInventoryClassBDedicatedReportVO> selectVehicleInventoryClassBDedicatedReportByParams(String scenario, Map<String, Object> params) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		List<VehicleInventoryClassBDedicatedReportVO> result = vehicleInventoryClassBDedicatedReportService.selectByParams(params);
		DynamicDataSourceContextHolder.clearDataSource();
		return result;
	}

	@Override
	public List<VehicleInventoryClassBFactoryReportVO> selectVehicleInventoryClassBFactoryReportByParams(String scenario, Map<String, Object> params) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		List<VehicleInventoryClassBFactoryReportVO> result = vehicleInventoryClassBFactoryReportService.selectByParams(params);
		DynamicDataSourceContextHolder.clearDataSource();
		return result;
	}

	@Override
	public List<VehicleInventoryClassAReportVO> selectVehicleInventoryClassAReportByParams(String scenario, Map<String, Object> params) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		List<VehicleInventoryClassAReportVO> result = vehicleInventoryClassADedicatedReportService.selectByParams(params);
		DynamicDataSourceContextHolder.clearDataSource();
		return result;
	}

	@Override
	public List<VehicleInventoryClassAFactoryReportVO> selectVehicleInventoryClassAFactoryReportByParams(String scenario, Map<String, Object> params) {
		DynamicDataSourceContextHolder.setDataSource(scenario);
		List<VehicleInventoryClassAFactoryReportVO> result = vehicleInventoryClassAFactoryReportService.selectByParams(params);
		DynamicDataSourceContextHolder.clearDataSource();
		return result;
	}
}
