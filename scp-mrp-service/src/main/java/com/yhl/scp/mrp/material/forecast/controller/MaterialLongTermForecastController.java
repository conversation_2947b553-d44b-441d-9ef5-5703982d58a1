package com.yhl.scp.mrp.material.forecast.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.enums.RedisKeyManageEnum;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mrp.material.forecast.dto.MaterialLongTermForecastDTO;
import com.yhl.scp.mrp.material.forecast.dto.MaterialLongTermForecastParam;
import com.yhl.scp.mrp.material.forecast.service.MaterialLongTermForecastService;
import com.yhl.scp.mrp.material.forecast.vo.MaterialLongTermForecastVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MaterialLongTermForecastController</code>
 * <p>
 * 材料长期预测控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-22 15:45:38
 */
@Slf4j
@Api(tags = "材料长期预测控制器")
@RestController
@RequestMapping("materialLongTermForecast")
public class MaterialLongTermForecastController extends BaseController {

    @Resource
    private MaterialLongTermForecastService materialLongTermForecastService;

    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<MaterialLongTermForecastVO>> page() {
        List<MaterialLongTermForecastVO> materialLongTermForecastList = materialLongTermForecastService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialLongTermForecastVO> pageInfo = new PageInfo<>(materialLongTermForecastList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "分页查询2")
    @PostMapping(value = "page2")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<MaterialLongTermForecastVO>> page2(@RequestBody MaterialLongTermForecastParam materialLongTermForecastParam) {
        PageInfo<MaterialLongTermForecastVO> materialGrossDemandVOPageInfo = materialLongTermForecastService.selectByPage2(materialLongTermForecastParam);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialGrossDemandVOPageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialLongTermForecastDTO materialLongTermForecastDTO) {
        return materialLongTermForecastService.doCreate(materialLongTermForecastDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialLongTermForecastDTO materialLongTermForecastDTO) {
        return materialLongTermForecastService.doUpdate(materialLongTermForecastDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialLongTermForecastService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MaterialLongTermForecastVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialLongTermForecastService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "下发")
    @PostMapping(value = "issue")
    @BusinessMonitorLog(businessCode = "材料长期预测发布", moduleCode = "MRP", businessFrequency = "WEEK")
    public BaseResponse<Void> issue(@RequestParam("type") String type,
                                    @RequestBody List<String> ids) {
        String redisKey = String.join("#", RedisKeyManageEnum.LONG_TERM_FORECAST_PUBLISH.getKey());
        try {
            if (Boolean.TRUE.equals(redisUtil.hasKey(redisKey))) {
                return BaseResponse.error("当前已有长期预测正在下发，请等待发布完成");
            }
            redisUtil.set(redisKey, SystemHolder.getUserId(), 60 * 5);
            return materialLongTermForecastService.issue(ids, type,true);
        } catch (Exception e) {
            log.error("下发长期预测失败", e);
            throw new BusinessException("下发长期预测失败,{0}", e.getLocalizedMessage());
        } finally {
            redisUtil.delete(redisKey);
        }
    }
}
