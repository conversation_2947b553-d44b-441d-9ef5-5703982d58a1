package com.yhl.scp.mrp.report.vehicle.convertor;

import com.yhl.scp.mrp.report.vehicle.domain.entity.VehicleInventoryClassAFactoryReportDO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassAFactoryReportDTO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassDataReportDTO;
import com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassAFactoryReportPO;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassAFactoryReportVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>VehicleInventoryClassAFactoryReportConvertor</code>
 * <p>
 * 车型库存管控-A类本厂编码维度报表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-28 10:34:49
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VehicleInventoryClassAFactoryReportConvertor {

    VehicleInventoryClassAFactoryReportConvertor INSTANCE = Mappers.getMapper(VehicleInventoryClassAFactoryReportConvertor.class);

    VehicleInventoryClassAFactoryReportDO dto2Do(VehicleInventoryClassAFactoryReportDTO obj);

    VehicleInventoryClassAFactoryReportDTO do2Dto(VehicleInventoryClassAFactoryReportDO obj);

    List<VehicleInventoryClassAFactoryReportDO> dto2Dos(List<VehicleInventoryClassAFactoryReportDTO> list);

    List<VehicleInventoryClassAFactoryReportDTO> do2Dtos(List<VehicleInventoryClassAFactoryReportDO> list);

    VehicleInventoryClassAFactoryReportVO do2Vo(VehicleInventoryClassAFactoryReportDO obj);

    VehicleInventoryClassAFactoryReportVO po2Vo(VehicleInventoryClassAFactoryReportPO obj);

    List<VehicleInventoryClassAFactoryReportVO> po2Vos(List<VehicleInventoryClassAFactoryReportPO> list);

    VehicleInventoryClassAFactoryReportPO do2Po(VehicleInventoryClassAFactoryReportDO obj);

    VehicleInventoryClassAFactoryReportDO po2Do(VehicleInventoryClassAFactoryReportPO obj);

    VehicleInventoryClassAFactoryReportPO dto2Po(VehicleInventoryClassAFactoryReportDTO obj);

    List<VehicleInventoryClassAFactoryReportPO> dto2Pos(List<VehicleInventoryClassAFactoryReportDTO> obj);

    VehicleInventoryClassAFactoryReportDTO dataDto2Dto(VehicleInventoryClassDataReportDTO data);
}
