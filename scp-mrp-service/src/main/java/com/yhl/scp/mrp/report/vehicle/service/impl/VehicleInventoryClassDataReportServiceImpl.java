package com.yhl.scp.mrp.report.vehicle.service.impl;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dfp.clean.vo.CleanDemandProductInventoryReportVO;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.MrpNewProductStockPoint.infrastructure.dao.MrpNewProductStockPointDao;
import com.yhl.scp.mrp.enums.ArrivalStatusEnum;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.plan.enums.MrpDemandSourceEnum;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftDataService;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftDetailService;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandVersionService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.mrpProductBom.infrastructure.dao.MrpProductBomDao;
import com.yhl.scp.mrp.mrpProductBom.infrastructure.dao.MrpProductBomVersionDao;
import com.yhl.scp.mrp.report.vehicle.domain.service.VehicleInventoryClassBFactoryReportDomainService;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassDataReportDTO;
import com.yhl.scp.mrp.report.vehicle.infrastructure.dao.VehicleInventoryClassBFactoryReportDao;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassAReportService;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassDataReportService;
import com.yhl.scp.mrp.supplier.enums.MaterialTypeEnum;
import com.yhl.scp.mrp.supplier.service.MaterialSupplierPurchaseService;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>VehicleInventoryClassDataReportServiceImpl</code>
 * <p>
 * 车型库存数据报表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-29 14:33:57
 */
@Slf4j
@Service
public class VehicleInventoryClassDataReportServiceImpl implements VehicleInventoryClassDataReportService {

    @Resource
    private VehicleInventoryClassAReportService vehicleInventoryClassAReportService;

    @Resource
    private MaterialSupplierPurchaseService materialSupplierPurchaseService;

    @Resource
    private MaterialGrossDemandService materialGrossDemandService;

    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;

    @Resource
    private NoGlassInventoryShiftDataService noGlassInventoryShiftDataService;

    @Resource
    private NoGlassInventoryShiftDetailService noGlassInventoryShiftDetailService;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private MrpNewProductStockPointDao mrpNewProductStockPointDao;

    @Resource
    private MrpProductBomVersionDao mrpProductBomVersionDao;

    @Resource
    private MrpProductBomDao mrpProductBomDao;

    @Override
    public void assembleVehicleInventoryFactoryReport(List<String> materialTypeList,
                                                      List<VehicleInventoryClassDataReportDTO> dataList) throws Exception {
        String scenario = SystemHolder.getScenario();

        List<String> threeMonths = getNextThreeMonths(new Date(), "yyyyMM");
        List<String> threeMonths02 = getNextThreeMonths(new Date(), "yyyy-MM");

        // 查询需求版本（最新的）
        DemandVersionVO latestVersion = dfpFeign.selectDemandVersionVOListByParams(
                        scenario,
                        ImmutableMap.of("versionType", VersionTypeEnum.CLEAN_DEMAND.getCode(), "planPeriod", ""))
                .stream().max(Comparator.comparing(DemandVersionVO::getCreateTime, Comparator.nullsLast(Comparator.naturalOrder())))
                .orElse(null);
        if (Objects.isNull(latestVersion)) {
            log.error("需求版本为空");
            return;
        }

        // 查询风险车型管控-产品库存
        List<CleanDemandProductInventoryReportVO> cleanDemandProductInventoryReportVOList = dfpFeign.selectProductInventoryReportListByParams(
                scenario,
                ImmutableMap.of("versionId", latestVersion.getId()));
        if (CollectionUtils.isEmpty(cleanDemandProductInventoryReportVOList)) {
            log.error("风险车型管控-产品库存数据为空");
            return;
        }
        Map<String, CleanDemandProductInventoryReportVO> cleanDemandProductInventoryReportVOMap = cleanDemandProductInventoryReportVOList.stream()
                .collect(Collectors.toMap(CleanDemandProductInventoryReportVO::getProductCode, Function.identity(), (v1, v2) -> v1));
        List<String> productCodeList = cleanDemandProductInventoryReportVOList.stream().map(CleanDemandProductInventoryReportVO::getProductCode)
                .distinct().collect(Collectors.toList());

        Executor executor = Executors.newFixedThreadPool(2);
        // 查询所有的BOM
        // 异步查询所有的BOM - 开两个独立线程分别查询
        CompletableFuture<List<ProductBomVersionVO>> bomVersionFuture = CompletableFuture.supplyAsync(() -> {
            log.info("开始异步查询ProductBomVersionVO");
            return mrpProductBomVersionDao.selectVOByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));
        }, executor);
        CompletableFuture<List<ProductBomVO>> bomFuture = CompletableFuture.supplyAsync(() -> {
            log.info("开始异步查询ProductBomVO");
            return mrpProductBomDao.selectVOByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));
        }, executor);
        // 等待两个异步查询都完成
        CompletableFuture<Void> allBomQueries = CompletableFuture.allOf(bomVersionFuture, bomFuture);
        // 获取异步查询结果，等待所有BOM查询完成
        allBomQueries.join();
        List<ProductBomVersionVO> productBomVersionVOList = bomVersionFuture.get();
        List<ProductBomVO> productBomVOList = bomFuture.get();

        log.info("BOM异步查询完成，ProductBomVersionVO数量: {}, ProductBomVO数量: {}",
                productBomVersionVOList.size(), productBomVOList.size());

        Map<String, ProductBomVersionVO> productBomVersionVOMapOfProductCode = productBomVersionVOList.stream()
                .collect(Collectors.toMap(ProductBomVersionVO::getProductCode, Function.identity(), (k1, k2) -> k2));
        Map<String, List<ProductBomVO>> productBomGroupOfBomVersionId = productBomVOList.stream()
                .collect(Collectors.groupingBy(ProductBomVO::getBomVersionId));

        // 解析成品所有的辅料信息
        Map<String, List<ProductBomVO>> productBomGroupOfProductCode = new HashMap<>();
        vehicleInventoryClassAReportService.getProductChildBomAll(productCodeList, productBomVersionVOMapOfProductCode, productBomGroupOfBomVersionId,
                productBomGroupOfProductCode, materialTypeList);

        // 收集材料编码
        List<String> materialCodeList = productBomGroupOfProductCode.values().stream()
                .flatMap(Collection::stream)
                .map(ProductBomVO::getProductCode)
                .distinct()
                .collect(Collectors.toList());

        // 查询材料与供应商关系
        List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOList =
                materialSupplierPurchaseService.selectVOByParams(ImmutableMap.of("materialCodeList", materialCodeList));
        Map<String, List<MaterialSupplierPurchaseVO>> materialSupplierPurchaseVOMap = materialSupplierPurchaseVOList.stream()
                .collect(Collectors.groupingBy(MaterialSupplierPurchaseVO::getMaterialCode));

        // 查询实时库存
        List<InventoryBatchDetailVO> inventoryBatchDetailVOList = dfpFeign.selectInventoryBatchDetailVOByParams03(scenario, ImmutableMap.of("productCodes", materialCodeList));
        String currentTime = DateUtils.dateToString(new Date());
        inventoryBatchDetailVOList.forEach(data -> {
            if (null == data.getAssignedTime()) {
                data.setAssignedTime(currentTime);
            }
        });
        Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailVOMap = inventoryBatchDetailVOList.stream()
                .collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));

        Map<String, List<MaterialGrossDemandVO>> grossDemandGroupOfProductCode;
        Map<String, List<MaterialGrossDemandVO>> grossDemandGroupOfVehicleModeCode;

        // 查询毛需求
        List<MaterialGrossDemandVO> materialGrossDemandVOList = materialGrossDemandService.selectByParams(
                ImmutableMap.of("productCodeList", materialCodeList));
        // 根据物料编码分组
        grossDemandGroupOfProductCode = materialGrossDemandVOList.stream()
                .filter(data -> null != data.getProductCode())
                .collect(Collectors.groupingBy(MaterialGrossDemandVO::getProductCode));
        // 根据车型编码分组
        grossDemandGroupOfVehicleModeCode = materialGrossDemandVOList.stream()
                .filter(data -> null != data.getVehicleModeCode())
                .collect(Collectors.groupingBy(MaterialGrossDemandVO::getVehicleModeCode));

        // 查询到货跟踪
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList =
                materialArrivalTrackingService.selectByParams(ImmutableMap.of("materialCodes", materialCodeList));
        Map<String, List<MaterialArrivalTrackingVO>> materialArrivalTrackingVOMap = materialArrivalTrackingVOList.stream()
                .filter(data -> !data.getArrivalStatus().equals(ArrivalStatusEnum.CANCEL.getCode()))
                .filter(data -> !data.getArrivalStatus().equals(ArrivalStatusEnum.CLOSE.getCode()))
                .collect(Collectors.groupingBy(MaterialArrivalTrackingVO::getMaterialCode));

        // 查询非原片推移
        List<NoGlassInventoryShiftDataVO> shiftDataList = noGlassInventoryShiftDataService.selectVOByParams(ImmutableMap.of("productCodeList", materialCodeList));
        Map<String, List<NoGlassInventoryShiftDataVO>> shiftDataMap = shiftDataList.stream().collect(Collectors.groupingBy(NoGlassInventoryShiftDataVO::getProductCode));
        List<String> shiftDataIds = shiftDataList.stream().map(BaseVO::getId).collect(Collectors.toList());
        Map<String, List<NoGlassInventoryShiftDetailVO>> shiftDetailMap = noGlassInventoryShiftDetailService.selectVOByParams(
                        ImmutableMap.of("noGlassInventoryShiftDataIdList", shiftDataIds)).stream()
                .filter(detail -> detail.getNoGlassInventoryShiftDataId() != null)
                .collect(Collectors.groupingBy(NoGlassInventoryShiftDetailVO::getNoGlassInventoryShiftDataId, HashMap::new, Collectors.toList()));

        // 查询需求预测版本
        List<ConsistenceDemandForecastVersionVO> allForecastVersions = dfpFeign.selectConsistenceDemandForecastVersion(scenario);
        Map<String, ConsistenceDemandForecastVersionVO> forecastVersionMap = allForecastVersions.stream()
                .filter(v -> threeMonths.contains(v.getPlanPeriod()))
                .collect(Collectors.groupingBy(ConsistenceDemandForecastVersionVO::getPlanPeriod))
                .values().stream()
                .map(vs -> vs.stream().max(Comparator.comparing(BaseVO::getCreateTime)).orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ConsistenceDemandForecastVersionVO::getPlanPeriod, Function.identity()));
        List<String> versionIds = forecastVersionMap.values().stream().map(BaseVO::getId).collect(Collectors.toList());

        // 查询需求预测明细
        List<ConsistenceDemandForecastDataDetailVO> forecastDetails = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(versionIds)) {
            List<ConsistenceDemandForecastDataVO> forecastDataList = dfpFeign.selectDemandForecastDataByParams(
                    scenario, ImmutableMap.of("versionIdList", versionIds, "productCodeList", productCodeList));
            List<String> forecastDataIds = forecastDataList.stream().map(BaseVO::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(forecastDataIds)) {
                forecastDetails = dfpFeign.selectDemandForecastByDataDetailVOByParams(
                        scenario, ImmutableMap.of("consistenceDemandForecastDataIdList", forecastDataIds));
            }
        }
        Map<String, List<ConsistenceDemandForecastDataDetailVO>> forecastDetailMap = forecastDetails.stream()
                .filter(d -> d.getProductCode() != null)
                .collect(Collectors.groupingBy(ConsistenceDemandForecastDataDetailVO::getProductCode));

        // 查询物料基础
        List<NewProductStockPointVO> newProductStockPointVOS = mrpNewProductStockPointDao.selectColumnVOByParams(ImmutableMap.of("productCodes", materialCodeList));
        Map<String, NewProductStockPointVO> productStockPointVOMap = newProductStockPointVOS.stream()
                .filter(data -> null != data.getItemCost())
                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (v1, v2) -> v1));

        for (Map.Entry<String, CleanDemandProductInventoryReportVO> entry : cleanDemandProductInventoryReportVOMap.entrySet()) {
            String key = entry.getKey();
            CleanDemandProductInventoryReportVO value = entry.getValue();

            // 获取材料编码
            List<ProductBomVO> productBomVOS = productBomGroupOfProductCode.get(key);
            if (null == productBomVOS) {
                VehicleInventoryClassDataReportDTO dto = new VehicleInventoryClassDataReportDTO();
                dto.setCustomer(value.getCustomerAbbreviation());
                dto.setVehicleModelCode(value.getVehicleModelCode());
                dto.setProductFactoryCode(value.getProductCode());
                dataList.add(dto);
                continue;
            }

            for (ProductBomVO productBomVO : productBomVOS) {
                VehicleInventoryClassDataReportDTO dto = new VehicleInventoryClassDataReportDTO();
                dto.setCustomer(value.getCustomerAbbreviation());
                dto.setVehicleModelCode(value.getVehicleModelCode());
                dto.setProductFactoryCode(value.getProductCode());
                dto.setProductCode(productBomVO.getProductCode());
                dto.setProductName(productBomVO.getProductName());
                dto.setProductType(productBomVO.getProductCategory());
                dto.setInputFactor(productBomVO.getIoFactor());
                dto.setUnit(productBomVO.getMeasurementUnit());
                dto.setOutputRate(productBomVO.getYield());

                // 获取材料与供应商关系
                List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOS = materialSupplierPurchaseVOMap.get(dto.getProductCode());
                if (CollectionUtils.isNotEmpty(materialSupplierPurchaseVOS)) {
                    dto.setPurchaseLot(materialSupplierPurchaseVOS.stream()
                            .map(MaterialSupplierPurchaseVO::getOrderPlacementLeadTimeDay)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                    dto.setSupplierCode(materialSupplierPurchaseVOS.stream()
                            .map(MaterialSupplierPurchaseVO::getSupplierCode)
                            .collect(Collectors.joining(",")));
                }

                // 获取实时库存
                List<InventoryBatchDetailVO> inventoryBatchDetailVOS = inventoryBatchDetailVOMap.get(dto.getProductCode());
                if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                    InventoryBatchDetailVO inventoryBatchDetailVO = inventoryBatchDetailVOS.stream()
                            .max(Comparator.comparing(InventoryBatchDetailVO::getAssignedTime))
                            .orElse(null);
                    // 库龄天数（当前时间 - 入库时间）
                    if (null != inventoryBatchDetailVO && inventoryBatchDetailVO.getAssignedTime() != null) {
                        Date assignedDate = DateUtils.stringToDate(inventoryBatchDetailVO.getAssignedTime());
                        Date currentDate = new Date();
                        // 计算时间差（毫秒）
                        long diffInMillis = currentDate.getTime() - assignedDate.getTime();
                        // 转换为天数（向上取整为整数）
                        long days = (long) Math.ceil((double) diffInMillis / (24 * 60 * 60 * 1000));
                        dto.setStockAge(BigDecimal.valueOf(days));
                    }
                }

                // 获取非原片推移
                List<NoGlassInventoryShiftDataVO> noGlassInventoryShiftDataVOs = shiftDataMap.get(dto.getProductCode());
                if (CollectionUtils.isNotEmpty(noGlassInventoryShiftDataVOs)) {
                    BigDecimal openingInventory = noGlassInventoryShiftDataVOs.stream()
                            .map(data -> {
                                List<NoGlassInventoryShiftDetailVO> detailVOS = shiftDetailMap.get(data.getId());
                                if (CollectionUtils.isNotEmpty(detailVOS)) {
                                    return detailVOS.stream()
                                            .map(NoGlassInventoryShiftDetailVO::getOpeningInventory)
                                            .filter(Objects::nonNull)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                                }
                                return BigDecimal.ZERO;
                            })
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    dto.setFactoryInventory(openingInventory);
                }

                // 获取到货跟踪
                List<MaterialArrivalTrackingVO> materialArrivalTrackingVOs = materialArrivalTrackingVOMap.get(dto.getProductCode());
                if (CollectionUtils.isNotEmpty(materialArrivalTrackingVOs)) {
                    BigDecimal supplierInventory = materialArrivalTrackingVOs.stream()
                            .peek(data -> {
                                if (null == data.getWaitDeliveryQuantity()) {
                                    data.setWaitDeliveryQuantity(BigDecimal.ZERO);
                                }
                                if (null == data.getPredictArrivalQuantity()) {
                                    data.setPredictArrivalQuantity(BigDecimal.ZERO);
                                }
                            })
                            .map(data -> data.getWaitDeliveryQuantity().add(data.getPredictArrivalQuantity()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    dto.setSupplierInventory(supplierInventory);
                }

                // （本厂库存 + 福耀库存） ÷ 单耗 × 产出率 向下取整
                if (dto.getFactoryInventory().compareTo(BigDecimal.ZERO) > 0 && dto.getSupplierInventory().compareTo(BigDecimal.ZERO) > 0) {
                    dto.setCurrentStockPartProdQuantity((dto.getFactoryInventory()
                            .add(dto.getSupplierInventory()))
                            .divide(dto.getInputFactor(), 0, RoundingMode.DOWN)
                            .multiply(dto.getOutputRate()));
                }

                // 获取毛需求（物料维度）
                List<MaterialGrossDemandVO> materialGrossDemandVOS = grossDemandGroupOfProductCode.get(dto.getProductCode());
                if (CollectionUtils.isNotEmpty(materialGrossDemandVOS)) {
                    BigDecimal threeMonthsDemandQuantity = materialGrossDemandVOS.stream()
                            .filter(data -> threeMonths02.contains(DateUtils.dateToString(data.getDemandTime(), "yyyy-MM")))
                            .map(MaterialGrossDemandVO::getDemandQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal totalDemandQuantity = materialGrossDemandVOS.stream()
                            .map(MaterialGrossDemandVO::getDemandQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal predictedProportion = threeMonthsDemandQuantity.divide(totalDemandQuantity, 2, RoundingMode.DOWN);
                    dto.setMaterialPredictedProportion(predictedProportion + "%");

                    // 获取后30天日期
                    List<String> next30Days = getNext30Days(new Date());
                    BigDecimal threeMonthsDemandQuantity02 = materialGrossDemandVOS.stream()
                            .filter(data -> next30Days.contains(DateUtils.dateToString(data.getDemandTime(), "yyyy-MM-dd")))
                            .map(MaterialGrossDemandVO::getDemandQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    dto.setNext30DaysDailyAverageDemand(threeMonthsDemandQuantity02.divide(new BigDecimal(30), 0, RoundingMode.DOWN));
                }

                // 获取毛需求（车型维度）
                List<MaterialGrossDemandVO> materialGrossDemandVOS02 = grossDemandGroupOfVehicleModeCode.get(dto.getVehicleModelCode());
                if (CollectionUtils.isNotEmpty(materialGrossDemandVOS02)) {
                    BigDecimal threeMonthsDemandQuantity = materialGrossDemandVOS02.stream()
                            .filter(data -> threeMonths02.contains(DateUtils.dateToString(data.getDemandTime(), "yyyy-MM")))
                            .map(MaterialGrossDemandVO::getDemandQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal totalDemandQuantity = materialGrossDemandVOS02.stream()
                            .map(MaterialGrossDemandVO::getDemandQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal predictedProportionVehicle = threeMonthsDemandQuantity
                            .divide(totalDemandQuantity, 2, RoundingMode.DOWN)
                            .multiply(new BigDecimal(100));
                    dto.setMaterialPredictedProportionVehicle(String.format("%.2f%%", predictedProportionVehicle));

                    if (predictedProportionVehicle.compareTo(new BigDecimal(50)) >= 0) {
                        dto.setIsDedicatedGe50pct(YesOrNoEnum.YES.getCode());
                    } else {
                        dto.setIsDedicatedGe50pct(YesOrNoEnum.NO.getCode());
                    }
                }

                // 获取毛需求（物料维度）只取需求来源是 生产计划 或 产能平衡
                List<MaterialGrossDemandVO> materialGrossDemandVOS03 = grossDemandGroupOfProductCode.get(dto.getProductCode());
                if (CollectionUtils.isNotEmpty(materialGrossDemandVOS03) && dto.getFactoryInventory().compareTo(BigDecimal.ZERO) > 0) {
                    materialGrossDemandVOS03 = materialGrossDemandVOS03.stream()
                            .filter(data -> data.getDemandSource().equals(MrpDemandSourceEnum.MPS.getCode()) ||
                                    data.getDemandSource().equals(MrpDemandSourceEnum.MCB.getCode()))
                            .collect(Collectors.toList());

                    BigDecimal demandQuantity = materialGrossDemandVOS03.stream()
                            .map(MaterialGrossDemandVO::getDemandQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    if (demandQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        // 辅料库存 ÷ 毛需求
                        dto.setFactoryInventoryUseDay(dto.getFactoryInventory().divide(demandQuantity, 0, RoundingMode.DOWN).intValue());
                    }
                }

                if (CollectionUtils.isNotEmpty(materialGrossDemandVOS) && dto.getSupplierInventory().compareTo(BigDecimal.ZERO) > 0) {
                    // 获取毛需求覆盖天数
                    long size = materialGrossDemandVOS.stream()
                            .map(MaterialGrossDemandVO::getDemandTime)
                            .distinct()
                            .count();

                    // 供应商库存 ÷ （需求覆盖天数 - 本厂库存可用天数）
                    if (size - dto.getFactoryInventoryUseDay() > 0) {
                        dto.setSupplierInventoryUseDay(dto.getSupplierInventory()
                                .divide(BigDecimal.valueOf((size - dto.getFactoryInventoryUseDay())),
                                        0, RoundingMode.DOWN).intValue());
                    }
                }

                // 福耀库存可用天数 + 供应商库存可用天数
                dto.setMaterialTotalDay(dto.getFactoryInventoryUseDay() + dto.getSupplierInventoryUseDay());

                // 获取一致性需求预测
                List<ConsistenceDemandForecastDataDetailVO> consistenceDemandForecastDataDetailVOs = forecastDetailMap.get(key);
                if (CollectionUtils.isNotEmpty(consistenceDemandForecastDataDetailVOs)) {
                    Date currentDate = new Date();
                    // 获取当前月、下个月、下下个月
                    String currentMonth = DateUtils.dateToString(currentDate, "yyyy-MM");
                    String nextMonth = DateUtils.dateToString(DateUtils.moveMonth(currentDate, 1), "yyyy-MM");
                    String nextNextMonth = DateUtils.dateToString(DateUtils.moveMonth(currentDate, 2), "yyyy-MM");
                    // 汇总到List集合
                    List<String> monthList = new ArrayList<>();
                    monthList.add(currentMonth);
                    monthList.add(nextMonth);
                    monthList.add(nextNextMonth);

                    for (int i = 0; i < monthList.size(); i++) {
                        String date = monthList.get(i);
                        BigDecimal forecastQuantity = consistenceDemandForecastDataDetailVOs.stream()
                                .filter(data -> null != data.getForecastTime())
                                .filter(data -> DateUtils.dateToString(data.getForecastTime(), "yyyy-MM").equals(date))
                                .map(ConsistenceDemandForecastDataDetailVO::getForecastQuantity)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (i == 0) {
                            dto.setCurrentMonthForecastQuantity(forecastQuantity);
                        }
                        if (i == 1) {
                            dto.setNextMonthForecastQuantity(forecastQuantity);
                        }
                        if (i == 2) {
                            dto.setAfterNextMonthForecastQuantity(forecastQuantity);
                        }
                    }
                }

                // 获取物料基础
                NewProductStockPointVO newProductStockPointVO = productStockPointVOMap.get(dto.getProductCode());
                if (Objects.nonNull(newProductStockPointVO)) {
                    // （辅料库存 + 供应商库存）× 物料单价 ÷ 10000
                    dto.setTotalCostAmountThousand((dto.getFactoryInventory().add(dto.getSupplierInventory()))
                            .multiply(new BigDecimal(newProductStockPointVO.getItemCost()))
                            .divide(new BigDecimal(10000), 2, RoundingMode.DOWN));

                    // 辅料库存 × 单价 ÷ 10000
                    dto.setFactoryInventoryAmountThousand(dto.getFactoryInventory()
                            .multiply(new BigDecimal(newProductStockPointVO.getItemCost()))
                            .divide(new BigDecimal(10000), 2, RoundingMode.DOWN));

                    // 供应商库存 × 单价 ÷ 10000
                    dto.setSupplierInventoryAmountThousand(dto.getSupplierInventory()
                            .multiply(new BigDecimal(newProductStockPointVO.getItemCost()))
                            .divide(new BigDecimal(10000), 2, RoundingMode.DOWN));

                    // 未来30天日均需求 × 单价
                    dto.setDailyAverageDemandAmount(dto.getNext30DaysDailyAverageDemand().multiply(new BigDecimal(newProductStockPointVO.getItemCost())));
                }
                // 30 + 采购周期
                dto.setControlStandard(dto.getPurchaseLot().add(new BigDecimal(30)));
                dataList.add(dto);
            }
        }
    }

    /**
     * 获取指定Date日期及其后三个月的月维度日期，返回pattern格式的列表
     *
     * @param date 输入的Date类型日期
     * @return 包含当前月及后三个月的pattern格式字符串列表
     */
    public static List<String> getNextThreeMonths(Date date, String pattern) {
        // 将java.util.Date转换为java.time.LocalDate（使用系统默认时区）
        LocalDate localDate = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        // 创建日期格式化器，用于将日期转换为yyyyMM格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);

        // 创建结果列表
        List<String> result = new ArrayList<>(3);

        // 当前日期对应的月份
        LocalDate currentDate = localDate;

        // 循环4次，获取当前月及后三个月
        for (int i = 0; i < 3; i++) {
            // 格式化当前日期并添加到列表
            result.add(currentDate.format(formatter));
            // 计算下一个月的同一天
            currentDate = currentDate.plusMonths(1);
        }
        return result;
    }

    /**
     * 获取指定日期开始的后30天日期，返回yyyy-MM-dd格式的列表
     *
     * @param date 基准日期（Date类型）
     * @return 包含30天日期的字符串列表
     * @throws IllegalArgumentException 如果输入日期为null
     */
    public static List<String> getNext30Days(Date date) {

        // 转换Date为LocalDate（使用系统默认时区）
        LocalDate localDate = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        // 定义日期格式化器（yyyy-MM-dd）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 存储结果的列表
        List<String> result = new ArrayList<>(30);

        // 循环生成30天的日期
        for (int i = 1; i <= 30; i++) {
            // 格式化当前日期并添加到列表
            result.add(localDate.plusDays(i).format(formatter));
        }

        return result;
    }
}
