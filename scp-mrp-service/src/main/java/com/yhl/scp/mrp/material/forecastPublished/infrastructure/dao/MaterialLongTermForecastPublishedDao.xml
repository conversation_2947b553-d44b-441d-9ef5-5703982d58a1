<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.forecastPublished.infrastructure.dao.MaterialLongTermForecastPublishedDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mrp.material.forecastPublished.infrastructure.po.MaterialLongTermForecastPublishedPO">
        <!--@Table mrp_material_long_term_forecast_published-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="forecast_version_id" jdbcType="VARCHAR" property="forecastVersionId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="demand_date" jdbcType="TIMESTAMP" property="demandDate"/>
        <result column="demand_quantity" jdbcType="VARCHAR" property="demandQuantity"/>
        <result column="supplier_id" jdbcType="VARCHAR" property="supplierId"/>
        <result column="demand_pattern" jdbcType="VARCHAR" property="demandPattern"/>
        <result column="inventory_shift_version_code" jdbcType="VARCHAR" property="inventoryShiftVersionCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.material.forecastPublished.vo.MaterialLongTermForecastPublishedVO">
        <!-- TODO -->
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="organization_id" jdbcType="VARCHAR" property="organizationId"/>
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,forecast_version_id,product_code,demand_date,demand_quantity,supplier_id,demand_pattern,inventory_shift_version_code,remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
        ,supplier_code,supplier_name,product_name,stock_point_code,organization_id,stock_point_name,material_planner
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.forecastVersionId != null and params.forecastVersionId != ''">
                and forecast_version_id = #{params.forecastVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.demandDate != null">
                and demand_date = #{params.demandDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.demandQuantity != null">
                and demand_quantity = #{params.demandQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierId != null and params.supplierId != ''">
                and supplier_id = #{params.supplierId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandPattern != null and params.demandPattern != ''">
                and demand_pattern = #{params.demandPattern,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryShiftVersionCode != null and params.inventoryShiftVersionCode != ''">
                and inventory_shift_version_code = #{params.inventoryShiftVersionCode,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_long_term_forecast_published
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_long_term_forecast_published
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_long_term_forecast_published
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_long_term_forecast_published
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 条件查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_long_term_forecast_published
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectGroupByParams" resultMap="VOResultMap">
        select supplier_id,product_code
        from v_mrp_material_long_term_forecast_published
        <include refid="Base_Where_Condition"/>
        group by supplier_id,product_code
    </select>

    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mrp.material.forecastPublished.infrastructure.po.MaterialLongTermForecastPublishedPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_long_term_forecast_published(
        id,
        forecast_version_id,
        product_code,
        demand_date,
        demand_quantity,
        supplier_id,
        demand_pattern,
        inventory_shift_version_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{forecastVersionId,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{demandDate,jdbcType=TIMESTAMP},
        #{demandQuantity,jdbcType=VARCHAR},
        #{supplierId,jdbcType=VARCHAR},
        #{demandPattern,jdbcType=VARCHAR},
        #{inventoryShiftVersionCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.forecastPublished.infrastructure.po.MaterialLongTermForecastPublishedPO">
        insert into mrp_material_long_term_forecast_published(id,
                                                              forecast_version_id,
                                                              product_code,
                                                              demand_date,
                                                              demand_quantity,
                                                              supplier_id,
                                                              demand_pattern,
                                                              inventory_shift_version_code,
                                                              remark,
                                                              enabled,
                                                              creator,
                                                              create_time,
                                                              modifier,
                                                              modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{forecastVersionId,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{demandDate,jdbcType=TIMESTAMP},
                #{demandQuantity,jdbcType=VARCHAR},
                #{supplierId,jdbcType=VARCHAR},
                #{demandPattern,jdbcType=VARCHAR},
                #{inventoryShiftVersionCode,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_long_term_forecast_published(
        id,
        forecast_version_id,
        product_code,
        demand_date,
        demand_quantity,
        supplier_id,
        demand_pattern,
        inventory_shift_version_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.forecastVersionId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.demandDate,jdbcType=TIMESTAMP},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.supplierId,jdbcType=VARCHAR},
            #{entity.demandPattern,jdbcType=VARCHAR},
            #{entity.inventoryShiftVersionCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_long_term_forecast_published(
        id,
        forecast_version_id,
        product_code,
        demand_date,
        demand_quantity,
        supplier_id,
        demand_pattern,
        inventory_shift_version_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.forecastVersionId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.demandDate,jdbcType=TIMESTAMP},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.supplierId,jdbcType=VARCHAR},
            #{entity.demandPattern,jdbcType=VARCHAR},
            #{entity.inventoryShiftVersionCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mrp.material.forecastPublished.infrastructure.po.MaterialLongTermForecastPublishedPO">
        update mrp_material_long_term_forecast_published
        set forecast_version_id = #{forecastVersionId,jdbcType=VARCHAR},
            product_code        = #{productCode,jdbcType=VARCHAR},
            demand_date         = #{demandDate,jdbcType=TIMESTAMP},
            demand_quantity     = #{demandQuantity,jdbcType=VARCHAR},
            supplier_id         = #{supplierId,jdbcType=VARCHAR},
            demand_pattern      = #{demandPattern,jdbcType=VARCHAR},
            inventory_shift_version_code      = #{inventoryShiftVersionCode,jdbcType=VARCHAR},
            remark              = #{remark,jdbcType=VARCHAR},
            enabled             = #{enabled,jdbcType=VARCHAR},
            modifier            = #{modifier,jdbcType=VARCHAR},
            modify_time         = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.material.forecastPublished.infrastructure.po.MaterialLongTermForecastPublishedPO">
        update mrp_material_long_term_forecast_published
        <set>
            <if test="item.forecastVersionId != null and item.forecastVersionId != ''">
                forecast_version_id = #{item.forecastVersionId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.demandDate != null">
                demand_date = #{item.demandDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierId != null and item.supplierId != ''">
                supplier_id = #{item.supplierId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandPattern != null and item.demandPattern != ''">
                demand_pattern = #{item.demandPattern,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryShiftVersionCode != null and item.inventoryShiftVersionCode != ''">
                inventory_shift_version_code = #{item.inventoryShiftVersionCode,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_long_term_forecast_published
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="forecast_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.forecastVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_pattern = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandPattern,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_shift_version_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryShiftVersionCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_long_term_forecast_published
            <set>
                <if test="item.forecastVersionId != null and item.forecastVersionId != ''">
                    forecast_version_id = #{item.forecastVersionId,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.demandDate != null">
                    demand_date = #{item.demandDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.demandQuantity != null">
                    demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierId != null and item.supplierId != ''">
                    supplier_id = #{item.supplierId,jdbcType=VARCHAR},
                </if>
                <if test="item.demandPattern != null and item.demandPattern != ''">
                    demand_pattern = #{item.demandPattern,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryShiftVersionCode != null and item.inventoryShiftVersionCode != ''">
                    inventory_shift_version_code = #{item.inventoryShiftVersionCode,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_long_term_forecast_published
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_long_term_forecast_published where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
