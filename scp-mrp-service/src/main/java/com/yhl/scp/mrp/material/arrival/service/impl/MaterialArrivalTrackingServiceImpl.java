package com.yhl.scp.mrp.material.arrival.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.rbac.entity.Role;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mrp.enums.*;
import com.yhl.scp.mrp.extension.material.vo.ConsistencyWarningVO;
import com.yhl.scp.mrp.extension.material.vo.MaterialPlanNeedVO;
import com.yhl.scp.mrp.extension.material.vo.MaterialPurchaseRequirementDetailVO;
import com.yhl.scp.mrp.material.arrival.dto.*;
import com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalTrackingBasicPO;
import com.yhl.scp.mrp.material.arrival.service.*;
import com.yhl.scp.mrp.material.arrival.vo.MaterialArrivalTrackingIssueDetailVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialDeliveryNoteVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialPurchaseStorageVO;
import com.yhl.scp.mrp.material.plan.dto.*;
import com.yhl.scp.mrp.material.plan.result.InsertPurchasingArrivalTrackResult;
import com.yhl.scp.mrp.material.plan.result.InsertRollPredictionDayResult;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanNeedIssueDetailsService;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanNeedService;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanNeedIssueDetailsVO;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseRequirementDetailDTO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequestService;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequirementDetailService;
import com.yhl.scp.mrp.material.purchase.service.MaterialReturnedPurchaseService;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseRequestVO;
import com.yhl.scp.mrp.material.purchase.vo.MaterialReturnedPurchaseVO;
import com.yhl.scp.mrp.supplier.dto.MaterialArrivalTrackingBasicDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.material.arrival.convertor.MaterialArrivalTrackingConvertor;
import com.yhl.scp.mrp.material.arrival.domain.entity.MaterialArrivalTrackingDO;
import com.yhl.scp.mrp.material.arrival.domain.service.MaterialArrivalTrackingDomainService;
import com.yhl.scp.mrp.material.arrival.infrastructure.dao.MaterialArrivalTrackingDao;
import com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalTrackingPO;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>MaterialArrivalTrackingServiceImpl</code>
 * <p>
 * 材料到货跟踪应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-21 16:57:38
 */
@Slf4j
@Service
public class MaterialArrivalTrackingServiceImpl extends AbstractService implements MaterialArrivalTrackingService {

    @Resource
    private MaterialArrivalTrackingDao materialArrivalTrackingDao;

    @Resource
    private MaterialArrivalTrackingDomainService materialArrivalTrackingDomainService;

    @Resource
    private MaterialPlanNeedService materialPlanNeedService;

    @Resource
    private MaterialPlanNeedIssueDetailsService materialPlanNeedIssueDetailsService;

    @Resource
    private MaterialPurchaseRequirementDetailService materialPurchaseRequirementDetailService;

    @Resource
    private MaterialDeliveryNoteService materialDeliveryNoteService;

    @Resource
    private MaterialPurchaseStorageService materialPurchaseStorageService;

    @Resource
    private MaterialPurchaseRequestService materialPurchaseRequestService;

    @Resource
    private MaterialReturnedPurchaseService materialReturnedPurchaseService;

    @Resource
    private MaterialArrivalTrackingIssueDetailService materialArrivalTrackingIssueDetailService;

    @Resource
    private MaterialArrivalTrackingIssueVersionService materialArrivalTrackingIssueVersionService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private RedisUtil redisUtil;

    protected static final String ISSUE_REDIS_USER = "MATERIAL_PURCHASE_REQUIREMENT_USER";

    @Override
    public BaseResponse<Void> doCreate(MaterialArrivalTrackingDTO materialArrivalTrackingDTO) {
        // 0.数据转换
        MaterialArrivalTrackingDO materialArrivalTrackingDO = MaterialArrivalTrackingConvertor.INSTANCE.dto2Do(materialArrivalTrackingDTO);
        MaterialArrivalTrackingPO materialArrivalTrackingPO = MaterialArrivalTrackingConvertor.INSTANCE.dto2Po(materialArrivalTrackingDTO);
        // 1.数据校验
        materialArrivalTrackingDomainService.validation(materialArrivalTrackingDO);
        // 2.数据持久化
        NewProductStockPointVO productInfo = newMdsFeign.selectProductStockPointByIds(
        		SystemHolder.getScenario(),
        		Arrays.asList(materialArrivalTrackingDTO.getProductId())).get(0);

        materialArrivalTrackingPO.setStockPointCode(productInfo.getStockPointCode());
        materialArrivalTrackingPO.setMaterialCode(productInfo.getProductCode());
        materialArrivalTrackingPO.setMaterialName(productInfo.getProductName());
        materialArrivalTrackingPO.setRequireDate(materialArrivalTrackingDTO.getPredictArrivalDate());
        BigDecimal requireQuantity = materialArrivalTrackingDTO.getPredictArrivalQuantity();
        if (null != materialArrivalTrackingDTO.getWaitDeliveryQuantity()){
            requireQuantity = requireQuantity.add(materialArrivalTrackingDTO.getWaitDeliveryQuantity());
        }

        materialArrivalTrackingPO.setRequireQuantity(requireQuantity);
//        materialArrivalTrackingPO.setWaitDeliveryQuantity(materialArrivalTrackingDTO.getPredictArrivalQuantity());
        if(StringUtils.isEmpty(materialArrivalTrackingPO.getDataSource())) {
        	materialArrivalTrackingPO.setDataSource(ArrivalTrackingDataSourceEnum.MANUAL_ADDITION.getCode());
        }

        BasePOUtils.insertFiller(materialArrivalTrackingPO);
        materialArrivalTrackingDao.insertWithPrimaryKey(materialArrivalTrackingPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

	private String getScenario(String stockPointCode) {
        List<ScenarioBusinessRangeVO> scenarioBusinessRangeVOList = ipsNewFeign.selectScenarioBusinessByParams(ImmutableMap.of("rangeData", stockPointCode));
//        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MDS.getCode(),
//                TenantCodeEnum.FYQB.getCode());
		return scenarioBusinessRangeVOList.get(0).getScenario();
	}

    @Override
    public BaseResponse<Void> doUpdate(MaterialArrivalTrackingDTO materialArrivalTrackingDTO) {
        // 0.数据转换
        MaterialArrivalTrackingDO materialArrivalTrackingDO = MaterialArrivalTrackingConvertor.INSTANCE.dto2Do(materialArrivalTrackingDTO);
        MaterialArrivalTrackingPO materialArrivalTrackingPO = MaterialArrivalTrackingConvertor.INSTANCE.dto2Po(materialArrivalTrackingDTO);
        // 1.数据校验
        materialArrivalTrackingDomainService.validation(materialArrivalTrackingDO);

        // 特殊处理已入库状态（原先是已送货状态，更新成已入库状态时，需要将在途数量更新到入库数量，并且在途数量更新为0 ）
//        if (materialArrivalTrackingPO.getArrivalStatus().equals(ArrivalStatusEnum.ALREADY_IN_STOCK.getCode())){
//            // 获取未修改的到货状态
//            MaterialArrivalTrackingVO materialArrivalTrackingVO = this.selectByPrimaryKey(materialArrivalTrackingDTO.getId());
//            if (materialArrivalTrackingVO.getArrivalStatus().equals(ArrivalStatusEnum.DELIVERED.getCode())){
//                materialArrivalTrackingPO.setInventoryQuantity(materialArrivalTrackingPO.getPredictArrivalQuantity());
//                materialArrivalTrackingPO.setPredictArrivalQuantity(BigDecimal.ZERO);
//            }
//        }

        // 获取修改详情（VO）
        MaterialArrivalTrackingVO materialArrivalTrackingVO =
                this.selectVOByParams(ImmutableMap.of("id", materialArrivalTrackingPO.getId())).get(0);

        // PO关闭（下发给ERP）
        if (materialArrivalTrackingPO.getArrivalStatus().equals(ArrivalStatusEnum.CLOSE.getCode())) {
            if (materialArrivalTrackingVO.getArrivalStatus().equals(ArrivalStatusEnum.CLOSE.getCode())){
                throw new BusinessException("已经关闭的不能再次关闭");
            }
            if (StringUtils.isEmpty(materialArrivalTrackingVO.getPurchaseOrderCode()) ||
                    StringUtils.isEmpty(materialArrivalTrackingVO.getPurchaseOrderLineCode())) {
                throw new BusinessException("PO和PO行不能为空");
            }

            // 调用SRM关单（PO）接口
            closeOrder(Collections.singletonList(materialArrivalTrackingPO.getId()));

//            Map<String, Object> params = new HashMap<>();
//            // PO号
//            params.put("poNum", materialArrivalTrackingVO.getPurchaseOrderCode());
//            // 行号
//            params.put("lineNum", materialArrivalTrackingVO.getPurchaseOrderLineCode());
//            // 组织ID
//            params.put("orgId", materialArrivalTrackingVO.getOrganizationId());
//            // 关闭原因
//            params.put("closeReason",  UpdateReasonEnum.valueOf(materialArrivalTrackingPO.getUpdateReason()).getDesc());
//            // 用户名 根据当前用户取用户表中的erp_user字段
//            User user = ipsNewFeign.userNameById(SystemHolder.getUserId());
//            log.info("当前用户信息为：{}", user);
//            params.put("erpUser", user.getErpUser());
//            log.info("关闭ERP参数为：{}", params);
//            try {
//                poClosed(SystemHolder.getTenantId(), params);
//            } catch (Exception e) {
//                log.error("同步失败", e);
//                throw new BusinessException("同步失败,{0}", e.getLocalizedMessage());
//            }
        }

        // PR取消（下发给ERP）
        if (materialArrivalTrackingPO.getArrivalStatus().equals(ArrivalStatusEnum.CANCEL.getCode())) {
            if (materialArrivalTrackingVO.getArrivalStatus().equals(ArrivalStatusEnum.CLOSE.getCode()) ||
                    materialArrivalTrackingVO.getArrivalStatus().equals(ArrivalStatusEnum.CANCEL.getCode())){
                throw new BusinessException("已经取消、关闭的不能再次取消");
            }
            if (materialArrivalTrackingPO.getDataSource().equals(ArrivalTrackingDataSourceEnum.PURCHASING_PLAN.getCode()) ||
                    materialArrivalTrackingPO.getDataSource().equals(ArrivalTrackingDataSourceEnum.PURCHASING_FORECAST.getCode()) )
            {
                materialPlanNeedService.cancelIssue02(Collections.singletonList(materialArrivalTrackingPO.getSourceId()));
            }else  {
                if (org.apache.commons.lang3.StringUtils.isBlank(materialArrivalTrackingVO.getPurchaseRequestCode()) ||
                        org.apache.commons.lang3.StringUtils.isBlank(materialArrivalTrackingVO.getPurchaseRequestLineCode())){
                    throw new BusinessException("PR和PR行不能为空");
                }

                Map<String, Object> params = new HashMap<>();
                // PR号
                params.put("reqNum", materialArrivalTrackingVO.getPurchaseRequestCode());
                // PR行号
                params.put("lineNum", materialArrivalTrackingVO.getPurchaseRequestLineCode());
                // 组织ID
                params.put("orgId", materialArrivalTrackingVO.getOrganizationId());
                // 取消原因
                params.put("cancelReason", UpdateReasonEnum.valueOf(materialArrivalTrackingPO.getUpdateReason()).getDesc());
                // 用户名 根据当前用户取用户表中的erp_user字段
                User user = ipsNewFeign.userNameById(SystemHolder.getUserId());
                log.info("当前用户信息为：{}", user);
                params.put("erpUser", user.getErpUser());
                log.info("取消ERP参数为：{}", params);
                try {
                    prCancel(SystemHolder.getTenantId(), params);
                } catch (Exception e) {
                    log.error("同步失败", e);
                    throw new BusinessException("同步失败,{0}", e.getLocalizedMessage());
                }
            }
        }
        // 2.数据持久化
        BasePOUtils.updateFiller(materialArrivalTrackingPO);
        materialArrivalTrackingDao.updateSelective(materialArrivalTrackingPO);

        // 到货跟踪的”预计到货日期”修改时，要货计划的”承诺到货日期”同步修改
        if (StringUtils.isNotEmpty(materialArrivalTrackingDTO.getDataSource()) &&
                (materialArrivalTrackingDTO.getDataSource().equals(ArrivalTrackingDataSourceEnum.PURCHASING_PLAN.getCode()) ||
                        materialArrivalTrackingDTO.getDataSource().equals(ArrivalTrackingDataSourceEnum.PURCHASING_FORECAST.getCode())) &&
                null != materialArrivalTrackingDTO.getPredictArrivalDate()) {
            MaterialPlanNeedVO materialPlanNeedVO =
                    materialPlanNeedService.selectByPrimaryKey(materialArrivalTrackingDTO.getSourceId());
            if (Objects.nonNull(materialPlanNeedVO)) {
                MaterialPlanNeedDTO dto = MaterialPlanNeedDTO
                        .builder()
                        .id(materialArrivalTrackingDTO.getSourceId())
                        .expectedArrivalTime(materialArrivalTrackingDTO.getPredictArrivalDate())
                        .build();
                materialPlanNeedService.updateBatchSelective(Collections.singletonList(dto));
            }
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialArrivalTrackingDTO> list) {
        List<MaterialArrivalTrackingPO> newList = MaterialArrivalTrackingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialArrivalTrackingDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialArrivalTrackingDTO> list) {
        List<MaterialArrivalTrackingPO> newList = MaterialArrivalTrackingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialArrivalTrackingDao.updateBatchSelective(newList);

        List<String> sourceIds = newList.stream()
                .filter(data -> (data.getDataSource().equals(ArrivalTrackingDataSourceEnum.PURCHASING_PLAN.getCode()) ||
                        data.getDataSource().equals(ArrivalTrackingDataSourceEnum.PURCHASING_FORECAST.getCode())) &&
                        null != data.getPredictArrivalDate())
                .map(MaterialArrivalTrackingBasicPO::getSourceId)
                .collect(Collectors.toList());

        List<MaterialPlanNeedVO> materialPlanNeedVOList = materialPlanNeedService.selectByParams(ImmutableMap.of("ids", sourceIds));
        if (CollectionUtils.isEmpty(materialPlanNeedVOList)) return;
        Map<String, MaterialPlanNeedVO> needVOMap = materialPlanNeedVOList.stream().collect(Collectors.toMap(BaseVO::getId, Function.identity()));

        for (MaterialArrivalTrackingDTO materialArrivalTracking : list) {
            // 到货跟踪的”预计到货日期”修改时，要货计划的”承诺到货日期”同步修改
            if (needVOMap.containsKey(materialArrivalTracking.getSourceId())) {

                MaterialPlanNeedVO materialPlanNeedVO = needVOMap.get(materialArrivalTracking.getSourceId());
                if (Objects.nonNull(materialPlanNeedVO)) {
                    MaterialPlanNeedDTO dto = MaterialPlanNeedDTO
                            .builder()
                            .id(materialArrivalTracking.getSourceId())
                            .expectedArrivalTime(materialArrivalTracking.getPredictArrivalDate())
                            .build();
                    materialPlanNeedService.updateBatchSelective(Collections.singletonList(dto));
                }
            }
        }
    }

    @Override
    public void doUpdateBatchNew(List<MaterialArrivalTrackingDTO> list) {
        List<MaterialArrivalTrackingPO> newList = MaterialArrivalTrackingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialArrivalTrackingDao.updateBatch(newList);
    }

    @Override
    public void doUpdateBatchSelective(List<MaterialArrivalTrackingDTO> list) {
        List<MaterialArrivalTrackingPO> newList = MaterialArrivalTrackingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialArrivalTrackingDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialArrivalTrackingDao.deleteBatch(idList);
        }
        return materialArrivalTrackingDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialArrivalTrackingVO selectByPrimaryKey(String id) {
        MaterialArrivalTrackingPO po = materialArrivalTrackingDao.selectByPrimaryKey(id);
        return MaterialArrivalTrackingConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_material_arrival_tracking")
    public List<MaterialArrivalTrackingVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        // 过滤材料计划员权限
        String queryCondition = String.format(" find_in_set('%s', material_planner) > 0", SystemHolder.getUserId());
        if (StringUtils.isEmpty(queryCriteriaParam)) {
            queryCriteriaParam = queryCondition;
        } else {
            queryCriteriaParam = queryCriteriaParam + " and" + queryCondition;
        }
        log.info("queryCriteriaParam = {}", queryCriteriaParam);
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mrp_material_arrival_tracking")
    public List<MaterialArrivalTrackingVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialArrivalTrackingVO> dataList = materialArrivalTrackingDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialArrivalTrackingServiceImpl target = springBeanUtils.getBean(MaterialArrivalTrackingServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialArrivalTrackingVO> selectByParams(Map<String, Object> params) {
        List<MaterialArrivalTrackingPO> list = materialArrivalTrackingDao.selectByParams(params);
        return MaterialArrivalTrackingConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialArrivalTrackingVO> selectVOByParams(Map<String, Object> params) {
        return materialArrivalTrackingDao.selectVOByParams(params);
    }


    @Override
    public List<MaterialArrivalTrackingVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MATERIAL_ARRIVAL_TRACKING.getCode();
    }

    @Override
    public List<MaterialArrivalTrackingVO> invocation(List<MaterialArrivalTrackingVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

//	@Override
//	public List<LabelValue<String>> queryStockPointDown() {
//		List<NewStockPointVO> allStockPoint = newMdsFeign.selectAllStockPoint(getScenario());
//		return allStockPoint.stream()
//                .map(x -> new LabelValue<>(x.getStockPointName(), x.getStockPointCode()))
//                .collect(Collectors.toList());
//	}

	@Override
	public void doSyncMaterialArrivalTracking(List<SyncMaterialArrivalTrackingDTO> syncList) {
		if(CollectionUtils.isEmpty(syncList)) {
			return;
		}
		//获取物料数据信息
		List<String> materialCodeList = syncList.stream().map(SyncMaterialArrivalTrackingDTO::getMaterialCode)
				.distinct().collect(Collectors.toList());
		List<NewProductStockPointVO> productInfoList = newMdsFeign
				.selectByProductCode(getScenario(syncList.get(0).getStockPointCode()), materialCodeList);
		Map<String, NewProductStockPointVO> productMap = productInfoList.stream()
				.collect(Collectors.toMap(e -> e.getStockPointCode() + "_" + e.getProductCode()
				,item -> item, (v1, v2) -> v1));
		List<MaterialArrivalTrackingDTO> bathcAddList = new ArrayList<>();
		for (SyncMaterialArrivalTrackingDTO syncTracking : syncList) {
			MaterialArrivalTrackingDTO add = new MaterialArrivalTrackingDTO();
			NewProductStockPointVO productInfo = productMap.get(syncTracking.getStockPointCode() + "_" + syncTracking.getMaterialCode());
			if(Objects.isNull(productInfo)) {
				String errorMsg = String.format("库存点【%s】物料编码【%s】未找到对应的物料数据信息",
						syncTracking.getStockPointCode(),
						syncTracking.getMaterialCode());
				throw new BusinessException(errorMsg);
			}
            // 预计到货日期为空，默认要货日期
            if (null == syncTracking.getPredictArrivalDate()){
                syncTracking.setPredictArrivalDate(syncTracking.getRequireDate());
            }
			add.setProductId(productInfo.getId());
			BeanUtils.copyProperties(syncTracking, add);
			add.setWaitDeliveryQuantity(syncTracking.getRequireQuantity());
			add.setArrivalStatus(ArrivalStatusEnum.PLAN_PRUCHASE.getCode());
            // 重新赋值创建人和修改人，不使用默认
            if (StringUtils.isEmpty(add.getCreator()) || StringUtils.isEmpty(add.getModifier())){
                // 获取 Redis 中的值并判空
                Object userFromRedis = redisUtil.get(ISSUE_REDIS_USER);
                if (Objects.nonNull(userFromRedis)) {
                    add.setCreator(userFromRedis.toString());
                    add.setModifier(userFromRedis.toString());
                } else {
                    // 可以根据实际情况进行处理，例如设置默认值
                    add.setCreator(SystemHolder.getUserId());
                    add.setModifier(SystemHolder.getUserId());
                }
            }
            add.setCreateTime(new Date());
            add.setModifyTime(new Date());
            add.setEnabled(YesOrNoEnum.YES.getCode());
			bathcAddList.add(add);
		}
		materialArrivalTrackingDao.insertBatch(MaterialArrivalTrackingConvertor.INSTANCE.dto2Pos(bathcAddList));
	}

    @Override
    public void doUpdateSyncMaterialArrivalTracking(List<SyncMaterialArrivalTrackingDTO> syncList) {
        if (CollectionUtils.isEmpty(syncList)) {
            return;
        }
        //获取物料数据信息
        List<String> materialCodeList = syncList.stream().map(SyncMaterialArrivalTrackingDTO::getMaterialCode)
                .distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> productInfoList = newMdsFeign
                .selectByProductCode(getScenario(syncList.get(0).getStockPointCode()), materialCodeList);
        Map<String, NewProductStockPointVO> productMap = productInfoList.stream()
                .collect(Collectors.toMap(e -> e.getStockPointCode() + "_" + e.getProductCode()
                        , item -> item, (v1, v2) -> v1));
        List<MaterialArrivalTrackingDTO> bathcUpdateList = new ArrayList<>();
        for (SyncMaterialArrivalTrackingDTO syncTracking : syncList) {
            MaterialArrivalTrackingDTO add = new MaterialArrivalTrackingDTO();
            NewProductStockPointVO productInfo = productMap.get(syncTracking.getStockPointCode() + "_" + syncTracking.getMaterialCode());
            if (Objects.isNull(productInfo)) {
                String errorMsg = String.format("库存点【%s】物料编码【%s】未找到对应的物料数据信息",
                        syncTracking.getStockPointCode(),
                        syncTracking.getMaterialCode());
                throw new BusinessException(errorMsg);
            }
            add.setProductId(productInfo.getId());
            BeanUtils.copyProperties(syncTracking, add);
            // 预计到货日期为空，默认要货日期
            if (null == syncTracking.getPredictArrivalDate()){
                syncTracking.setPredictArrivalDate(syncTracking.getRequireDate());
            }
            add.setWaitDeliveryQuantity(syncTracking.getRequireQuantity());
            add.setArrivalStatus(ArrivalStatusEnum.PLAN_PRUCHASE.getCode());
            add.setCreateTime(new Date());
            add.setModifyTime(new Date());
            add.setEnabled(YesOrNoEnum.YES.getCode());
            bathcUpdateList.add(add);
        }
        materialArrivalTrackingDao.updateBatchSelective(MaterialArrivalTrackingConvertor.INSTANCE.dto2Pos(bathcUpdateList));
    }


	@Override
	public void doUpdateBatchBySourceId(List<MaterialArrivalTrackingDTO> list) {
		List<MaterialArrivalTrackingPO> newList = MaterialArrivalTrackingConvertor.INSTANCE.dto2Pos(list);
		BasePOUtils.updateBatchFiller(newList);
		materialArrivalTrackingDao.doUpdateBatchBySourceId(newList);
	}

    @Override
    public BaseResponse<Void> splitTheOrder(List<MaterialArrivalTrackingDTO> materialArrivalTrackingDTOList) {
        // 校验到货状态
        for (MaterialArrivalTrackingDTO trackingDTO : materialArrivalTrackingDTOList) {
            if (!trackingDTO.getArrivalStatus().equals(ArrivalStatusEnum.PLAN_PRUCHASE.getCode())) {
                throw new BusinessException("到货状态为计划采购才允许拆单");
            }
        }

        // 校验要货数量和待发货数量
        String id = materialArrivalTrackingDTOList.get(0).getId();
        MaterialArrivalTrackingVO materialArrivalTrackingVO = this.selectByPrimaryKey(id);
        if (materialArrivalTrackingVO.getRequireQuantity().compareTo(materialArrivalTrackingVO.getWaitDeliveryQuantity()) != 0) {
            throw new BusinessException("要货数量不等于待发货数量，不允许拆单");
        }

        // 校验拆单数量是否正确
        BigDecimal waitDeliveryQuantitySum = materialArrivalTrackingDTOList.stream()
                .map(MaterialArrivalTrackingBasicDTO::getWaitDeliveryQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (waitDeliveryQuantitySum.compareTo(materialArrivalTrackingVO.getWaitDeliveryQuantity()) != 0){
            throw new BusinessException("拆单数量之和必须等于原待发货数量");
        }

        // 区分要货计划拆单 和 采购需求拆单
        List<String> sourceIdList = new ArrayList<>();
        if (materialArrivalTrackingVO.getDataSource().equals(ArrivalTrackingDataSourceEnum.PURCHASING_PLAN.getCode())) {
            MaterialPlanNeedVO materialPlanNeedVO =
                    materialPlanNeedService.selectByPrimaryKey(materialArrivalTrackingVO.getSourceId());
            if (Objects.nonNull(materialPlanNeedVO)) {

                List<MaterialPlanNeedDTO> splitTheOrderNeedList = createSplitTheOrderNeedList(materialArrivalTrackingDTOList, materialPlanNeedVO);
                // 要货计划拆单
                materialPlanNeedService.splitTheOrder(splitTheOrderNeedList);

                // 获取拆单的子集要货计划id 并根据要货数量排序（确保正确映射到货跟踪的待发货数量）
                sourceIdList = materialPlanNeedService.selectByParams(ImmutableMap.of("parentId", materialPlanNeedVO.getId()))
                        .stream()
                        .sorted(Comparator.comparing(MaterialPlanNeedVO::getNeedQuantity))
                        .map(BaseVO::getId)
                        .collect(Collectors.toList());
            }
        } else if (materialArrivalTrackingVO.getDataSource().equals(ArrivalTrackingDataSourceEnum.MATERIAL_PURCHASE.getCode())) {
            MaterialPurchaseRequirementDetailVO materialPurchaseRequirementDetailVO =
                    materialPurchaseRequirementDetailService.selectByPrimaryKey(materialArrivalTrackingVO.getSourceId());
            if (Objects.nonNull(materialPurchaseRequirementDetailVO)) {

                List<MaterialPurchaseRequirementDetailDTO> splitTheOrderRequirementList =
                        createSplitTheOrderRequirementList(materialArrivalTrackingDTOList, materialPurchaseRequirementDetailVO);
                // 采购需求拆单
                materialPurchaseRequirementDetailService.splitTheOrder(splitTheOrderRequirementList);

                // 获取拆单的子集采购需求id 并根据要货数量排序（确保正确映射到货跟踪的待发货数量）
                sourceIdList = materialPurchaseRequirementDetailService.selectByParams(ImmutableMap.of("parentId", materialPurchaseRequirementDetailVO.getId()))
                        .stream()
                        .sorted(Comparator.comparing(MaterialPurchaseRequirementDetailVO::getRequireQuantity))
                        .map(BaseVO::getId)
                        .collect(Collectors.toList());
            }
        }

        List<MaterialArrivalTrackingDTO> addList = new ArrayList<>();

        // 处理拆批数据
        if (CollectionUtils.isNotEmpty(materialArrivalTrackingDTOList) && CollectionUtils.isNotEmpty(sourceIdList) &&
                sourceIdList.size() == materialArrivalTrackingDTOList.size()) {

            processRemainingData(materialArrivalTrackingDTOList, addList, id, sourceIdList);
        }

        // 删除原本到货跟踪
        this.doDelete(Collections.singletonList(id));

        // 批量新增
        if (CollectionUtils.isNotEmpty(addList)) {
            Lists.partition(addList, 500).forEach(this::doCreateBatch);
        }

        // 获取拆批数据
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList = this.selectByParams(ImmutableMap.of("parentId", id));
        List<String> releaseOrderIdList = materialArrivalTrackingVOList.stream().map(BaseVO::getId).collect(Collectors.toList());

        // 发布
        this.releaseOrder(releaseOrderIdList);

        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
    /**
     * 创建要货计划拆单
     *
     * @param materialArrivalTrackingDTOList 到货跟踪
     * @param materialPlanNeedVO             要货计划
     * @return 拆单后的要货计划
     */
    private List<MaterialPlanNeedDTO> createSplitTheOrderNeedList(List<MaterialArrivalTrackingDTO> materialArrivalTrackingDTOList,
                                                                  MaterialPlanNeedVO materialPlanNeedVO) {
        return materialArrivalTrackingDTOList.stream()
                .map(trackingDTO -> {
                    MaterialPlanNeedDTO materialPlanNeedDTO = new MaterialPlanNeedDTO();
                    BeanUtils.copyProperties(materialPlanNeedVO, materialPlanNeedDTO);
                    materialPlanNeedDTO.setNeedQuantity(trackingDTO.getWaitDeliveryQuantity());
                    materialPlanNeedDTO.setNeedDate(trackingDTO.getRequireDate());
                    return materialPlanNeedDTO;
                })
                .collect(Collectors.toList());
    }

    private List<MaterialPurchaseRequirementDetailDTO> createSplitTheOrderRequirementList(List<MaterialArrivalTrackingDTO> materialArrivalTrackingDTOList,
                                                                         MaterialPurchaseRequirementDetailVO materialPurchaseRequirementDetailVO) {
        return materialArrivalTrackingDTOList.stream()
                .map(trackingDTO -> {
                    MaterialPurchaseRequirementDetailDTO materialPurchaseRequirementDetailDTO = new MaterialPurchaseRequirementDetailDTO();
                    BeanUtils.copyProperties(materialPurchaseRequirementDetailVO, materialPurchaseRequirementDetailDTO);
                    materialPurchaseRequirementDetailDTO.setRequireQuantity(trackingDTO.getWaitDeliveryQuantity());
                    materialPurchaseRequirementDetailDTO.setRequireDate(trackingDTO.getRequireDate());
                    return materialPurchaseRequirementDetailDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 处理拆批到货跟踪的子级
     *
     * @param materialArrivalTrackingDTOList 到货跟踪
     * @param addList                        收集添加
     * @param parentId                       父级ID
     * @param sourceIdList                   数据来源ID
     */
    private void processRemainingData(List<MaterialArrivalTrackingDTO> materialArrivalTrackingDTOList,
                                      List<MaterialArrivalTrackingDTO> addList,
                                      String parentId,
                                      List<String> sourceIdList) {
        for (int i = 0; i < materialArrivalTrackingDTOList.size(); i++) {
            MaterialArrivalTrackingDTO trackingDTO = materialArrivalTrackingDTOList.get(i);
            trackingDTO.setId(UUID.randomUUID().toString());
            trackingDTO.setRemark("拆单数据");
            trackingDTO.setParentId(parentId);
            trackingDTO.setPublishStatus(ArrivalTrackingPublishStatusEnum.CHANGED_IN.getCode());
            trackingDTO.setSourceId(sourceIdList.get(i));
            trackingDTO.setRequireQuantity(trackingDTO.getWaitDeliveryQuantity());
            addList.add(trackingDTO);
        }
    }

    @Override
    public BaseResponse<Void> closeOrder(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)){
            throw new BusinessException("id不能为空");
        }

        // 获取对应到货跟踪
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList = this.selectByParams(ImmutableMap.of("ids", idList));

        // 校验PO号和PO行号
        List<MaterialArrivalTrackingVO> exceptionList = materialArrivalTrackingVOList.stream()
                .filter(data -> StringUtils.isEmpty(data.getPurchaseOrderCode()) || StringUtils.isEmpty(data.getPurchaseOrderLineCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(exceptionList)) throw new BusinessException("PO号和PO行号不能为空");

        // 根据PO行号和PO号分组
        List<String> poMap = materialArrivalTrackingVOList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getPurchaseOrderLineCode()) && StringUtils.isNotEmpty(data.getPurchaseOrderCode()))
                .map(data -> data.getPurchaseOrderLineCode() + "&" + data.getPurchaseOrderCode())
                .distinct()
                .collect(Collectors.toList());

        // 收集MaterialArrivalTrackingCloseDTO.POLine  List
        List<MaterialArrivalTrackingCloseDTO.POLine> poLineList = new ArrayList<>();
        for (String po : poMap) {
            String[] split = po.split("&");
            if (split.length == 2) {
                MaterialArrivalTrackingCloseDTO.POLine poLine = new MaterialArrivalTrackingCloseDTO.POLine();
                poLine.setPoNo(split[1]);
                poLine.setLineNo(split[0]);
                poLineList.add(poLine);
            }
        }

        // 构建 MaterialArrivalTrackingCloseDTO
        MaterialArrivalTrackingCloseDTO materialArrivalTrackingCloseDTO = new MaterialArrivalTrackingCloseDTO();
        MaterialArrivalTrackingCloseDTO.Service service = new MaterialArrivalTrackingCloseDTO.Service();
        MaterialArrivalTrackingCloseDTO.Route route = new MaterialArrivalTrackingCloseDTO.Route();
        route.setSerialNo("2024121203018000001");
        route.setServiceId("02003000000013");
        route.setSourceSysId("03018");
        route.setServiceTime("20241212152805");
        route.setSoapenv("http://schemas.xmlsoap.org/soap/envelope/");
        MaterialArrivalTrackingCloseDTO.ServiceResponse serviceResponse = new MaterialArrivalTrackingCloseDTO.ServiceResponse();
        serviceResponse.setStatus("COMPLETE");
        route.setServiceResponse(serviceResponse);
        service.setRoute(route);

        MaterialArrivalTrackingCloseDTO.Data data = new MaterialArrivalTrackingCloseDTO.Data();
        MaterialArrivalTrackingCloseDTO.Control control = new MaterialArrivalTrackingCloseDTO.Control();
        control.setPathVariable(null);
        data.setControl(control);

        MaterialArrivalTrackingCloseDTO.Request request = new MaterialArrivalTrackingCloseDTO.Request();
        MaterialArrivalTrackingCloseDTO.ListInfo listInfo = new MaterialArrivalTrackingCloseDTO.ListInfo();
        listInfo.setPoLine(poLineList);
        request.setList(listInfo);
        data.setRequest(request);

        MaterialArrivalTrackingCloseDTO.Response response = new MaterialArrivalTrackingCloseDTO.Response();
        response.setClosePOResult(null);
        data.setResponse(response);
        service.setData(data);
        materialArrivalTrackingCloseDTO.setService(service);

        try {

            // 填充参数调用dcp服务下发到货跟踪-关单
            HashMap<String, Object> params = new HashMap<>();
            params.put("Service", materialArrivalTrackingCloseDTO.getService());
            BaseResponse<String> baseResponse = newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.SRM.getCode(), ApiCategoryEnum.MATERIAL_ARRIVAL_TRACKING_CLOSED.getCode(), params);
            log.info("下发到货跟踪-关单的数据为{}", materialArrivalTrackingCloseDTO);
            if (baseResponse.getSuccess().equals(false)){
                throw new BusinessException("到货跟踪-关单服务接口调用失败");
            }
        } catch (Exception e) {
            log.error("下发到货跟踪-关单失败", e);
            throw new BusinessException("下发到货跟踪-关单失败,{0}",e.getLocalizedMessage());
        }

        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> releaseOrder(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            throw new BusinessException("id不能为空");
        }

        // 获取对应到货跟踪
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList = this.selectVOByParams(ImmutableMap.of("ids", idList));
        Map<String, List<MaterialArrivalTrackingVO>> materialArrivalTrackingMap =
                materialArrivalTrackingVOList.stream().collect(Collectors.groupingBy(MaterialArrivalTrackingVO::getArrivalStatus));
        if (materialArrivalTrackingMap.containsKey(ArrivalStatusEnum.DELIVERED.getCode()))
            throw new BusinessException("已发货状态数据不允许发布");

        // 区分模式，分别是要货计划模式和PR模式（分别走不同的接口）
        List<MaterialArrivalTrackingVO> materialArrivalTrackingNeedList = materialArrivalTrackingVOList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getMaterialPlanNeedNo())).collect(Collectors.toList());

        List<MaterialArrivalTrackingVO> materialArrivalTrackingPrList = materialArrivalTrackingVOList.stream()
                .filter(data -> StringUtils.isEmpty(data.getMaterialPlanNeedNo())).collect(Collectors.toList());

        // 要货模式的数据要调用SRM两个接口（对方就是这个结构，没办法），先调用下发30天要货发布，再调用要货计划修改接口
        if (CollectionUtils.isNotEmpty(materialArrivalTrackingNeedList)) {

            // 收集数据源id
            List<String> sourceIdList = materialArrivalTrackingVOList.stream()
                    .filter(data -> StringUtils.isNotEmpty(data.getMaterialPlanNeedNo()))
                    .map(MaterialArrivalTrackingVO::getSourceId).distinct().collect(Collectors.toList());
            // 获取当前要货计划
            List<MaterialPlanNeedVO> materialPlanNeedVOList = materialPlanNeedService.selectByVOParams(ImmutableMap.of("ids", sourceIdList));
            // 根据id分组
            Map<String, MaterialPlanNeedVO> materialPlanNeedVOMap = materialPlanNeedVOList.stream()
                    .collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v1));
            // 根据要货计划号分组
            Map<String, List<MaterialPlanNeedVO>> materialPlanNeedVOMaterialPlanNeedNoMap = materialPlanNeedVOList.stream()
                    .collect(Collectors.groupingBy(MaterialPlanNeedVO::getMaterialPlanNeedNo));

            // 获取当前要货计划下发明细
            List<MaterialPlanNeedIssueDetailsVO> materialPlanNeedIssueDetailsVOList =
                    materialPlanNeedIssueDetailsService.selectByParams(ImmutableMap.of("materialPlanNeedNos", materialPlanNeedVOMaterialPlanNeedNoMap.keySet()));
            // 根据要货计划id分组并过滤出天的
            Map<String, List<MaterialPlanNeedIssueDetailsVO>> materialPlanNeedIssueDetailsMap = materialPlanNeedIssueDetailsVOList.stream()
                    .filter(data -> data.getDimension().equals(NeedDimensionEnum.DAY.getCode()))
                    .collect(Collectors.groupingBy(MaterialPlanNeedIssueDetailsVO::getMaterialPlanNeedNo));

            // 构建MaterialPlanNeedReleaseDayDTO
            MaterialPlanNeedReleaseDayDTO materialPlanNeedReleaseDayDTO =
                    buildReleaseDayMaterialArrivalTrackingDTO(materialArrivalTrackingNeedList, materialPlanNeedVOMap, materialPlanNeedIssueDetailsMap);

            try {
                // 获取当前的要货计划下发明细
                // 填充参数调用dcp服务下发30天要货计划
                HashMap<String, Object> params = new HashMap<>();
                params.put("Service", materialPlanNeedReleaseDayDTO.getService());
                BaseResponse<String> response = newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.SRM.getCode(), ApiCategoryEnum.MATERIAL_PRODUCT_NEED_DAY.getCode(), params);
                log.info("30天要货计划下发的数据为{}", materialPlanNeedReleaseDayDTO);
                if (response.getSuccess().equals(false)) {
                    throw new BusinessException("服务接口调用失败");
                }
                // 解析嵌套的 JSON 数组
                List<InsertRollPredictionDayResult> insertRollPredictionResultDayList = JSON.parseArray(response.getData(), InsertRollPredictionDayResult.class);

                // 根据状态分组看有无异常状态
                Map<Integer, List<InsertRollPredictionDayResult>> statusDatMap =
                        insertRollPredictionResultDayList.stream().collect(Collectors.groupingBy(InsertRollPredictionDayResult::getStatus));

                // 记录下发失败的30天要货数据日志
                processMaterialPlanDay(statusDatMap.getOrDefault(0, new ArrayList<>()), materialPlanNeedVOMaterialPlanNeedNoMap, Boolean.FALSE.toString(), NeedDimensionEnum.DAY.getCode());
                // 记录下发成功的30天要货数据日志
                processMaterialPlanDay(statusDatMap.getOrDefault(1, new ArrayList<>()), materialPlanNeedVOMaterialPlanNeedNoMap, Boolean.TRUE.toString(), NeedDimensionEnum.DAY.getCode());
            } catch (Exception e) {
                log.error("到货跟踪（下发30天要货计划）失败", e);
                throw new BusinessException("到货跟踪（下发30天要货计划）失败,{0}", e.getLocalizedMessage());
            }

            // 构建MaterialArrivalTrackingUpdateDTO
            MaterialArrivalTrackingUpdateDTO materialArrivalTrackingUpdateDTO = buildNeedMaterialArrivalTrackingUpdateDTO(materialArrivalTrackingNeedList);
            try {

                // 填充参数调用dcp服务下发到货跟踪-更新单（要货模式）
                HashMap<String, Object> params = new HashMap<>();
                params.put("Service", materialArrivalTrackingUpdateDTO.getService());

                BaseResponse<String> baseResponse = newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(),
                        ApiSourceEnum.SRM.getCode(), ApiCategoryEnum.MATERIAL_ARRIVAL_TRACKING_UPDATE_NEED.getCode(), params);
                log.info("下发到货跟踪-更新单（要货模式）的数据为{}", materialArrivalTrackingUpdateDTO);
                if (baseResponse.getSuccess().equals(false)) {
                    throw new BusinessException("下发到货跟踪-更新单（要货模式）接口调用失败");
                }
            } catch (Exception e) {
                log.error("下发到货跟踪-更新单（要货模式）失败", e);
                throw new BusinessException("下发到货跟踪-更新单（要货模式）,{0}", e.getLocalizedMessage());
            }
        }
        if (CollectionUtils.isNotEmpty(materialArrivalTrackingPrList)) {
            MaterialArrivalTrackingUpdateDTO materialArrivalTrackingUpdateDTO = buildPRMaterialArrivalTrackingUpdateDTO(materialArrivalTrackingPrList);

            try {

                // 填充参数调用dcp服务下发货跟踪-更新单（PR模式）
                HashMap<String, Object> params = new HashMap<>();
                params.put("Service", materialArrivalTrackingUpdateDTO.getService());

                BaseResponse<String> baseResponse = newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(),
                        ApiSourceEnum.SRM.getCode(), ApiCategoryEnum.MATERIAL_ARRIVAL_TRACKING_UPDATE_PR.getCode(), params);
                log.info("下发货跟踪-更新单（PR模式）的数据为{}", materialArrivalTrackingUpdateDTO);
                if (baseResponse.getSuccess().equals(false)) {
                    throw new BusinessException("下发货跟踪-更新单（PR模式）服务接口调用失败");
                }
            } catch (Exception e) {
                log.error("下发货跟踪-更新单（PR模式失败", e);
                throw new BusinessException("下发货跟踪-更新单（PR模式）失败,{0}", e.getLocalizedMessage());
            }
        }

        // 修改到货跟踪发布状态为已发布
        List<MaterialArrivalTrackingDTO> updateMaterialArrivalTrackingDTOList = idList.stream().map(data -> {
            MaterialArrivalTrackingDTO trackingDTO = new MaterialArrivalTrackingDTO();
            trackingDTO.setId(data);
            trackingDTO.setPublishStatus(ArrivalTrackingPublishStatusEnum.CHANGED_IN.getCode());
            return trackingDTO;
        }).collect(Collectors.toList());
        this.doUpdateBatchSelective(updateMaterialArrivalTrackingDTOList);

        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    /**
     * 构建到货跟踪更新MaterialArrivalTrackingUpdateDTO（要货模式）
     *
     * @param materialArrivalTrackingNeedList 到货跟踪（要货模式）
     * @return MaterialArrivalTrackingUpdateDTO
     */
    private MaterialArrivalTrackingUpdateDTO buildNeedMaterialArrivalTrackingUpdateDTO(List<MaterialArrivalTrackingVO> materialArrivalTrackingNeedList) {

        List<MaterialArrivalTrackingUpdateDTO.PurchasingPlanLine> purchasingPlanLineList = new ArrayList<>();
        for (MaterialArrivalTrackingVO materialArrivalTrackingVO : materialArrivalTrackingNeedList) {
            MaterialArrivalTrackingUpdateDTO.PurchasingPlanLine purchasingPlanLine = new MaterialArrivalTrackingUpdateDTO.PurchasingPlanLine();
            purchasingPlanLine.setMaterialPlanNeedId(materialArrivalTrackingVO.getSourceId());
            purchasingPlanLine.setOnroadQty(null != materialArrivalTrackingVO.getPredictArrivalQuantity() ?
                    materialArrivalTrackingVO.getPredictArrivalQuantity() : BigDecimal.ZERO);
            purchasingPlanLine.setReceivedQty(null != materialArrivalTrackingVO.getInventoryQuantity() ?
                    materialArrivalTrackingVO.getInventoryQuantity() : BigDecimal.ZERO);
            purchasingPlanLine.setWaitingQty(null != materialArrivalTrackingVO.getWaitDeliveryQuantity() ?
                    materialArrivalTrackingVO.getWaitDeliveryQuantity() : BigDecimal.ZERO);
            purchasingPlanLineList.add(purchasingPlanLine);
        }

        // 构建 MaterialArrivalTrackingUpdateDTO
        MaterialArrivalTrackingUpdateDTO dto = new MaterialArrivalTrackingUpdateDTO();
        MaterialArrivalTrackingUpdateDTO.Service service = new MaterialArrivalTrackingUpdateDTO.Service();
        MaterialArrivalTrackingUpdateDTO.Route route = new MaterialArrivalTrackingUpdateDTO.Route();
        route.setSerialNo("2024121203018000001");
        route.setServiceId("02003000000011");
        route.setSourceSysId("03018");
        route.setServiceTime("20241212152805");
        service.setRoute(route);

        MaterialArrivalTrackingUpdateDTO.Data data = new MaterialArrivalTrackingUpdateDTO.Data();
        MaterialArrivalTrackingUpdateDTO.Control control = new MaterialArrivalTrackingUpdateDTO.Control();
        control.setPathVariable(null);
        data.setControl(control);

        MaterialArrivalTrackingUpdateDTO.Request request = new MaterialArrivalTrackingUpdateDTO.Request();
        MaterialArrivalTrackingUpdateDTO.ListInfo listInfo = new MaterialArrivalTrackingUpdateDTO.ListInfo();
        listInfo.setPurchasingPlanLine(purchasingPlanLineList);
        request.setList(listInfo);
        data.setRequest(request);

        MaterialArrivalTrackingUpdateDTO.Response response = new MaterialArrivalTrackingUpdateDTO.Response();
        response.setUpdatePOResult(null);
        data.setResponse(response);
        service.setData(data);
        dto.setService(service);

        return dto;
    }

    /**
     * 构建到货跟踪更新MaterialArrivalTrackingUpdateDTO（PR模式）
     *
     * @param materialArrivalTrackingPrList 到货跟踪（PR模式）
     * @return MaterialArrivalTrackingUpdateDTO
     */
    private MaterialArrivalTrackingUpdateDTO buildPRMaterialArrivalTrackingUpdateDTO(List<MaterialArrivalTrackingVO> materialArrivalTrackingPrList) {
        List<MaterialArrivalTrackingUpdateDTO.PurchasingPlanLine> purchasingPlanLineList = new ArrayList<>();
        for (MaterialArrivalTrackingVO materialArrivalTrackingVO : materialArrivalTrackingPrList) {
            MaterialArrivalTrackingUpdateDTO.PurchasingPlanLine purchasingPlanLine = new MaterialArrivalTrackingUpdateDTO.PurchasingPlanLine();
            purchasingPlanLine.setPrNo(materialArrivalTrackingVO.getPurchaseRequestCode());
            purchasingPlanLine.setLineNo(materialArrivalTrackingVO.getPurchaseOrderLineCode());
            purchasingPlanLine.setRequiredQty(null != materialArrivalTrackingVO.getRequireQuantity() ?
                    materialArrivalTrackingVO.getRequireQuantity() : BigDecimal.ZERO);
            purchasingPlanLine.setOnroadQty(null != materialArrivalTrackingVO.getPredictArrivalQuantity() ?
                    materialArrivalTrackingVO.getPredictArrivalQuantity() : BigDecimal.ZERO);
            purchasingPlanLine.setReceivedQty(null != materialArrivalTrackingVO.getInventoryQuantity() ?
                    materialArrivalTrackingVO.getInventoryQuantity() : BigDecimal.ZERO);
            purchasingPlanLine.setWaitingQty(null != materialArrivalTrackingVO.getWaitDeliveryQuantity() ?
                    materialArrivalTrackingVO.getWaitDeliveryQuantity() : BigDecimal.ZERO);
            purchasingPlanLine.setPromiseDate(null != materialArrivalTrackingVO.getPredictArrivalDate() ?
                    DateUtils.dateToString(materialArrivalTrackingVO.getPredictArrivalDate()) : null);
            purchasingPlanLine.setUpdateReason(UpdateReasonEnum.getCodeByDesc(materialArrivalTrackingVO.getUpdateReason()));
            purchasingPlanLineList.add(purchasingPlanLine);
        }

        // 构建 MaterialArrivalTrackingUpdateDTO
        MaterialArrivalTrackingUpdateDTO dto = new MaterialArrivalTrackingUpdateDTO();
        MaterialArrivalTrackingUpdateDTO.Service service = new MaterialArrivalTrackingUpdateDTO.Service();
        MaterialArrivalTrackingUpdateDTO.Route route = new MaterialArrivalTrackingUpdateDTO.Route();
        route.setSerialNo("2024121203018000001");
        route.setServiceId("02003000000012");
        route.setSourceSysId("03018");
        route.setServiceTime("20241212152805");
        service.setRoute(route);

        MaterialArrivalTrackingUpdateDTO.Data data = new MaterialArrivalTrackingUpdateDTO.Data();
        MaterialArrivalTrackingUpdateDTO.Control control = new MaterialArrivalTrackingUpdateDTO.Control();
        control.setPathVariable(null);
        data.setControl(control);

        MaterialArrivalTrackingUpdateDTO.Request request = new MaterialArrivalTrackingUpdateDTO.Request();
        MaterialArrivalTrackingUpdateDTO.ListInfo listInfo = new MaterialArrivalTrackingUpdateDTO.ListInfo();
        listInfo.setPurchasingPlanLine(purchasingPlanLineList);
        request.setList(listInfo);
        data.setRequest(request);

        MaterialArrivalTrackingUpdateDTO.Response response = new MaterialArrivalTrackingUpdateDTO.Response();
        response.setUpdatePOResult(null);
        data.setResponse(response);
        service.setData(data);
        dto.setService(service);

        return dto;
    }

    /**
     * 构建到货跟踪MaterialPlanNeedReleaseDayDTO（30天要货计划发布）
     *
     * @param materialArrivalTrackingVOList   到货跟踪（30天要货计划发布）
     * @param materialPlanNeedVOMap           要货计划
     * @param materialPlanNeedIssueDetailsMap 要货计划下发记录
     * @return MaterialPlanNeedReleaseDayDTO
     */
    private MaterialPlanNeedReleaseDayDTO buildReleaseDayMaterialArrivalTrackingDTO(List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList,
                                                                                    Map<String, MaterialPlanNeedVO> materialPlanNeedVOMap,
                                                                                    Map<String, List<MaterialPlanNeedIssueDetailsVO>> materialPlanNeedIssueDetailsMap) {


        // 根据要货计划号分组
        Map<String, List<MaterialArrivalTrackingVO>> materialArrivalTrackingMap = materialArrivalTrackingVOList.stream().collect(Collectors.groupingBy(MaterialArrivalTrackingVO::getMaterialPlanNeedNo));

        List<MaterialPlanNeedReleaseDayDTO.PurchasingPlan> purchasingPlanList = new ArrayList<>();
        int version = 1;
        for (Map.Entry<String, List<MaterialArrivalTrackingVO>> entry : materialArrivalTrackingMap.entrySet()) {
            // 根据来源id获取要货计划数据，随便取一个就行，属性是一样的
            MaterialPlanNeedVO materialPlanNeedVO = materialPlanNeedVOMap.get(entry.getValue().get(0).getSourceId());
            if (Objects.isNull(materialPlanNeedVO)) throw new BusinessException("要货计划id在要货计划中不存在");

            List<MaterialPlanNeedIssueDetailsVO> materialPlanNeedIssueDetailsVOS = materialPlanNeedIssueDetailsMap.get(entry.getKey());
            if (CollectionUtils.isNotEmpty(materialPlanNeedIssueDetailsVOS)) {

                // 获取明细里的版本号
                version = materialPlanNeedIssueDetailsVOS.stream().sorted((vo1, vo2) -> Integer.compare(vo2.getIssueVersion(), vo1.getIssueVersion()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getIssueVersion() + 1;
            }

            MaterialPlanNeedReleaseDayDTO.PurchasingPlan purchasingPlan = new MaterialPlanNeedReleaseDayDTO.PurchasingPlan();
            purchasingPlan.setPlanCode(entry.getKey());
            purchasingPlan.setVersionNo(version);
            purchasingPlan.setOrgId(materialPlanNeedVO.getOrganizeId() != null ? materialPlanNeedVO.getOrganizeId() : "");
            purchasingPlan.setSupplierCode(materialPlanNeedVO.getSupplierCode() != null ? materialPlanNeedVO.getSupplierCode() : "");
            purchasingPlan.setSupplierName(materialPlanNeedVO.getSupplierName() != null ? materialPlanNeedVO.getSupplierName() : "");
            purchasingPlan.setMaterialPlanNeedId(materialPlanNeedVO.getId());
            purchasingPlan.setPublisherCode(SystemHolder.getStaffCode() != null ? SystemHolder.getStaffCode() : "默认");
            purchasingPlan.setPublishDate(DateUtils.dateToString(new Date(), "yyyy-MM-dd"));
            purchasingPlan.setSupplierSouce("");

            List<MaterialPlanNeedReleaseDayDTO.PurchasingPlanLine> purchasingPlanLineList = new ArrayList<>();
            for (MaterialArrivalTrackingVO materialArrivalTrackingVO : entry.getValue()) {
                // 获取要货计划
                MaterialPlanNeedVO planNeedVO = materialPlanNeedVOMap.get(materialArrivalTrackingVO.getSourceId());

                MaterialPlanNeedReleaseDayDTO.PurchasingPlanLine purchasingPlanLine = new MaterialPlanNeedReleaseDayDTO.PurchasingPlanLine();
                purchasingPlanLine.setItemCode(planNeedVO.getProductCode() != null ? planNeedVO.getProductCode() : "");
                purchasingPlanLine.setItemName(planNeedVO.getProductName() != null ? planNeedVO.getProductName() : "");
                purchasingPlanLine.setLockDays(planNeedVO.getRequestCargoPlanLockDay() != null ? planNeedVO.getRequestCargoPlanLockDay().intValue() : 0);
                purchasingPlanLine.setRequireDate(materialArrivalTrackingVO.getRequireDate() != null ? DateUtils.dateToString(materialArrivalTrackingVO.getRequireDate()) : "");
                purchasingPlanLine.setRequireQty(materialArrivalTrackingVO.getWaitDeliveryQuantity() != null ? materialArrivalTrackingVO.getWaitDeliveryQuantity().intValue() : 0);
                purchasingPlanLine.setMatchedPo(planNeedVO.getOrderNo() != null ? planNeedVO.getOrderNo() : "");
                purchasingPlanLine.setMatchedQty(planNeedVO.getOrderQuantity() != null ? planNeedVO.getOrderQuantity().intValue() : 0);
                purchasingPlanLine.setMaterialPlanNeedId(planNeedVO.getId());
                purchasingPlanLine.setAgentCode(SystemHolder.getStaffCode() != null ? SystemHolder.getStaffCode() : "默认");
                // 拆单id
                purchasingPlanLine.setOldMaterialPlanNeedId(planNeedVO.getParentId() != null ? planNeedVO.getParentId() : "");
                // 修改原因
                if (StringUtils.isNotEmpty(materialArrivalTrackingVO.getUpdateReason())){
                    purchasingPlanLine.setUpdateReason(UpdateReasonEnum.valueOf(materialArrivalTrackingVO.getUpdateReason()).getDesc());
                }
                purchasingPlanLineList.add(purchasingPlanLine);
            }

            MaterialPlanNeedReleaseDayDTO.Lines lines = new MaterialPlanNeedReleaseDayDTO.Lines();
            lines.setPurchasingPlanLine(purchasingPlanLineList);
            purchasingPlan.setLines(lines);
            purchasingPlanList.add(purchasingPlan);
        }

        // 创建 MaterialPlanNeedReleaseDayDTO 实例
        MaterialPlanNeedReleaseDayDTO dto = new MaterialPlanNeedReleaseDayDTO();

        // 创建 Service 实例并设置属性
        MaterialPlanNeedReleaseDayDTO.Service service = new MaterialPlanNeedReleaseDayDTO.Service();
        MaterialPlanNeedReleaseDayDTO.Route route = new MaterialPlanNeedReleaseDayDTO.Route();
        route.setSerialNo("2024121203018000001");
        route.setServiceId("02003000000008");
        route.setSourceSysId("03018");
        route.setServiceTime("20241212152805");

        MaterialPlanNeedReleaseDayDTO.Data data = new MaterialPlanNeedReleaseDayDTO.Data();
        MaterialPlanNeedReleaseDayDTO.Control control = new MaterialPlanNeedReleaseDayDTO.Control();
        control.setPathVariable(null);

        MaterialPlanNeedReleaseDayDTO.Request request = new MaterialPlanNeedReleaseDayDTO.Request();
        MaterialPlanNeedReleaseDayDTO.list list = new MaterialPlanNeedReleaseDayDTO.list();

        list.setPurchasingPlanList(purchasingPlanList);

        request.setList(list);
        data.setControl(control);
        data.setRequest(request);

        service.setRoute(route);
        service.setData(data);

        dto.setService(service);

        return dto;
    }

    /**
     * 记录30天要货计划下发日志
     *
     * @param predictionDayResults 预测结果列表（即状态为下发失败或下发成功的30天数据）
     * @param materialPlanNeedMap  存放物料计划所需数据的Map
     * @param status               当前状态（下发失败或下发成功）
     */
    private void processMaterialPlanDay(List<InsertRollPredictionDayResult> predictionDayResults,
                                        Map<String, List<MaterialPlanNeedVO>> materialPlanNeedMap,
                                        String status,
                                        String dimension) {
        if (CollectionUtils.isNotEmpty(predictionDayResults)) {
            List<MaterialPlanNeedIssueDetailsDTO> materialPlanNeedIssueDetailsAddList = new ArrayList<>();
            for (InsertRollPredictionDayResult predictionDayResult : predictionDayResults) {
                List<MaterialPlanNeedVO> materialPlanNeedVOList = materialPlanNeedMap.get(predictionDayResult.getPlanCode());
                if (CollectionUtils.isNotEmpty(materialPlanNeedVOList)) {
                    for (MaterialPlanNeedVO materialPlanNeedVO : materialPlanNeedVOList) {
                        MaterialPlanNeedIssueDetailsDTO materialPlanNeedIssueDetailsDTO = new MaterialPlanNeedIssueDetailsDTO();
                        BeanUtils.copyProperties(materialPlanNeedVO, materialPlanNeedIssueDetailsDTO);
                        materialPlanNeedIssueDetailsDTO.setMaterialPlanNeedNo(predictionDayResult.getPlanCode());
                        materialPlanNeedIssueDetailsDTO.setIssueVersion(predictionDayResult.getVersionNo());
                        materialPlanNeedIssueDetailsDTO.setPlanNeedId(materialPlanNeedVO.getId());
                        materialPlanNeedIssueDetailsDTO.setStatus(status);
                        materialPlanNeedIssueDetailsDTO.setDimension(dimension);
                        materialPlanNeedIssueDetailsAddList.add(materialPlanNeedIssueDetailsDTO);
                    }
                }
            }
            Lists.partition(materialPlanNeedIssueDetailsAddList, 1000).forEach(materialPlanNeedIssueDetailsService::doCreateBatch);
        }
    }

    @Override
    public void doSyncArrivalStatus() {
        List<MaterialArrivalTrackingPO> list = materialArrivalTrackingDao.selectNeedClose();
        if (CollectionUtils.isEmpty(list)){
            return;
        }
        List<MaterialArrivalTrackingVO> voList = MaterialArrivalTrackingConvertor.INSTANCE.po2Vos(list);
        List<MaterialArrivalTrackingDTO> materialArrivalTrackingDTOS = MaterialArrivalTrackingConvertor.INSTANCE.vo2Dtos(voList);
        materialArrivalTrackingDTOS.forEach(item -> item.setArrivalStatus(ArrivalStatusEnum.CLOSE.getCode()));
        Lists.partition(materialArrivalTrackingDTOS, 500).forEach(this::doUpdateBatchSelective);
    }

    @Override
    public PageInfo<ConsistencyWarningVO> consistencyWarning(String startDate, String endDate, String source, Integer pageNum, Integer pageSize) {
        List<ConsistencyWarningVO> result = new ArrayList<>();

        // 获取 1 天
        Date endModifyTime = DateUtils.moveDay(new Date(), -1);

        // 超过1天未匹配的送货单
        List<MaterialDeliveryNoteVO> materialDeliveryNoteVOList = materialDeliveryNoteService.selectByParams(
                        ImmutableMap.of("endModifyTime", endModifyTime)).stream()
                .filter(data -> StringUtils.isEmpty(data.getWhetherMatches()))
                .collect(Collectors.toList());
        List<ConsistencyWarningVO> noteResult = materialDeliveryNoteVOList.stream()
                .map(data -> {
                    return ConsistencyWarningVO.builder()
                            .purchaseOrderCode(data.getPurchaseOrderCode())
                            .purchaseOrderLineCode(data.getPurchaseOrderLineCode())
                            .productCode(data.getProductCode())
                            .deliveryNoteCode(data.getDeliveryNoteCode())
                            .modifyTime(data.getModifyTime())
                            .source(ConsistencyWarningSourceEnum.NOTE.getCode())
                            .build();
                }).collect(Collectors.toList());
        result.addAll(noteResult);

        // 超过1天未匹配的入库记录
        List<MaterialPurchaseStorageVO> materialPurchaseStorageVOList = materialPurchaseStorageService.selectByParams(
                        ImmutableMap.of("endModifyTime", endModifyTime)).stream()
                .filter(data -> StringUtils.isEmpty(data.getWhetherMatches()))
                .collect(Collectors.toList());

        List<ConsistencyWarningVO> storageResult = materialPurchaseStorageVOList.stream()
                .map(data -> {
                    return ConsistencyWarningVO.builder()
                            .purchaseOrderCode(data.getPurchaseOrderCode())
                            .purchaseOrderLineCode(data.getPurchaseOrderLineCode())
                            .productCode(data.getProductCode())
                            .deliveryNoteCode(data.getDeliveryNoteCode())
                            .modifyTime(data.getModifyTime())
                            .source(ConsistencyWarningSourceEnum.STORAGE.getCode())
                            .build();
                }).collect(Collectors.toList());
        result.addAll(storageResult);

        // 超过1天未匹配的申请单
        List<MaterialPurchaseRequestVO> materialPurchaseRequestVOList = materialPurchaseRequestService.selectByParams(
                        ImmutableMap.of("endModifyTime", endModifyTime)).stream()
                .filter(data -> StringUtils.isEmpty(data.getWhetherMatches()))
                .collect(Collectors.toList());

        List<ConsistencyWarningVO> requestResult = materialPurchaseRequestVOList.stream()
                .map(data -> {
                    return ConsistencyWarningVO.builder()
                            .purchaseOrderCode(data.getPurchaseOrderCode())
                            .purchaseOrderLineCode(data.getPurchaseOrderLineCode())
                            .purchaseRequestCode(data.getPurchaseRequestCode())
                            .purchaseRequestLineCode(data.getPurchaseRequestLineCode())
                            .modifyTime(data.getModifyTime())
                            .source(ConsistencyWarningSourceEnum.REQUEST.getCode())
                            .build();
                }).collect(Collectors.toList());
        result.addAll(requestResult);

        // 获取一天内没匹配的退货记录
        List<MaterialReturnedPurchaseVO> materialReturnedPurchaseVOList = materialReturnedPurchaseService.selectByParams(
                        ImmutableMap.of("endModifyTime", endModifyTime)).stream()
                .filter(data -> StringUtils.isEmpty(data.getWhetherMatches()))
                .collect(Collectors.toList());

        List<ConsistencyWarningVO> returnResult = materialReturnedPurchaseVOList.stream()
                .map(data -> {
                    return ConsistencyWarningVO.builder()
                            .purchaseOrderCode(data.getPoNumber())
                            .purchaseOrderLineCode(data.getLineNum())
                            .productCode(data.getProductCode())
                            .modifyTime(data.getModifyTime())
                            .source(ConsistencyWarningSourceEnum.RETURN.getCode())
                            .build();
                }).collect(Collectors.toList());
        result.addAll(returnResult);

        List<String> productCodes = result.stream().map(ConsistencyWarningVO::getProductCode).distinct().collect(Collectors.toList());

        // 获取物料
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("product_code", "product_classify"))
                .queryParam(ImmutableMap.of("productCodes", productCodes))
                .build();
        List<NewProductStockPointVO> newProductStockPointVOS =
                newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);

        // 筛选出B类 和 PVB 的物料
        List<String> needProductCode = newProductStockPointVOS.stream().filter(data -> (data.getProductClassify().contains("RA.V")) ||
                        (data.getProductClassify().contains("BB") ||
                                data.getProductClassify().contains("BG") ||
                                data.getProductClassify().contains("BJ")))
                .map(NewProductStockPointVO::getProductCode).collect(Collectors.toList());

        // 只保留B类 和 PVB 的预测
        result = result.stream().filter(data -> needProductCode.contains(data.getProductCode())).collect(Collectors.toList());

        if (null != source) {
            result = result.stream().filter(data -> data.getSource().equals(source)).collect(Collectors.toList());
        }
        if (null != startDate) {
            Date start = DateUtils.stringToDate(startDate);
            result = result.stream()
                    .filter(data -> {
                        Date modifyTime = data.getModifyTime();
                        Date modifyDateOnly = DateUtils.formatDate(modifyTime, "yyyy-MM-dd");
                        return !modifyDateOnly.before(start);
                    })
                    .collect(Collectors.toList());
        }

        if (null != endDate) {
            Date end = DateUtils.stringToDate(endDate);
            result = result.stream()
                    .filter(data -> {
                        Date modifyTime = data.getModifyTime();
                        Date modifyDateOnly = DateUtils.formatDate(modifyTime, "yyyy-MM-dd");
                        return !modifyDateOnly.after(end);
                    })
                    .collect(Collectors.toList());
        }

        // 计算分页的起始和结束位置
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, result.size());

        // 物理分页
        List<ConsistencyWarningVO> pageData = Collections.emptyList();
        if (startIndex < result.size()) {
            pageData = result.subList(startIndex, endIndex);
        }

        // 构建PageInfo对象，包含分页信息和当前页数据
        PageInfo<ConsistencyWarningVO> pageInfo = new PageInfo<>(pageData);
        pageInfo.setTotal(result.size());
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages((int) Math.ceil((double) result.size() / pageSize));
        pageInfo.setHasNextPage(endIndex < result.size());
        pageInfo.setHasPreviousPage(pageNum > 1);

        return pageInfo;
    }

    @Override
    public BaseResponse<Void> handlePrCancel(ErpResponse response) {
        return null;
    }

    @Override
    public BaseResponse<Void> prCancel(String tenantId, Map<String, Object> params) {
        params.put("scenario", SystemHolder.getScenario());
        BaseResponse<String> response = newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                ApiCategoryEnum.PR_CANCEL.getCode(), params);

        if (Boolean.FALSE.equals(response.getSuccess())) {
            throw new BusinessException(response.getMsg());
        }

        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> handlePoClosed(ErpResponse response) {
        return null;
    }

    @Override
    public BaseResponse<Void> poClosed(String tenantId, Map<String, Object> params) {
        params.put("scenario", SystemHolder.getScenario());
        BaseResponse<String> response = newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                ApiCategoryEnum.PO_CLOSED.getCode(), params);

        if (Boolean.FALSE.equals(response.getSuccess())) {
            throw new BusinessException(response.getMsg());
        }

        return BaseResponse.success("同步成功");
    }

    @Override
    public void doUpdateNeedDate(List<MaterialArrivalTrackingDTO> list) {
        if (CollectionUtils.isEmpty(list)){
            return;
        }
        List<MaterialArrivalTrackingDTO> filterList = list.stream().filter(item -> null != item.getRecommendRequireDate()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)){
            return;
        }

        List<MaterialArrivalTrackingDTO> planNeedDataList = filterList.stream()
                .filter(item -> org.apache.commons.lang3.StringUtils.equals(ArrivalTrackingDataSourceEnum.PURCHASING_PLAN.getCode(), item.getDataSource()))
                .collect(Collectors.toList());

        // 更新要货计划的要货日期
        if (CollectionUtils.isNotEmpty(planNeedDataList)){
            List<MaterialPlanNeedDTO> updateList = new ArrayList<>();
            for (MaterialArrivalTrackingDTO materialArrivalTrackingDTO : planNeedDataList) {
                MaterialPlanNeedDTO materialPlanNeedDTO = new MaterialPlanNeedDTO();
                materialPlanNeedDTO.setId(materialArrivalTrackingDTO.getSourceId());
                materialPlanNeedDTO.setNeedDate(materialArrivalTrackingDTO.getRecommendRequireDate());
                materialPlanNeedDTO.setExpectedArrivalTime(materialArrivalTrackingDTO.getRecommendRequireDate());
                updateList.add(materialPlanNeedDTO);
            }
            materialPlanNeedService.doUpdateBatchSelective(updateList);
        }

        // 更新到货跟踪的日期
        List<String> ids = filterList.stream()
                .map(MaterialArrivalTrackingDTO::getId)
                .collect(Collectors.toList());
        Map<String, MaterialArrivalTrackingDTO> trackingDTOMap = filterList.stream()
                .collect(Collectors.toMap(MaterialArrivalTrackingDTO::getId, Function.identity()));
        Date date = new Date();
        List<MaterialArrivalTrackingPO> materialArrivalTrackingPOList = materialArrivalTrackingDao.selectByPrimaryKeys(ids);
        for (MaterialArrivalTrackingPO materialArrivalTrackingPO : materialArrivalTrackingPOList) {
            materialArrivalTrackingPO.setPredictArrivalDate(trackingDTOMap.get(materialArrivalTrackingPO.getId()).getRecommendRequireDate());
            materialArrivalTrackingPO.setRecommendRequireDate(null);
            materialArrivalTrackingPO.setModifier(SystemHolder.getUserId());
            materialArrivalTrackingPO.setModifyTime(date);
        }
        materialArrivalTrackingDao.updateBatch(materialArrivalTrackingPOList);
    }

    @Override
    public void doIssuePlanNeed(List<String> idList) {
        // 根据ID查询导购跟踪数据
        List<MaterialArrivalTrackingPO> materialArrivalTrackingPOList = materialArrivalTrackingDao.selectByPrimaryKeys(idList);
        // 过滤出要货计划生成的数据
        List<MaterialArrivalTrackingPO> filterList = materialArrivalTrackingPOList.stream()
                .filter(item -> StringUtils.equals(item.getDataSource(), ArrivalTrackingDataSourceEnum.PURCHASING_PLAN.getCode()))
                .collect(Collectors.toList());
        // 过滤出要货计划生成的数据
        if (CollectionUtils.isEmpty((filterList))){
            return;
        }
        List<String> sourceIds = filterList.stream()
                .map(MaterialArrivalTrackingPO::getSourceId)
                .collect(Collectors.toList());

        // 调用要货计划发布接口
        materialPlanNeedService.issueData(sourceIds, true);
    }

    @Override
    public BaseResponse<Void> issue(List<String> idList, Boolean checkPermission) {
        // 查询对应到货跟踪
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList = this.selectVOByParams(ImmutableMap.of("ids", idList));

        if (Boolean.TRUE.equals(checkPermission)){
            // 校验权限
            FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                    .dynamicColumnParam(com.google.common.collect.Lists.newArrayList("product_code"))
                    .queryParam(com.alibaba.nacos.shaded.com.google.common.collect.ImmutableMap.of("materialPlanner", SystemHolder.getUserId()))
                    .build();
            List<String> permissionProductCodes = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam).stream()
                    .map(NewProductStockPointVO::getProductCode)
                    .distinct().collect(Collectors.toList());

            if (permissionProductCodes.isEmpty()) throw new BusinessException("权限下无可用物料");
            materialArrivalTrackingVOList = materialArrivalTrackingVOList.stream()
                    .filter(data -> permissionProductCodes.contains(data.getMaterialCode()))
                    .collect(Collectors.toList());
        }

        materialArrivalTrackingVOList = materialArrivalTrackingVOList.stream()
                .filter(data -> data.getArrivalStatus().equals(ArrivalStatusEnum.PLAN_PRUCHASE.getCode()) ||
                        data.getArrivalStatus().equals(ArrivalStatusEnum.DELIVERED.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(materialArrivalTrackingVOList)) throw new BusinessException("无可用到货跟踪数据下发");

        // 当前日期
        String strNowDate = DateUtils.dateToString(new Date(), "yyyyMMdd");
        List<String> modifierList = materialArrivalTrackingVOList.stream().map(BaseVO::getModifier).distinct().collect(Collectors.toList());
        // 根据单号（DH + 当前日期 + 供应商编码） 分组
        Map<String, List<MaterialArrivalTrackingVO>> materialArrivalTrackingMap = materialArrivalTrackingVOList.stream()
                .filter(data -> null != data.getSupplierCode())
                .collect(Collectors.groupingBy(data -> "DH" + strNowDate + data.getSupplierCode()));
        // 收集到货跟踪id
        List<String> ids = materialArrivalTrackingVOList.stream().map(BaseVO::getId).collect(Collectors.toList());
        // 查询到货跟踪下发明细
        List<MaterialArrivalTrackingIssueDetailVO> issueDetailVOList = materialArrivalTrackingIssueDetailService.selectVOByParams(
                ImmutableMap.of("materialArrivalTrackingIds", ids));
        // 根据单号（DH + 当前日期 + 供应商编码） 分组
        Map<String, List<MaterialArrivalTrackingIssueDetailVO>> issueDetailVOMap = issueDetailVOList.stream()
                .collect(Collectors.groupingBy(data -> "DH" + strNowDate + data.getSupplierCode()));

        Map<String, String> userMap = ipsNewFeign.selectUserByParams(SystemHolder.getScenario(), ImmutableMap.of("ids", modifierList)).stream()
                .collect(Collectors.toMap(User::getId, User::getCnName));

        // 构建MaterialPlanNeedReleaseDayDTO
        MaterialArrivalTrackingIssueDTO materialArrivalTrackingIssueDTO =
                buildMaterialArrivalTrackingIssueDTO(materialArrivalTrackingMap,userMap);

        try {
            // 获取当前的要货计划下发明细
            // 填充参数调用dcp服务下发30天要货计划
            HashMap<String, Object> params = new HashMap<>();
            params.put("Service", materialArrivalTrackingIssueDTO.getService());
            BaseResponse<String> response = newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.SRM.getCode(), ApiCategoryEnum.MATERIAL_ARRIVAL_TRACKING_ISSUE.getCode(), params);
            log.info("到货跟踪下发数据为{}", materialArrivalTrackingIssueDTO);
            if (response.getSuccess().equals(false)) {
                throw new BusinessException("服务接口调用失败");
            }
            // 解析嵌套的 JSON 数组
            List<InsertPurchasingArrivalTrackResult> insertPurchasingArrivalTrackResultList = JSON.parseArray(response.getData(), InsertPurchasingArrivalTrackResult.class);

            // 根据状态分组看有无异常状态
            Map<Integer, List<InsertPurchasingArrivalTrackResult>> statusDatMap =
                    insertPurchasingArrivalTrackResultList.stream().collect(Collectors.groupingBy(InsertPurchasingArrivalTrackResult::getStatus));

            // 生成版本
            MaterialArrivalTrackingIssueVersionDTO issueVersionDTO = new MaterialArrivalTrackingIssueVersionDTO();
            String version = materialArrivalTrackingIssueVersionService.getVersionCode();
            issueVersionDTO.setVersionCode(version);
            issueVersionDTO.setVersionName(version);
            String versionId = UUID.randomUUID().toString();
            issueVersionDTO.setId(versionId);

            // 记录下发失败的30天要货数据日志
            processPurchasingArrivalTrack(statusDatMap.getOrDefault(0, new ArrayList<>()), materialArrivalTrackingMap,
                    issueDetailVOMap, Boolean.FALSE.toString(),issueVersionDTO);
            // 记录下发成功的30天要货数据日志
            processPurchasingArrivalTrack(statusDatMap.getOrDefault(1, new ArrayList<>()), materialArrivalTrackingMap,
                    issueDetailVOMap,Boolean.TRUE.toString(),issueVersionDTO);

            // 记录下发版本
            materialArrivalTrackingIssueVersionService.doCreateWithPrimaryKey(issueVersionDTO);
        } catch (Exception e) {
            log.error("到货跟踪（下发）失败", e);
            throw new BusinessException("到货跟踪（下发）失败,{0}", e.getLocalizedMessage());
        }

        // 修改到货跟踪发布状态为已发布
        List<MaterialArrivalTrackingDTO> updateMaterialArrivalTrackingDTOList = materialArrivalTrackingVOList.stream().map(data -> {
            MaterialArrivalTrackingDTO trackingDTO = new MaterialArrivalTrackingDTO();
            trackingDTO.setId(data.getId());
            trackingDTO.setPublishStatus(ArrivalTrackingPublishStatusEnum.PUBLISHED.getCode());
            return trackingDTO;
        }).collect(Collectors.toList());
        this.doUpdateBatchSelective(updateMaterialArrivalTrackingDTOList);

        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    private MaterialArrivalTrackingIssueDTO buildMaterialArrivalTrackingIssueDTO(Map<String, List<MaterialArrivalTrackingVO>> materialArrivalTrackingMap,
                                                                                 Map<String, String> userMap) {
        Map<String, String> arrivalStatusEnumMap = Arrays.stream(ArrivalStatusEnum.values())
                .collect(Collectors.toMap(ArrivalStatusEnum::getCode, ArrivalStatusEnum::getDesc));

        Map<String, String> publishStatusEnumMap = Arrays.stream(ArrivalTrackingPublishStatusEnum.values())
                .collect(Collectors.toMap(ArrivalTrackingPublishStatusEnum::getCode, ArrivalTrackingPublishStatusEnum::getDesc));

        Map<String, String> dataSourceEnumMap = Arrays.stream(ArrivalTrackingDataSourceEnum.values())
                .collect(Collectors.toMap(ArrivalTrackingDataSourceEnum::getCode, ArrivalTrackingDataSourceEnum::getDesc));

        List<MaterialArrivalTrackingIssueDTO.PurchasingArrivalTrack> purchasingArrivalTrackList = new ArrayList<>();

        for (Map.Entry<String, List<MaterialArrivalTrackingVO>> entry : materialArrivalTrackingMap.entrySet()) {
            MaterialArrivalTrackingVO materialArrivalTrackingVO = entry.getValue().get(0);

            MaterialArrivalTrackingIssueDTO.PurchasingArrivalTrack purchasingArrivalTrack = new MaterialArrivalTrackingIssueDTO.PurchasingArrivalTrack();
            purchasingArrivalTrack.setArrivalCode(entry.getKey());
            purchasingArrivalTrack.setOrgId(materialArrivalTrackingVO.getOrganizationId() != null ? materialArrivalTrackingVO.getOrganizationId() : "");
            purchasingArrivalTrack.setSupplierCode(materialArrivalTrackingVO.getSupplierCode() != null ? materialArrivalTrackingVO.getSupplierCode() : "");
            purchasingArrivalTrack.setSupplierName(materialArrivalTrackingVO.getSupplierName() != null ? materialArrivalTrackingVO.getSupplierName() : "");

            List<MaterialArrivalTrackingIssueDTO.PurchasingArrivalTrackLine> purchasingArrivalTrackLineList = new ArrayList<>();
            for (MaterialArrivalTrackingVO trackingVO : entry.getValue()) {

                MaterialArrivalTrackingIssueDTO.PurchasingArrivalTrackLine purchasingArrivalTrackLine = new MaterialArrivalTrackingIssueDTO.PurchasingArrivalTrackLine();
                purchasingArrivalTrackLine.setItemCode(trackingVO.getMaterialCode() != null ? trackingVO.getMaterialCode() : "");
                purchasingArrivalTrackLine.setItemName(trackingVO.getMaterialName() != null ? trackingVO.getMaterialName() : "");
                purchasingArrivalTrackLine.setArrivalStatus(trackingVO.getArrivalStatus() != null ? arrivalStatusEnumMap.get(trackingVO.getArrivalStatus()) : "");
                purchasingArrivalTrackLine.setPoNo(trackingVO.getPurchaseOrderCode() != null ? trackingVO.getPurchaseOrderCode() : "");
                purchasingArrivalTrackLine.setPoLineNo(trackingVO.getPurchaseOrderLineCode() != null ? trackingVO.getPurchaseOrderLineCode() : "");
                purchasingArrivalTrackLine.setWaitingQty(trackingVO.getWaitDeliveryQuantity() != null ? trackingVO.getWaitDeliveryQuantity().intValue() : 0);
                purchasingArrivalTrackLine.setForecastArrivalDate(trackingVO.getPredictArrivalDate() != null ? DateUtils.dateToString(trackingVO.getPredictArrivalDate()) : "");
                purchasingArrivalTrackLine.setOnRoadQty(trackingVO.getPredictArrivalQuantity() != null ? trackingVO.getPredictArrivalQuantity().intValue() : 0);
                purchasingArrivalTrackLine.setDeliveryOrderCreatedTime(trackingVO.getShippingDate() != null ? DateUtils.dateToString(trackingVO.getShippingDate()) : "");
                purchasingArrivalTrackLine.setUom(trackingVO.getProductUnit() != null ? trackingVO.getProductUnit() : "");
                purchasingArrivalTrackLine.setDeliveryOrderCode(trackingVO.getDeliveryNoteCode() != null ? trackingVO.getDeliveryNoteCode() : "");
                purchasingArrivalTrackLine.setCreationTime(DateUtils.dateToString(trackingVO.getCreateTime()));
                purchasingArrivalTrackLine.setLastUpdateTime(DateUtils.dateToString(trackingVO.getModifyTime()));
                purchasingArrivalTrackLine.setLastUpdateBy(userMap.get(trackingVO.getModifier()));
                purchasingArrivalTrackLine.setPurchasingPlanCode(trackingVO.getMaterialPlanNeedNo() != null ? trackingVO.getMaterialPlanNeedNo() : "");
                purchasingArrivalTrackLine.setPublishStatus(trackingVO.getPublishStatus() != null ? publishStatusEnumMap.get(trackingVO.getPublishStatus()) : "");
                purchasingArrivalTrackLine.setDataSource(trackingVO.getDataSource() != null ? dataSourceEnumMap.get(trackingVO.getDataSource()) : "");
                purchasingArrivalTrackLineList.add(purchasingArrivalTrackLine);
            }

            MaterialArrivalTrackingIssueDTO.Lines lines = new MaterialArrivalTrackingIssueDTO.Lines();
            lines.setPurchasingArrivalTrackLineList(purchasingArrivalTrackLineList);
            purchasingArrivalTrack.setLines(lines);
            purchasingArrivalTrackList.add(purchasingArrivalTrack);
        }

        // 创建 MaterialPlanNeedReleaseDayDTO 实例
        MaterialArrivalTrackingIssueDTO dto = new MaterialArrivalTrackingIssueDTO();

        // 创建 Service 实例并设置属性
        MaterialArrivalTrackingIssueDTO.Service service = new MaterialArrivalTrackingIssueDTO.Service();
        MaterialArrivalTrackingIssueDTO.Route route = new MaterialArrivalTrackingIssueDTO.Route();
        route.setSerialNo("2024121203018000001");
        route.setServiceId("02003000000015");
        route.setSourceSysId("03018");
        route.setServiceTime("20241212152805");

        MaterialArrivalTrackingIssueDTO.Data data = new MaterialArrivalTrackingIssueDTO.Data();
        MaterialArrivalTrackingIssueDTO.Control control = new MaterialArrivalTrackingIssueDTO.Control();
        control.setPathVariable("");

        MaterialArrivalTrackingIssueDTO.Request request = new MaterialArrivalTrackingIssueDTO.Request();
        MaterialArrivalTrackingIssueDTO.list list = new MaterialArrivalTrackingIssueDTO.list();

        list.setPurchasingArrivalTrackList(purchasingArrivalTrackList);

        request.setList(list);
        data.setControl(control);
        data.setRequest(request);

        service.setRoute(route);
        service.setData(data);

        dto.setService(service);

        return dto;
    }

    private void processPurchasingArrivalTrack(List<InsertPurchasingArrivalTrackResult> insertPurchasingArrivalTrackResultList,
                                               Map<String, List<MaterialArrivalTrackingVO>> materialArrivalTrackingMap,
                                               Map<String, List<MaterialArrivalTrackingIssueDetailVO>> issueDetailVOMap,
                                               String status,
                                               MaterialArrivalTrackingIssueVersionDTO issueVersionDTO) {
        if (CollectionUtils.isNotEmpty(insertPurchasingArrivalTrackResultList)) {
            List<MaterialArrivalTrackingIssueDetailDTO> materialArrivalTrackingIssueDetailDTOList = new ArrayList<>();

            int version = 1;
            for (InsertPurchasingArrivalTrackResult arrivalTrackResult : insertPurchasingArrivalTrackResultList) {
                // 根据DH单号获取明细
                List<MaterialArrivalTrackingIssueDetailVO> materialArrivalTrackingIssueDetailVOList = issueDetailVOMap.get(arrivalTrackResult.getArrivalCode());
                if (CollectionUtils.isNotEmpty(materialArrivalTrackingIssueDetailVOList)) {
                    // 获取明细里的版本号
                    version = materialArrivalTrackingIssueDetailVOList.stream().sorted((vo1, vo2) -> Integer.compare(vo2.getIssueVersion(), vo1.getIssueVersion()))
                            .collect(Collectors.toList())
                            .get(0)
                            .getIssueVersion() + 1;
                }

                List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList = materialArrivalTrackingMap.get(arrivalTrackResult.getArrivalCode());
                if (CollectionUtils.isNotEmpty(materialArrivalTrackingVOList)) {
                    for (MaterialArrivalTrackingVO vo : materialArrivalTrackingVOList) {
                        MaterialArrivalTrackingIssueDetailDTO dto = new MaterialArrivalTrackingIssueDetailDTO();
                        BeanUtils.copyProperties(vo, dto);
                        dto.setMaterialArrivalTrackingIssueVersionId(issueVersionDTO.getId());
                        dto.setMaterialArrivalTrackingNo(arrivalTrackResult.getArrivalCode());
                        dto.setIssueVersion(version);
                        dto.setMaterialArrivalTrackingId(vo.getId());
                        dto.setStatus(status);
                        materialArrivalTrackingIssueDetailDTOList.add(dto);
                    }
                }
            }
            Lists.partition(materialArrivalTrackingIssueDetailDTOList, 1000).forEach(materialArrivalTrackingIssueDetailService::doCreateBatch);
        }
    }
}
