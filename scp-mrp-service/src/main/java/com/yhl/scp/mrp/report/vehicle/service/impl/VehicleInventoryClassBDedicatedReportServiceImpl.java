package com.yhl.scp.mrp.report.vehicle.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dfp.clean.vo.CleanDemandProductInventoryReportVO;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.MrpNewProductStockPoint.infrastructure.dao.MrpNewProductStockPointDao;
import com.yhl.scp.mrp.enums.ArrivalStatusEnum;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.plan.enums.MrpDemandSourceEnum;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftDataService;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftDetailService;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandVersionService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVersionVO;
import com.yhl.scp.mrp.mrpProductBom.infrastructure.dao.MrpProductBomDao;
import com.yhl.scp.mrp.mrpProductBom.infrastructure.dao.MrpProductBomVersionDao;
import com.yhl.scp.mrp.report.vehicle.convertor.VehicleInventoryClassBDedicatedReportConvertor;
import com.yhl.scp.mrp.report.vehicle.domain.entity.VehicleInventoryClassBDedicatedReportDO;
import com.yhl.scp.mrp.report.vehicle.domain.service.VehicleInventoryClassBDedicatedReportDomainService;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassBDedicatedReportDTO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassBFactoryReportDTO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassDataReportDTO;
import com.yhl.scp.mrp.report.vehicle.infrastructure.dao.VehicleInventoryClassBDedicatedReportDao;
import com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassBDedicatedReportPO;
import com.yhl.scp.mrp.report.vehicle.service.*;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBDedicatedReportVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.supplier.enums.MaterialTypeEnum;
import com.yhl.scp.mrp.supplier.service.MaterialSupplierPurchaseService;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yhl.scp.mrp.report.vehicle.service.impl.VehicleInventoryClassBFactoryReportServiceImpl.getNext30Days;
import static com.yhl.scp.mrp.report.vehicle.service.impl.VehicleInventoryClassBFactoryReportServiceImpl.getNextThreeMonths;

/**
 * <code>VehicleInventoryClassBDedicatedReportServiceImpl</code>
 * <p>
 * 车型库存（B类专用）报表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:47:32
 */
@Slf4j
@Service
public class VehicleInventoryClassBDedicatedReportServiceImpl extends AbstractService implements VehicleInventoryClassBDedicatedReportService {

    @Resource
    private VehicleInventoryClassBDedicatedReportDao vehicleInventoryClassBDedicatedReportDao;

    @Resource
    private VehicleInventoryClassBDedicatedReportDomainService vehicleInventoryClassBDedicatedReportDomainService;

    @Resource
    private VehicleInventoryClassDataReportService vehicleInventoryClassDataReportService;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(VehicleInventoryClassBDedicatedReportDTO vehicleInventoryClassBDedicatedReportDTO) {
        // 0.数据转换
        VehicleInventoryClassBDedicatedReportDO vehicleInventoryClassBDedicatedReportDO = VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.dto2Do(vehicleInventoryClassBDedicatedReportDTO);
        VehicleInventoryClassBDedicatedReportPO vehicleInventoryClassBDedicatedReportPO = VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.dto2Po(vehicleInventoryClassBDedicatedReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        vehicleInventoryClassBDedicatedReportDomainService.validation(vehicleInventoryClassBDedicatedReportDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(vehicleInventoryClassBDedicatedReportPO);
        vehicleInventoryClassBDedicatedReportDao.insert(vehicleInventoryClassBDedicatedReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(VehicleInventoryClassBDedicatedReportDTO vehicleInventoryClassBDedicatedReportDTO) {
        // 0.数据转换
        VehicleInventoryClassBDedicatedReportDO vehicleInventoryClassBDedicatedReportDO = VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.dto2Do(vehicleInventoryClassBDedicatedReportDTO);
        VehicleInventoryClassBDedicatedReportPO vehicleInventoryClassBDedicatedReportPO = VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.dto2Po(vehicleInventoryClassBDedicatedReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        vehicleInventoryClassBDedicatedReportDomainService.validation(vehicleInventoryClassBDedicatedReportDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(vehicleInventoryClassBDedicatedReportPO);
        vehicleInventoryClassBDedicatedReportDao.update(vehicleInventoryClassBDedicatedReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<VehicleInventoryClassBDedicatedReportDTO> list) {
        List<VehicleInventoryClassBDedicatedReportPO> newList = VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        vehicleInventoryClassBDedicatedReportDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<VehicleInventoryClassBDedicatedReportDTO> list) {
        List<VehicleInventoryClassBDedicatedReportPO> newList = VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        vehicleInventoryClassBDedicatedReportDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return vehicleInventoryClassBDedicatedReportDao.deleteBatch(idList);
        }
        return vehicleInventoryClassBDedicatedReportDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public VehicleInventoryClassBDedicatedReportVO selectByPrimaryKey(String id) {
        VehicleInventoryClassBDedicatedReportPO po = vehicleInventoryClassBDedicatedReportDao.selectByPrimaryKey(id);
        return VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "VEHICLE_INVENTORY_CLASS_BDEDICATED_REPORT")
    public List<VehicleInventoryClassBDedicatedReportVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "VEHICLE_INVENTORY_CLASS_BDEDICATED_REPORT")
    public List<VehicleInventoryClassBDedicatedReportVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<VehicleInventoryClassBDedicatedReportVO> dataList = vehicleInventoryClassBDedicatedReportDao.selectByCondition(sortParam, queryCriteriaParam);
        VehicleInventoryClassBDedicatedReportServiceImpl target = SpringBeanUtils.getBean(VehicleInventoryClassBDedicatedReportServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<VehicleInventoryClassBDedicatedReportVO> selectByParams(Map<String, Object> params) {
        List<VehicleInventoryClassBDedicatedReportPO> list = vehicleInventoryClassBDedicatedReportDao.selectByParams(params);
        return VehicleInventoryClassBDedicatedReportConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<VehicleInventoryClassBDedicatedReportVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void generateReport() throws Exception {
        List<VehicleInventoryClassDataReportDTO> dataList = new ArrayList<>();
        vehicleInventoryClassDataReportService.assembleVehicleInventoryFactoryReport(
                Collections.singletonList(MaterialTypeEnum.B_TYPE.getCode()), dataList);
        // 映射DTO
        List<VehicleInventoryClassBDedicatedReportDTO> addList = dataList.stream().map(data -> {
            VehicleInventoryClassBDedicatedReportDTO dto = new VehicleInventoryClassBDedicatedReportDTO();
            BeanUtils.copyProperties(data, dto);
            return dto;
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(addList)) {
            // 清空表（每次只保留最新计算数据）
            this.deleteAll();
            Lists.partition(addList, 500).forEach(this::doCreateBatch);
        }
    }

    private void deleteAll() {
        vehicleInventoryClassBDedicatedReportDao.deleteAll();
    }

    @Override
    public String getObjectType() {
        // return ObjectTypeEnum.VEHICLE_INVENTORY_CLASS_BDEDICATED_REPORT.getCode();
        return null;
    }

    @Override
    public List<VehicleInventoryClassBDedicatedReportVO> invocation(List<VehicleInventoryClassBDedicatedReportVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
