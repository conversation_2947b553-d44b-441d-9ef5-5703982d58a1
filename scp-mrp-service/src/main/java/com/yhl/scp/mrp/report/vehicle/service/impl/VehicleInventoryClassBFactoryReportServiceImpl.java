package com.yhl.scp.mrp.report.vehicle.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.clean.vo.CleanDemandProductInventoryReportVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.plan.enums.MrpDemandSourceEnum;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.report.vehicle.convertor.VehicleInventoryClassBFactoryReportConvertor;
import com.yhl.scp.mrp.report.vehicle.domain.entity.VehicleInventoryClassBFactoryReportDO;
import com.yhl.scp.mrp.report.vehicle.domain.service.VehicleInventoryClassBFactoryReportDomainService;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassBFactoryReportDTO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassDataReportDTO;
import com.yhl.scp.mrp.report.vehicle.infrastructure.dao.VehicleInventoryClassBFactoryReportDao;
import com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassBFactoryReportPO;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassBFactoryReportService;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassDataReportService;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBFactoryReportVO;
import com.yhl.scp.mrp.supplier.enums.MaterialTypeEnum;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>VehicleInventoryClassBFactoryReportServiceImpl</code>
 * <p>
 * 车型库存（B类本厂）报表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:48:34
 */
@Slf4j
@Service
public class VehicleInventoryClassBFactoryReportServiceImpl extends AbstractService implements VehicleInventoryClassBFactoryReportService {

    @Resource
    private VehicleInventoryClassBFactoryReportDao vehicleInventoryClassBFactoryReportDao;

    @Resource
    private VehicleInventoryClassBFactoryReportDomainService vehicleInventoryClassBFactoryReportDomainService;

    @Resource
    private VehicleInventoryClassDataReportService vehicleInventoryClassDataReportService;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(VehicleInventoryClassBFactoryReportDTO vehicleInventoryClassBFactoryReportDTO) {
        // 0.数据转换
        VehicleInventoryClassBFactoryReportDO vehicleInventoryClassBFactoryReportDO = VehicleInventoryClassBFactoryReportConvertor.INSTANCE.dto2Do(vehicleInventoryClassBFactoryReportDTO);
        VehicleInventoryClassBFactoryReportPO vehicleInventoryClassBFactoryReportPO = VehicleInventoryClassBFactoryReportConvertor.INSTANCE.dto2Po(vehicleInventoryClassBFactoryReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        vehicleInventoryClassBFactoryReportDomainService.validation(vehicleInventoryClassBFactoryReportDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(vehicleInventoryClassBFactoryReportPO);
        vehicleInventoryClassBFactoryReportDao.insert(vehicleInventoryClassBFactoryReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(VehicleInventoryClassBFactoryReportDTO vehicleInventoryClassBFactoryReportDTO) {
        // 0.数据转换
        VehicleInventoryClassBFactoryReportDO vehicleInventoryClassBFactoryReportDO = VehicleInventoryClassBFactoryReportConvertor.INSTANCE.dto2Do(vehicleInventoryClassBFactoryReportDTO);
        VehicleInventoryClassBFactoryReportPO vehicleInventoryClassBFactoryReportPO = VehicleInventoryClassBFactoryReportConvertor.INSTANCE.dto2Po(vehicleInventoryClassBFactoryReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        vehicleInventoryClassBFactoryReportDomainService.validation(vehicleInventoryClassBFactoryReportDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(vehicleInventoryClassBFactoryReportPO);
        vehicleInventoryClassBFactoryReportDao.update(vehicleInventoryClassBFactoryReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<VehicleInventoryClassBFactoryReportDTO> list) {
        List<VehicleInventoryClassBFactoryReportPO> newList = VehicleInventoryClassBFactoryReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        vehicleInventoryClassBFactoryReportDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<VehicleInventoryClassBFactoryReportDTO> list) {
        List<VehicleInventoryClassBFactoryReportPO> newList = VehicleInventoryClassBFactoryReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        vehicleInventoryClassBFactoryReportDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return vehicleInventoryClassBFactoryReportDao.deleteBatch(idList);
        }
        return vehicleInventoryClassBFactoryReportDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public VehicleInventoryClassBFactoryReportVO selectByPrimaryKey(String id) {
        VehicleInventoryClassBFactoryReportPO po = vehicleInventoryClassBFactoryReportDao.selectByPrimaryKey(id);
        return VehicleInventoryClassBFactoryReportConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "VEHICLE_INVENTORY_CLASS_BFACTORY_REPORT")
    public List<VehicleInventoryClassBFactoryReportVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "VEHICLE_INVENTORY_CLASS_BFACTORY_REPORT")
    public List<VehicleInventoryClassBFactoryReportVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<VehicleInventoryClassBFactoryReportVO> dataList = vehicleInventoryClassBFactoryReportDao.selectByCondition(sortParam, queryCriteriaParam);
        VehicleInventoryClassBFactoryReportServiceImpl target = SpringBeanUtils.getBean(VehicleInventoryClassBFactoryReportServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<VehicleInventoryClassBFactoryReportVO> selectByParams(Map<String, Object> params) {
        List<VehicleInventoryClassBFactoryReportPO> list = vehicleInventoryClassBFactoryReportDao.selectByParams(params);
        return VehicleInventoryClassBFactoryReportConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<VehicleInventoryClassBFactoryReportVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void generateReport() throws Exception {
        List<VehicleInventoryClassDataReportDTO> dataList = new ArrayList<>();
        vehicleInventoryClassDataReportService.assembleVehicleInventoryFactoryReport(
                Collections.singletonList(MaterialTypeEnum.B_TYPE.getCode()), dataList);
        // 映射DTO
        List<VehicleInventoryClassBFactoryReportDTO> addList = dataList.stream().map(data -> {
            VehicleInventoryClassBFactoryReportDTO dto = new VehicleInventoryClassBFactoryReportDTO();
            BeanUtils.copyProperties(data, dto);
            return dto;
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(addList)) {
            // 清空表（每次只保留最新计算数据）
            this.deleteAll();
            Lists.partition(addList, 500).forEach(this::doCreateBatch);
        }
    }

    @Override
    public void assembleVehicleInventoryFactoryReport(List<String> threeMonths,
                                                      List<String> threeMonths02,
                                                      List<VehicleInventoryClassDataReportDTO> addList,
                                                      Map<String, CleanDemandProductInventoryReportVO> cleanDemandProductInventoryReportVOMap,
                                                      List<String> productCodeList,
                                                      Map<String, List<ProductBomVO>> productBomGroupOfProductCode,
                                                      Map<String, List<MaterialSupplierPurchaseVO>> materialSupplierPurchaseVOMap,
                                                      Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailVOMap,
                                                      Map<String, List<MaterialGrossDemandVO>> grossDemandGroupOfProductCode,
                                                      Map<String, List<MaterialGrossDemandVO>> grossDemandGroupOfVehicleModeCode,
                                                      Map<String, List<MaterialArrivalTrackingVO>> materialArrivalTrackingVOMap,
                                                      Map<String, List<NoGlassInventoryShiftDataVO>> shiftDataMap,
                                                      Map<String, List<NoGlassInventoryShiftDetailVO>> shiftDetailMap,
                                                      Map<String, List<ConsistenceDemandForecastDataDetailVO>> forecastDetailMap,
                                                      Map<String, NewProductStockPointVO> productStockPointVOMap) {

        for (Map.Entry<String, CleanDemandProductInventoryReportVO> entry : cleanDemandProductInventoryReportVOMap.entrySet()) {
            String key = entry.getKey();
            CleanDemandProductInventoryReportVO value = entry.getValue();

            // 获取材料编码
            List<ProductBomVO> productBomVOS = productBomGroupOfProductCode.get(key);
            if (null == productBomVOS) {
                VehicleInventoryClassDataReportDTO dto = new VehicleInventoryClassDataReportDTO();
                dto.setCustomer(value.getCustomerAbbreviation());
                dto.setVehicleModelCode(value.getVehicleModelCode());
                dto.setProductFactoryCode(value.getProductCode());
                addList.add(dto);
                continue;
            }

            for (ProductBomVO productBomVO : productBomVOS) {
                VehicleInventoryClassDataReportDTO dto = new VehicleInventoryClassDataReportDTO();
                dto.setCustomer(value.getCustomerAbbreviation());
                dto.setVehicleModelCode(value.getVehicleModelCode());
                dto.setProductFactoryCode(value.getProductCode());
                dto.setProductCode(productBomVO.getProductCode());
                dto.setProductName(productBomVO.getProductName());
                dto.setProductType(productBomVO.getProductCategory());
                dto.setInputFactor(productBomVO.getIoFactor());
                dto.setUnit(productBomVO.getMeasurementUnit());
                dto.setOutputRate(productBomVO.getYield());

                // 获取材料与供应商关系
                List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOS = materialSupplierPurchaseVOMap.get(dto.getProductCode());
                if (CollectionUtils.isNotEmpty(materialSupplierPurchaseVOS)) {
                    dto.setPurchaseLot(materialSupplierPurchaseVOS.stream()
                            .map(MaterialSupplierPurchaseVO::getOrderPlacementLeadTimeDay)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                    dto.setSupplierCode(materialSupplierPurchaseVOS.stream()
                            .map(MaterialSupplierPurchaseVO::getSupplierCode)
                            .collect(Collectors.joining(",")));
                }

                // 获取实时库存
                List<InventoryBatchDetailVO> inventoryBatchDetailVOS = inventoryBatchDetailVOMap.get(dto.getProductCode());
                if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                    InventoryBatchDetailVO inventoryBatchDetailVO = inventoryBatchDetailVOS.stream()
                            .max(Comparator.comparing(InventoryBatchDetailVO::getAssignedTime))
                            .orElse(null);
                    // 库龄天数（当前时间 - 入库时间）
                    if (null != inventoryBatchDetailVO && inventoryBatchDetailVO.getAssignedTime() != null) {
                        Date assignedDate = DateUtils.stringToDate(inventoryBatchDetailVO.getAssignedTime());
                        Date currentDate = new Date();
                        // 计算时间差（毫秒）
                        long diffInMillis = currentDate.getTime() - assignedDate.getTime();
                        // 转换为天数（向上取整为整数）
                        long days = (long) Math.ceil((double) diffInMillis / (24 * 60 * 60 * 1000));
                        dto.setStockAge(BigDecimal.valueOf(days));
                    }
                }

                // 获取非原片推移
                List<NoGlassInventoryShiftDataVO> noGlassInventoryShiftDataVOs = shiftDataMap.get(dto.getProductCode());
                if (CollectionUtils.isNotEmpty(noGlassInventoryShiftDataVOs)) {
                    BigDecimal openingInventory = noGlassInventoryShiftDataVOs.stream()
                            .map(data -> {
                                List<NoGlassInventoryShiftDetailVO> detailVOS = shiftDetailMap.get(data.getId());
                                if (CollectionUtils.isNotEmpty(detailVOS)) {
                                    return detailVOS.stream()
                                            .map(NoGlassInventoryShiftDetailVO::getOpeningInventory)
                                            .filter(Objects::nonNull)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                                }
                                return BigDecimal.ZERO;
                            })
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    dto.setFactoryInventory(openingInventory);
                }

                // 获取到货跟踪
                List<MaterialArrivalTrackingVO> materialArrivalTrackingVOs = materialArrivalTrackingVOMap.get(dto.getProductCode());
                if (CollectionUtils.isNotEmpty(materialArrivalTrackingVOs)) {
                    BigDecimal supplierInventory = materialArrivalTrackingVOs.stream()
                            .peek(data -> {
                                if (null == data.getWaitDeliveryQuantity()) {
                                    data.setWaitDeliveryQuantity(BigDecimal.ZERO);
                                }
                                if (null == data.getPredictArrivalQuantity()) {
                                    data.setPredictArrivalQuantity(BigDecimal.ZERO);
                                }
                            })
                            .map(data -> data.getWaitDeliveryQuantity().add(data.getPredictArrivalQuantity()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    dto.setSupplierInventory(supplierInventory);
                }

                // （本厂库存 + 福耀库存） ÷ 单耗 × 产出率 向下取整
                if (dto.getFactoryInventory().compareTo(BigDecimal.ZERO) > 0 && dto.getSupplierInventory().compareTo(BigDecimal.ZERO) > 0) {
                    dto.setCurrentStockPartProdQuantity((dto.getFactoryInventory()
                            .add(dto.getSupplierInventory()))
                            .divide(dto.getInputFactor(), 0, RoundingMode.DOWN)
                            .multiply(dto.getOutputRate()));
                }

                // 获取毛需求（物料维度）
                List<MaterialGrossDemandVO> materialGrossDemandVOS = grossDemandGroupOfProductCode.get(dto.getProductCode());
                if (CollectionUtils.isNotEmpty(materialGrossDemandVOS)) {
                    BigDecimal threeMonthsDemandQuantity = materialGrossDemandVOS.stream()
                            .filter(data -> threeMonths02.contains(DateUtils.dateToString(data.getDemandTime(), "yyyy-MM")))
                            .map(MaterialGrossDemandVO::getDemandQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal totalDemandQuantity = materialGrossDemandVOS.stream()
                            .map(MaterialGrossDemandVO::getDemandQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal predictedProportion = threeMonthsDemandQuantity.divide(totalDemandQuantity, 2, RoundingMode.DOWN);
                    dto.setMaterialPredictedProportion(predictedProportion + "%");

                    // 获取后30天日期
                    List<String> next30Days = getNext30Days(new Date());
                    BigDecimal threeMonthsDemandQuantity02 = materialGrossDemandVOS.stream()
                            .filter(data -> next30Days.contains(DateUtils.dateToString(data.getDemandTime(), "yyyy-MM-dd")))
                            .map(MaterialGrossDemandVO::getDemandQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    dto.setNext30DaysDailyAverageDemand(threeMonthsDemandQuantity02.divide(new BigDecimal(30), 0, RoundingMode.DOWN));
                }

                // 获取毛需求（车型维度）
                List<MaterialGrossDemandVO> materialGrossDemandVOS02 = grossDemandGroupOfVehicleModeCode.get(dto.getVehicleModelCode());
                if (CollectionUtils.isNotEmpty(materialGrossDemandVOS02)) {
                    BigDecimal threeMonthsDemandQuantity = materialGrossDemandVOS02.stream()
                            .filter(data -> threeMonths02.contains(DateUtils.dateToString(data.getDemandTime(), "yyyy-MM")))
                            .map(MaterialGrossDemandVO::getDemandQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal totalDemandQuantity = materialGrossDemandVOS02.stream()
                            .map(MaterialGrossDemandVO::getDemandQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal predictedProportionVehicle = threeMonthsDemandQuantity
                            .divide(totalDemandQuantity, 2, RoundingMode.DOWN)
                            .multiply(new BigDecimal(100));
                    dto.setMaterialPredictedProportionVehicle(String.format("%.2f%%", predictedProportionVehicle));

                    if (predictedProportionVehicle.compareTo(new BigDecimal(50)) >= 0) {
                        dto.setIsDedicatedGe50pct(YesOrNoEnum.YES.getCode());
                    } else {
                        dto.setIsDedicatedGe50pct(YesOrNoEnum.NO.getCode());
                    }
                }

                // 获取毛需求（物料维度）只取需求来源是 生产计划 或 产能平衡
                List<MaterialGrossDemandVO> materialGrossDemandVOS03 = grossDemandGroupOfProductCode.get(dto.getProductCode());
                if (CollectionUtils.isNotEmpty(materialGrossDemandVOS03) && dto.getFactoryInventory().compareTo(BigDecimal.ZERO) > 0) {
                    materialGrossDemandVOS03 = materialGrossDemandVOS03.stream()
                            .filter(data -> data.getDemandSource().equals(MrpDemandSourceEnum.MPS.getCode()) ||
                                    data.getDemandSource().equals(MrpDemandSourceEnum.MCB.getCode()))
                            .collect(Collectors.toList());

                    BigDecimal demandQuantity = materialGrossDemandVOS03.stream()
                            .map(MaterialGrossDemandVO::getDemandQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    if (demandQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        // 辅料库存 ÷ 毛需求
                        dto.setFactoryInventoryUseDay(dto.getFactoryInventory().divide(demandQuantity, 0, RoundingMode.DOWN).intValue());
                    }
                }

                if (CollectionUtils.isNotEmpty(materialGrossDemandVOS) && dto.getSupplierInventory().compareTo(BigDecimal.ZERO) > 0) {
                    // 获取毛需求覆盖天数
                    long size = materialGrossDemandVOS.stream()
                            .map(MaterialGrossDemandVO::getDemandTime)
                            .distinct()
                            .count();

                    // 供应商库存 ÷ （需求覆盖天数 - 本厂库存可用天数）
                    if (size - dto.getFactoryInventoryUseDay() > 0) {
                        dto.setSupplierInventoryUseDay(dto.getSupplierInventory()
                                .divide(BigDecimal.valueOf((size - dto.getFactoryInventoryUseDay())),
                                        0, RoundingMode.DOWN).intValue());
                    }
                }

                // 福耀库存可用天数 + 供应商库存可用天数
                dto.setMaterialTotalDay(dto.getFactoryInventoryUseDay() + dto.getSupplierInventoryUseDay());

                // 获取一致性需求预测
                List<ConsistenceDemandForecastDataDetailVO> consistenceDemandForecastDataDetailVOs = forecastDetailMap.get(key);
                if (CollectionUtils.isNotEmpty(consistenceDemandForecastDataDetailVOs)) {
                    Date currentDate = new Date();
                    // 获取当前月、下个月、下下个月
                    String currentMonth = DateUtils.dateToString(currentDate, "yyyy-MM");
                    String nextMonth = DateUtils.dateToString(DateUtils.moveMonth(currentDate, 1), "yyyy-MM");
                    String nextNextMonth = DateUtils.dateToString(DateUtils.moveMonth(currentDate, 2), "yyyy-MM");
                    // 汇总到List集合
                    List<String> monthList = new ArrayList<>();
                    monthList.add(currentMonth);
                    monthList.add(nextMonth);
                    monthList.add(nextNextMonth);

                    for (int i = 0; i < monthList.size(); i++) {
                        String date = monthList.get(i);
                        BigDecimal forecastQuantity = consistenceDemandForecastDataDetailVOs.stream()
                                .filter(data -> null != data.getForecastTime())
                                .filter(data -> DateUtils.dateToString(data.getForecastTime(), "yyyy-MM").equals(date))
                                .map(ConsistenceDemandForecastDataDetailVO::getForecastQuantity)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (i == 0) {
                            dto.setCurrentMonthForecastQuantity(forecastQuantity);
                        }
                        if (i == 1) {
                            dto.setNextMonthForecastQuantity(forecastQuantity);
                        }
                        if (i == 2) {
                            dto.setAfterNextMonthForecastQuantity(forecastQuantity);
                        }
                    }
                }

                // 获取物料基础
                NewProductStockPointVO newProductStockPointVO = productStockPointVOMap.get(dto.getProductCode());
                if (Objects.nonNull(newProductStockPointVO)) {
                    // （辅料库存 + 供应商库存）× 物料单价 ÷ 10000
                    dto.setTotalCostAmountThousand((dto.getFactoryInventory().add(dto.getSupplierInventory()))
                            .multiply(new BigDecimal(newProductStockPointVO.getItemCost()))
                            .divide(new BigDecimal(10000), 2, RoundingMode.DOWN));

                    // 辅料库存 × 单价 ÷ 10000
                    dto.setFactoryInventoryAmountThousand(dto.getFactoryInventory()
                            .multiply(new BigDecimal(newProductStockPointVO.getItemCost()))
                            .divide(new BigDecimal(10000), 2, RoundingMode.DOWN));

                    // 供应商库存 × 单价 ÷ 10000
                    dto.setSupplierInventoryAmountThousand(dto.getSupplierInventory()
                            .multiply(new BigDecimal(newProductStockPointVO.getItemCost()))
                            .divide(new BigDecimal(10000), 2, RoundingMode.DOWN));

                    // 未来30天日均需求 × 单价
                    dto.setDailyAverageDemandAmount(dto.getNext30DaysDailyAverageDemand().multiply(new BigDecimal(newProductStockPointVO.getItemCost())));
                }
                // 30 + 采购周期
                dto.setControlStandard(dto.getPurchaseLot().add(new BigDecimal(30)));
                addList.add(dto);
            }
        }
    }

    private void deleteAll() {
        vehicleInventoryClassBFactoryReportDao.deleteAll();
    }

    @Override
    public String getObjectType() {
        // return ObjectTypeEnum.VEHICLE_INVENTORY_CLASS_BFACTORY_REPORT.getCode();
        return null;
    }

    @Override
    public List<VehicleInventoryClassBFactoryReportVO> invocation(List<VehicleInventoryClassBFactoryReportVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    /**
     * 获取指定Date日期及其后三个月的月维度日期，返回pattern格式的列表
     *
     * @param date 输入的Date类型日期
     * @return 包含当前月及后三个月的pattern格式字符串列表
     */
    public static List<String> getNextThreeMonths(Date date, String pattern) {
        // 将java.util.Date转换为java.time.LocalDate（使用系统默认时区）
        LocalDate localDate = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        // 创建日期格式化器，用于将日期转换为yyyyMM格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);

        // 创建结果列表
        List<String> result = new ArrayList<>(3);

        // 当前日期对应的月份
        LocalDate currentDate = localDate;

        // 循环4次，获取当前月及后三个月
        for (int i = 0; i < 3; i++) {
            // 格式化当前日期并添加到列表
            result.add(currentDate.format(formatter));
            // 计算下一个月的同一天
            currentDate = currentDate.plusMonths(1);
        }
        return result;
    }

    /**
     * 获取指定日期开始的后30天日期，返回yyyy-MM-dd格式的列表
     *
     * @param date 基准日期（Date类型）
     * @return 包含30天日期的字符串列表
     * @throws IllegalArgumentException 如果输入日期为null
     */
    public static List<String> getNext30Days(Date date) {

        // 转换Date为LocalDate（使用系统默认时区）
        LocalDate localDate = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        // 定义日期格式化器（yyyy-MM-dd）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 存储结果的列表
        List<String> result = new ArrayList<>(30);

        // 循环生成30天的日期
        for (int i = 1; i <= 30; i++) {
            // 格式化当前日期并添加到列表
            result.add(localDate.plusDays(i).format(formatter));
        }

        return result;
    }
}
