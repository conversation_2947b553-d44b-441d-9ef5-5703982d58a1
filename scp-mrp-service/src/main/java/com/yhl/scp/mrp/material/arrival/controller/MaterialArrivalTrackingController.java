package com.yhl.scp.mrp.material.arrival.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.biz.common.enums.RedisKeyManageEnum;
import com.yhl.scp.mrp.extension.material.vo.ConsistencyWarningVO;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.arrival.service.MaterialDeliveryNoteService;
import com.yhl.scp.mrp.material.arrival.service.MaterialPurchaseStorageService;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequestService;
import com.yhl.scp.mrp.material.purchase.service.MaterialReturnedPurchaseService;
import com.yhl.scp.mrp.order.service.PurchaseOrderInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MaterialArrivalTrackingController</code>
 * <p>
 * 材料到货跟踪控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-21 16:57:36
 */
@Slf4j
@Api(tags = "材料到货跟踪控制器")
@RestController
@RequestMapping("materialArrivalTracking")
public class MaterialArrivalTrackingController extends BaseController {

    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;

    @Resource
    private MaterialDeliveryNoteService materialDeliveryNoteService;

    @Resource
    private MaterialPurchaseStorageService materialPurchaseStorageService;

    @Resource
    private MaterialReturnedPurchaseService materialReturnedPurchaseService;

    @Resource
    private PurchaseOrderInfoService purchaseOrderInfoService;

    @Resource
    private MaterialPurchaseRequestService materialPurchaseRequestService;

    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @BusinessMonitorLog(businessCode = "在途、到货情况查看", moduleCode = "MRP", businessFrequency = "DAY")
    public BaseResponse<PageInfo<MaterialArrivalTrackingVO>> page() {
        List<MaterialArrivalTrackingVO> materialArrivalTrackingList = materialArrivalTrackingService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialArrivalTrackingVO> pageInfo = new PageInfo<>(materialArrivalTrackingList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialArrivalTrackingDTO materialArrivalTrackingDTO) {
        return materialArrivalTrackingService.doCreate(materialArrivalTrackingDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialArrivalTrackingDTO materialArrivalTrackingDTO) {
        return materialArrivalTrackingService.doUpdate(materialArrivalTrackingDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialArrivalTrackingService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MaterialArrivalTrackingVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialArrivalTrackingService.selectByPrimaryKey(id));
    }
    
    @ApiOperation(value = "获取库存点下拉框")
    @GetMapping(value = "queryStockPointDown")
    public BaseResponse<List<LabelValue<String>>> queryStockPointDown() {
        return null;
    }

    @ApiOperation(value = "拆单")
    @PostMapping(value = "splitTheOrder")
    public BaseResponse<Void> splitTheOrder(@RequestBody List<MaterialArrivalTrackingDTO> materialArrivalTrackingDTOList) {
        return materialArrivalTrackingService.splitTheOrder(materialArrivalTrackingDTOList);
    }

    @ApiOperation(value = "关单")
    @PostMapping(value = "closeOrder")
    public BaseResponse<Void> closeOrder(@RequestBody List<String> idList) {
        try {
            return materialArrivalTrackingService.closeOrder(idList);
        } catch (Exception e) {
            log.error("关单失败", e);
            throw new BusinessException("关单失败,{0}", e.getLocalizedMessage());
        }
    }

    @ApiOperation(value = "发布")
    @PostMapping(value = "releaseOrder")
    public BaseResponse<Void> releaseOrder(@RequestBody List<String> idList) {
        try {
            return materialArrivalTrackingService.releaseOrder(idList);
        } catch (Exception e) {
            log.error("到货跟踪发布失败", e);
            throw new BusinessException("到货跟踪发布失败,{0}", e.getLocalizedMessage());
        }
    }

    @ApiOperation(value = "定时任务（送货）")
    @GetMapping(value = "/deliver")
    public BaseResponse<Void> deliver() {
        materialDeliveryNoteService.doDisposePredictArrivalForJob(-40);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "定时任务（入库）")
    @GetMapping(value = "/store")
    public BaseResponse<Void> store() {
        materialPurchaseStorageService.doDisposePurchaseStorageForJob(-40);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "定时任务（退货）")
    @GetMapping(value = "/cancel")
    public BaseResponse<Void> cancel() {
        materialReturnedPurchaseService.doReturnedJob(-40);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "定时任务（更新要货计划PO）")
    @GetMapping(value = "/updateNeedPo")
    public BaseResponse<Void> updateNeedPo() {
        purchaseOrderInfoService.doPurchaseOrderInfoJob(-40);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "定时任务（关闭PO）")
    @GetMapping(value = "/closedPo")
    public BaseResponse<Void> closedPo() {
        purchaseOrderInfoService.doClosePoJob(-40);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "PR取消")
    @GetMapping(value = "/cancelPr")
    public BaseResponse<Void> cancelPr() {
        materialPurchaseRequestService.doPrCancelJob(-40);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "一致性预警")
    @GetMapping(value = "/consistencyWarning")
    public BaseResponse<PageInfo<ConsistencyWarningVO>> consistencyWarning(@RequestParam(value = "startDate", required = false) String startDate,
                                                                       @RequestParam(value = "endDate", required = false) String endDate,
                                                                       @RequestParam(value = "source", required = false) String source,
                                                                       @RequestParam(value = "pageNum") Integer pageNum,
                                                                       @RequestParam(value = "pageSize") Integer pageSize) {
        PageInfo<ConsistencyWarningVO> warning = materialArrivalTrackingService.consistencyWarning(startDate, endDate, source,pageNum,pageSize);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, warning);
    }

    @ApiOperation(value = "更新要货日期")
    @PostMapping(value = "/updateNeedDate")
    public BaseResponse<Void> updateNeedDate(@RequestBody List<MaterialArrivalTrackingDTO> list) {
        materialArrivalTrackingService.doUpdateNeedDate(list);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "下发要货计划")
    @PostMapping(value = "/issuePlanNeed")
    public BaseResponse<Void> issuePlanNeed(@RequestBody List<String> idList) {
        materialArrivalTrackingService.doIssuePlanNeed(idList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "下发")
    @PostMapping(value = "issue")
    public BaseResponse<Void> issue(@RequestBody List<String> idList) {
        String redisKey = String.join("#", RedisKeyManageEnum.MATERIAL_ARRIVAL_TRACKING_ISSUE.getKey());
        try {
            if (Boolean.TRUE.equals(redisUtil.hasKey(redisKey))) {
                return BaseResponse.error("当前已有到货跟踪正在下发，请等待下发完成");
            }
            redisUtil.set(redisKey, SystemHolder.getUserId(), 60 * 5);
            // 下发要货计划
            return materialArrivalTrackingService.issue(idList,true);
        } catch (Exception e) {
            log.error("失败", e);
            throw new BusinessException("失败,{0}", e.getLocalizedMessage());
        } finally {
            redisUtil.delete(redisKey);
        }
    }
}
