package com.yhl.scp.mrp.report.vehicle.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.clean.vo.CleanDemandProductInventoryReportVO;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.MrpNewProductStockPoint.infrastructure.dao.MrpNewProductStockPointDao;
import com.yhl.scp.mrp.enums.ArrivalStatusEnum;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftDataService;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftDetailService;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandVersionService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.mrpProductBom.infrastructure.dao.MrpProductBomDao;
import com.yhl.scp.mrp.mrpProductBom.infrastructure.dao.MrpProductBomVersionDao;
import com.yhl.scp.mrp.report.vehicle.convertor.VehicleInventoryClassAFactoryReportConvertor;
import com.yhl.scp.mrp.report.vehicle.domain.entity.VehicleInventoryClassAFactoryReportDO;
import com.yhl.scp.mrp.report.vehicle.domain.service.VehicleInventoryClassAFactoryReportDomainService;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassAFactoryReportDTO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassBDedicatedReportDTO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassBFactoryReportDTO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassDataReportDTO;
import com.yhl.scp.mrp.report.vehicle.infrastructure.dao.VehicleInventoryClassAFactoryReportDao;
import com.yhl.scp.mrp.report.vehicle.infrastructure.po.VehicleInventoryClassAFactoryReportPO;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassAFactoryReportService;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassAReportService;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassBFactoryReportService;
import com.yhl.scp.mrp.report.vehicle.service.VehicleInventoryClassDataReportService;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassAFactoryReportVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.supplier.enums.MaterialTypeEnum;
import com.yhl.scp.mrp.supplier.service.MaterialSupplierPurchaseService;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yhl.scp.mrp.report.vehicle.service.impl.VehicleInventoryClassBFactoryReportServiceImpl.getNextThreeMonths;

/**
 * <code>VehicleInventoryClassAFactoryReportServiceImpl</code>
 * <p>
 * 车型库存管控-A类本厂编码维度报表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-28 10:34:42
 */
@Slf4j
@Service
public class VehicleInventoryClassAFactoryReportServiceImpl extends AbstractService implements VehicleInventoryClassAFactoryReportService {

    @Resource
    private VehicleInventoryClassAFactoryReportDao vehicleInventoryClassAFactoryReportDao;

    @Resource
    private VehicleInventoryClassAFactoryReportDomainService vehicleInventoryClassAFactoryReportDomainService;

    @Resource
    private VehicleInventoryClassDataReportService vehicleInventoryClassDataReportService;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(VehicleInventoryClassAFactoryReportDTO vehicleInventoryClassAFactoryReportDTO) {
        // 0.数据转换
        VehicleInventoryClassAFactoryReportDO vehicleInventoryClassAFactoryReportDO = VehicleInventoryClassAFactoryReportConvertor.INSTANCE.dto2Do(vehicleInventoryClassAFactoryReportDTO);
        VehicleInventoryClassAFactoryReportPO vehicleInventoryClassAFactoryReportPO = VehicleInventoryClassAFactoryReportConvertor.INSTANCE.dto2Po(vehicleInventoryClassAFactoryReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        vehicleInventoryClassAFactoryReportDomainService.validation(vehicleInventoryClassAFactoryReportDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(vehicleInventoryClassAFactoryReportPO);
        vehicleInventoryClassAFactoryReportDao.insert(vehicleInventoryClassAFactoryReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(VehicleInventoryClassAFactoryReportDTO vehicleInventoryClassAFactoryReportDTO) {
        // 0.数据转换
        VehicleInventoryClassAFactoryReportDO vehicleInventoryClassAFactoryReportDO = VehicleInventoryClassAFactoryReportConvertor.INSTANCE.dto2Do(vehicleInventoryClassAFactoryReportDTO);
        VehicleInventoryClassAFactoryReportPO vehicleInventoryClassAFactoryReportPO = VehicleInventoryClassAFactoryReportConvertor.INSTANCE.dto2Po(vehicleInventoryClassAFactoryReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        vehicleInventoryClassAFactoryReportDomainService.validation(vehicleInventoryClassAFactoryReportDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(vehicleInventoryClassAFactoryReportPO);
        vehicleInventoryClassAFactoryReportDao.update(vehicleInventoryClassAFactoryReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<VehicleInventoryClassAFactoryReportDTO> list) {
        List<VehicleInventoryClassAFactoryReportPO> newList = VehicleInventoryClassAFactoryReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        vehicleInventoryClassAFactoryReportDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<VehicleInventoryClassAFactoryReportDTO> list) {
        List<VehicleInventoryClassAFactoryReportPO> newList = VehicleInventoryClassAFactoryReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        vehicleInventoryClassAFactoryReportDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return vehicleInventoryClassAFactoryReportDao.deleteBatch(idList);
        }
        return vehicleInventoryClassAFactoryReportDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public VehicleInventoryClassAFactoryReportVO selectByPrimaryKey(String id) {
        VehicleInventoryClassAFactoryReportPO po = vehicleInventoryClassAFactoryReportDao.selectByPrimaryKey(id);
        return VehicleInventoryClassAFactoryReportConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "VEHICLE_INVENTORY_CLASS_AFACTORY_REPORT")
    public List<VehicleInventoryClassAFactoryReportVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "VEHICLE_INVENTORY_CLASS_AFACTORY_REPORT")
    public List<VehicleInventoryClassAFactoryReportVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<VehicleInventoryClassAFactoryReportVO> dataList = vehicleInventoryClassAFactoryReportDao.selectByCondition(sortParam, queryCriteriaParam);
        VehicleInventoryClassAFactoryReportServiceImpl target = SpringBeanUtils.getBean(VehicleInventoryClassAFactoryReportServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<VehicleInventoryClassAFactoryReportVO> selectByParams(Map<String, Object> params) {
        List<VehicleInventoryClassAFactoryReportPO> list = vehicleInventoryClassAFactoryReportDao.selectByParams(params);
        return VehicleInventoryClassAFactoryReportConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<VehicleInventoryClassAFactoryReportVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<VehicleInventoryClassAFactoryReportVO> invocation(List<VehicleInventoryClassAFactoryReportVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public void doGenerateReport() {
        List<VehicleInventoryClassDataReportDTO> dataList = new ArrayList<>();
        try {
            vehicleInventoryClassDataReportService.assembleVehicleInventoryFactoryReport(
                    Arrays.asList(MaterialTypeEnum.PVB.getCode(), MaterialTypeEnum.ORIGINAL_FILM.getCode()), dataList);
        } catch (Exception e) {
            throw new BusinessException("生成车型库存报表计算失败");
        }
        // 映射DTO
        List<VehicleInventoryClassAFactoryReportDTO> addList = dataList.stream()
                .map(VehicleInventoryClassAFactoryReportConvertor.INSTANCE::dataDto2Dto).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(addList)) {
            // 删除现有版本数据
            vehicleInventoryClassAFactoryReportDao.deleteAll();
            Lists.partition(addList, 500).forEach(this::doCreateBatch);
        }
    }
}
