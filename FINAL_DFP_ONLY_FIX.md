# 已排产量计算问题 - 仅修改DFP服务的最终解决方案

## 🎯 问题根本原因

通过深入分析MPS服务的代码，发现了问题的真正根源：

### MPS服务中的关键差异
1. **masterPlanWorkOrder方法**：调用`taskAssembleLoop`方法填充`operationVO`字段
2. **selectByMasterReq方法**：只查询数据，不填充`operationVO`字段

### taskAssembleLoop方法的关键逻辑
```java
// MPS服务中的关键代码（第243-244行）
OperationVO operationVO = OperationConvertor.INSTANCE.po2Vo(operationPO);
operationTaskVO.setOperationVO(operationVO);
```

### DFP服务的问题
DFP服务调用的是`mpsFeign.selectByMasterReq`，这个方法**没有调用`taskAssembleLoop`**，所以返回的`MasterPlanTaskVO`对象中的`operationVO`字段为空。

## 🔧 解决方案：仅修改DFP服务

### 修改1: 扩展getMasterPlanData方法
**文件**: `scp-dfp-service/src/main/java/com/yhl/scp/dfp/report/service/impl/DemandDeliveryProductionServiceImpl.java`

```java
// 修改前
return mpsFeign.selectByMasterReq(scenario, masterPlanReq);

// 修改后
List<MasterPlanTaskVO> masterPlanTasks = mpsFeign.selectByMasterReq(scenario, masterPlanReq);

// 手动填充operationVO字段（解决operationVO为空的问题）
fillOperationVO(scenario, masterPlanTasks);

return masterPlanTasks;
```

### 修改2: 添加fillOperationVO方法
**文件**: `scp-dfp-service/src/main/java/com/yhl/scp/dfp/report/service/impl/DemandDeliveryProductionServiceImpl.java`

```java
/**
 * 手动填充operationVO字段
 * 解决selectByMasterReq方法返回的数据中operationVO为空的问题
 * 参考MPS服务中taskAssembleLoop方法的实现
 */
private void fillOperationVO(String scenario, List<MasterPlanTaskVO> masterPlanTasks) {
    if (CollectionUtils.isEmpty(masterPlanTasks)) {
        return;
    }

    try {
        // 获取所有操作ID
        List<String> operationIds = masterPlanTasks.stream()
                .map(MasterPlanTaskVO::getOperationId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(operationIds)) {
            log.warn("没有找到有效的操作ID，无法填充operationVO");
            return;
        }

        // 通过MPS服务查询操作数据
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("ids", operationIds);  // 使用ids参数名
        List<OperationVO> operations = mpsFeign.selectOperationByParams(scenario, queryParams);

        if (CollectionUtils.isEmpty(operations)) {
            log.warn("查询操作数据为空，无法填充operationVO");
            return;
        }

        // 构建操作映射
        Map<String, OperationVO> operationMap = operations.stream()
                .collect(Collectors.toMap(OperationVO::getId, Function.identity(), (v1, v2) -> v1));

        // 填充operationVO字段
        int filledCount = 0;
        for (MasterPlanTaskVO task : masterPlanTasks) {
            String operationId = task.getOperationId();
            if (StringUtils.isNotBlank(operationId) && operationMap.containsKey(operationId)) {
                task.setOperationVO(operationMap.get(operationId));
                filledCount++;
            }
        }

        log.info("成功填充{}个主生产计划任务的operationVO字段", filledCount);

        // 特别关注的产品编码，增强调试
        boolean hasTargetProduct = masterPlanTasks.stream()
                .anyMatch(task -> task.getProductStockPointCode() != null && 
                         task.getProductStockPointCode().contains("01009LDR06001"));
        if (hasTargetProduct) {
            long targetTaskCount = masterPlanTasks.stream()
                    .filter(task -> task.getProductStockPointCode() != null && 
                           task.getProductStockPointCode().contains("01009LDR06001"))
                    .filter(task -> task.getOperationVO() != null)
                    .count();
            log.info("🎯 [目标产品调试] 目标产品相关任务中，成功填充operationVO的数量: {}", targetTaskCount);
        }

    } catch (Exception e) {
        log.error("填充operationVO时发生异常", e);
    }
}
```

### 修改3: 增强调试信息
**文件**: `scp-dfp-service/src/main/java/com/yhl/scp/dfp/report/service/impl/DemandDeliveryProductionServiceImpl.java`

```java
// 增强operationVO为空的调试信息
if (task.getOperationVO() == null) {
    if (isTargetProduct) {
        log.warn("🎯 [目标产品调试] 主生产计划任务{}的操作VO为空，跳过", task.getId());
    } else {
        log.info("主生产计划任务的操作VO为空，跳过");
    }
    continue;
}

if (isTargetProduct) {
    log.info("🎯 [目标产品调试] 任务{}的操作VO正常，工单ID: {}", 
            task.getId(), task.getOperationVO().getOrderId());
}
```

## 📊 修复效果预测

### 修复前
```
🎯 [目标产品调试] 找到成型相关工序任务，工序类型: 钢化
主生产计划任务的操作VO为空，跳过
🎯 [目标产品调试] 工单映射大小: 0, 顶层工单映射大小: 0
已排产量: 0
```

### 修复后
```
成功填充26个主生产计划任务的operationVO字段
🎯 [目标产品调试] 目标产品相关任务中，成功填充operationVO的数量: 6
🎯 [目标产品调试] 找到成型相关工序任务，工序类型: 钢化
🎯 [目标产品调试] 任务xxx的操作VO正常，工单ID: xxx
🎯 [目标产品调试] 工单映射大小: >0, 顶层工单映射大小: >0
🎯 [目标产品调试] 工单xxx匹配成功！累加数量: xxx, 当前总量: xxx
已排产量: 18600.0000
```

## 🧪 测试验证

### 1. 部署修复代码
- 只需要部署DFP服务的修改
- MPS服务保持不变
- 重启DFP服务

### 2. 验证步骤
1. 调用需求发货生产报表接口
2. 查看日志中的调试信息：
   - `成功填充X个主生产计划任务的operationVO字段`
   - `🎯 [目标产品调试] 目标产品相关任务中，成功填充operationVO的数量: 6`
   - `🎯 [目标产品调试] 任务xxx的操作VO正常，工单ID: xxx`
3. 确认最终结果：已排产量 = 18600.0000

### 3. 回归测试
- 验证其他产品的已排产量计算正常
- 确认DFP服务的其他功能不受影响

## 📋 修改文件清单

**仅修改DFP服务**：
1. **scp-dfp-service/src/main/java/com/yhl/scp/dfp/report/service/impl/DemandDeliveryProductionServiceImpl.java**
   - 修改`getMasterPlanData`方法，添加`fillOperationVO`调用
   - 添加`fillOperationVO`方法，手动填充`operationVO`字段
   - 增强`operationVO`为空时的调试信息

## 🎯 优势

1. **不影响MPS服务**：MPS服务保持稳定运行，不需要修改
2. **风险最小**：只修改DFP服务，影响范围可控
3. **逻辑清晰**：在DFP服务中补充缺失的数据填充逻辑
4. **易于回滚**：如果有问题，只需要回滚DFP服务

## 🎯 成功标准

1. ✅ 日志显示：`成功填充X个主生产计划任务的operationVO字段`
2. ✅ 日志显示：`🎯 [目标产品调试] 任务xxx的操作VO正常，工单ID: xxx`
3. ✅ 工单映射大小 > 0
4. ✅ 产品`01009LDR06001`已排产量 = 18600.0000
5. ✅ 其他产品已排产量计算正常

---

**修复完成日期**: 2025-01-09  
**修复类型**: DFP服务数据填充增强  
**影响范围**: 仅DFP服务已排产量计算逻辑  
**风险等级**: 低（不影响MPS服务）  
**预期效果**: 完全解决operationVO为空导致的已排产量为0问题
