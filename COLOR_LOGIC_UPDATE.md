# 颜色逻辑调整说明

## 🎨 调整概述
根据新的颜色方案，调整了装车计划和发货计划的颜色判断逻辑，简化了颜色分类，使其更符合业务需求。

## 📊 新的颜色方案

### 装车计划颜色逻辑
| 颜色值 | 颜色 | 条件 | 说明 |
|--------|------|------|------|
| "1" | 绿色 | 中转库 >= 装车计划 | 中转库存充足 |
| "2" | 浅绿色 | (中转库+在途) >= 装车计划 | 中转库+在途库存充足 |
| "3" | 橙色 | (中转库+在途) < 装车计划 | 库存不足 |

### 发货计划颜色逻辑
| 颜色值 | 颜色 | 条件 | 说明 |
|--------|------|------|------|
| "1" | 绿色 | 仓库 >= 发货计划 | 成品库存充足 |
| "2" | 浅绿色 | 仓库+成型后半品 >= 发货计划 | 成品+半成品库存充足 |
| "3" | 橙色 | 仓库+成型后半品 < 发货计划 | 库存不足 |

## 🔧 技术实现

### 关键变量说明
- **bohStock**: 中转库存
- **transportingQty**: 在途库存
- **fgStock**: 成品仓库库存
- **semiFinishedStock**: 成型后半品库存 = afterPacking + afterLamination + afterShape
- **beforeLoadingQty[0]**: 累积装车数量
- **beforeDemandQty[0]**: 累积发货需求数量

### 装车计划颜色判断
```java
// 计算累积扣减后的可用库存
BigDecimal availableTransferStock = bohStock.subtract(beforeLoadingQty[0]); // 剩余中转库存
BigDecimal availableTransferAndTransport = bohStock.add(transportingQty).subtract(beforeLoadingQty[0]); // 剩余中转库+在途

if (loadingQty.compareTo(availableTransferStock) <= 0) {
    detail.setLoadingColor("1"); // 绿色：中转库>=装车计划
} else if (loadingQty.compareTo(availableTransferAndTransport) <= 0) {
    detail.setLoadingColor("2"); // 浅绿色：(中转库+在途)>=装车计划
} else {
    detail.setLoadingColor("3"); // 橙色：(中转库+在途)<装车计划
}
```

### 发货计划颜色判断
```java
// 计算累积扣减后的可用库存
BigDecimal availableFgStock = fgStock.subtract(beforeDemandQty[0]); // 剩余仓库库存
// 成型后半品 = 包装后+包装+高压包装+合片+成型合片
BigDecimal semiFinishedStock = afterPacking.add(afterLamination).add(afterShape);
BigDecimal availableFgAndSemiFinished = fgStock.add(semiFinishedStock).subtract(beforeDemandQty[0]); // 剩余仓库+成型后半品

if (demandQty.compareTo(availableFgStock) <= 0) {
    detail.setDemandColor("1"); // 绿色：仓库>=发货计划
} else if (demandQty.compareTo(availableFgAndSemiFinished) <= 0) {
    detail.setDemandColor("2"); // 浅绿色：仓库+成型后半品>=发货计划
} else {
    detail.setDemandColor("3"); // 橙色：仓库+成型后半品<发货计划
}
```

## 📋 主要变更

### 移除的逻辑
1. **移除了"4"（红色）颜色值** - 简化为3种颜色
2. **移除了"一旦红色后续都红色"的逻辑** - 每日独立判断
3. **简化了库存层级判断** - 从4层简化为2-3层

### 保留的逻辑
1. **累积扣减机制** - 仍然按日期顺序累积扣减库存
2. **独立的装车和发货判断** - 两者使用不同的库存基础
3. **详细的库存计算** - 精确计算各类库存的可用量

## 🎯 业务价值

### 优势
1. **逻辑更清晰** - 3种颜色更容易理解和使用
2. **判断更精准** - 基于实际业务场景的库存层级
3. **维护更简单** - 减少了复杂的状态管理

### 适用场景
1. **装车计划** - 主要关注中转库和在途库存
2. **发货计划** - 主要关注成品库存和半成品库存
3. **库存预警** - 通过颜色快速识别库存状态

## 🧪 测试建议

### 测试用例
1. **绿色场景** - 库存充足的情况
2. **浅绿色场景** - 需要动用次级库存的情况
3. **橙色场景** - 库存不足的情况
4. **累积扣减** - 多日连续消耗的情况

### 验证要点
1. 颜色值正确性（"1", "2", "3"）
2. 库存计算准确性
3. 累积扣减逻辑正确性
4. 装车和发货独立判断

## 📝 注意事项

1. **颜色值变更** - 前端需要适配新的颜色值（移除"4"）
2. **库存定义** - 确保各类库存的定义与业务一致
3. **性能影响** - 简化后的逻辑应该有更好的性能
4. **向后兼容** - 如有必要，考虑渐进式迁移