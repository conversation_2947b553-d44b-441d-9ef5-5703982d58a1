package com.yhl.scp.dfp.clean.dto;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <code>CleanDemandProductInventoryReportDTO</code>
 * <p>
 * 风险车型管控报表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-22 10:16:21
 */
@ApiModel(value = "风险车型管控报表DTO")
@Data
public class CleanDemandProductInventoryReportDTO implements Serializable {

    private static final long serialVersionUID = -95581513681821744L;

    /**
     * 客户简称
     */
    @ApiModelProperty(value = "主键ID")
    private String customerAbbreviation;
    /**
     * 车型
     */
    @ApiModelProperty(value = "车型")
    private String vehicleModelCode;
    

}
