package com.yhl.scp.dfp.clean.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yhl.platform.common.annotation.FieldInterpretation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <code>CleanDemandProductInventoryCollect</code>
 * <p>
 * 风险车型管控-产品库存汇总VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-21 10:28:21
 */
@ApiModel(value = "风险车型管控-产品库存汇总VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CleanDemandProductInventoryCollectVO implements Serializable {

    private static final long serialVersionUID = -85130264218462187L;

    /**
     * 是否高亮
     */
    @ApiModelProperty(value = "是否高亮")
    @FieldInterpretation(value = "是否高亮")
    private String highlightFlag;

    /**
     * 客户
     */
    @ApiModelProperty(value = "客户")
    @FieldInterpretation(value = "客户")
    private String customerAbbreviation;

    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @FieldInterpretation(value = "车型编码")
    private String vehicleModelCode;

    /**
     * 未来30天日均需求
     */
    @ApiModelProperty(value = "未来30天日均需求")
    @FieldInterpretation(value = "未来30天日均需求")
    private BigDecimal averageDemandQuantity;

    /**
     * 产品库存数量
     */
    @ApiModelProperty(value = "产品库存数量")
    @FieldInterpretation(value = "产品库存数量")
    private BigDecimal productInventory;


    /**
     * 产品周转天数
     */
    @ApiModelProperty(value = "产品周转天数")
    @FieldInterpretation(value = "产品周转天数")
    private BigDecimal productInventoryDays;

    /**
     * 日均需求金额
     */
    @ApiModelProperty(value = "日均需求金额")
    @FieldInterpretation(value = "日均需求金额")
    private BigDecimal averageDemandAmount;

    /**
     * 成本金额
     */
    @ApiModelProperty(value = "成本金额")
    @FieldInterpretation(value = "成本金额")
    private BigDecimal costAmount;

    /**
     * 产品库存天数（金额） = 成本金额 / 日均需求金额
     */
    @ApiModelProperty(value = "成本金额")
    @FieldInterpretation(value = "成本金额")
    private BigDecimal productInventoryAmount;

    /**
     * B类材料本厂库存天数
     */
    @ApiModelProperty(value = "B类材料本厂库存天数")
    @FieldInterpretation(value = "B类材料本厂库存天数")
    private Integer materialBTypeFactoryInventoryDays = 0;

    /**
     * B类材料供应商库存天数
     */
    @ApiModelProperty(value = "B类材料供应商库存天数")
    @FieldInterpretation(value = "B类材料供应商库存天数")
    private Integer materialBTypeSupplierInventoryDays = 0;

    /**
     * B类材料库存天数
     */
    @ApiModelProperty(value = "B类材料库存天数")
    @FieldInterpretation(value = "B类材料库存天数")
    private Integer materialBTypeInventoryDays = 0;

    /**
     * B类材料金额
     */
    @ApiModelProperty(value = "B类材料金额")
    @FieldInterpretation(value = "B类材料金额")
    private BigDecimal materialBTypeInventoryCost = BigDecimal.ZERO;

    /**
     * B类材料日均需求成本金额
     */
    @ApiModelProperty(value = "B类材料日均需求成本金额")
    @FieldInterpretation(value = "B类材料日均需求成本金额")
    private BigDecimal materialBTypeDayDemandCostAmount = BigDecimal.ZERO;
    ;

    /**
     * B类材料成本总金额（万元）
     */
    @ApiModelProperty(value = "B类材料成本总金额（万元）")
    @FieldInterpretation(value = "B类材料成本总金额（万元）")
    private BigDecimal materialBTypeTotalCostAmountThousand = BigDecimal.ZERO;
    ;
    /**
     * B类材料本厂库存金额（万元）
     */
    @ApiModelProperty(value = "B类材料本厂库存金额（万元）")
    @FieldInterpretation(value = "B类材料本厂库存金额（万元）")
    private BigDecimal materialBTypeFactoryInventoryAmountThousand = BigDecimal.ZERO;
    ;
    /**
     * B类材料供应商库存金额（万元）
     */
    @ApiModelProperty(value = "B类材料供应商库存金额（万元）")
    @FieldInterpretation(value = "B类材料供应商库存金额（万元）")
    private BigDecimal materialBTypeSupplierInventoryAmountThousand = BigDecimal.ZERO;
    ;

    /**
     * A类材料本厂库存天数
     */
    @ApiModelProperty(value = "A类材料本厂库存天数")
    @FieldInterpretation(value = "A类材料本厂库存天数")
    private Integer materialATypeFactoryInventoryDays = 0;

    /**
     * A类材料供应商库存天数
     */
    @ApiModelProperty(value = "A类材料供应商库存天数")
    @FieldInterpretation(value = "A类材料供应商库存天数")
    private Integer materialATypeSupplierInventoryDays = 0;

    /**
     * A类材料库存天数
     */
    @ApiModelProperty(value = "A类材料库存天数")
    @FieldInterpretation(value = "A类材料库存天数")
    private Integer materialATypeInventoryDays = 0;

    /**
     * A类材料金额
     */
    @ApiModelProperty(value = "A类材料金额")
    @FieldInterpretation(value = "A类材料金额")
    private BigDecimal materialATypeInventoryCost = BigDecimal.ZERO;

    /**
     * A类材料日均需求成本金额
     */
    @ApiModelProperty(value = "A类材料日均需求成本金额")
    @FieldInterpretation(value = "A类材料日均需求成本金额")
    private BigDecimal materialATypeDayDemandCostAmount = BigDecimal.ZERO;

    /**
     * A类材料成本总金额（万元）
     */
    @ApiModelProperty(value = "A类材料成本总金额（万元）")
    @FieldInterpretation(value = "A类材料成本总金额（万元）")
    private BigDecimal materialATypeTotalCostAmountThousand = BigDecimal.ZERO;

    /**
     * A类材料本厂库存金额（万元）
     */
    @ApiModelProperty(value = "A类材料本厂库存金额（万元）")
    @FieldInterpretation(value = "A类材料本厂库存金额（万元）")
    private BigDecimal materialATypeFactoryInventoryAmountThousand = BigDecimal.ZERO;

    /**
     * A类材料供应商库存金额（万元）
     */
    @ApiModelProperty(value = "A类材料供应商库存金额（万元）")
    @FieldInterpretation(value = "A类材料供应商库存金额（万元）")
    private BigDecimal materialATypeSupplierInventoryAmountThousand = BigDecimal.ZERO;

    /**
     * 材料成本总金额（万元）
     */
    @ApiModelProperty(value = "材料成本总金额（万元）")
    @FieldInterpretation(value = "材料成本总金额（万元）")
    private BigDecimal materialTotalCostAmountThousand = BigDecimal.ZERO;

    /**
     * 材料库存天数
     */
    @ApiModelProperty(value = "材料库存天数")
    @FieldInterpretation(value = "材料库存天数")
    private Integer materialInventoryDays = 0;
}
