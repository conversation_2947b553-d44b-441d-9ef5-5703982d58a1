-- 已排产量计算调试SQL查询脚本
-- 用于分析产品01009LDR06001的数据情况

-- =====================================================
-- 1. 检查产品是否存在于产品库存点表
-- =====================================================
SELECT 
    '1. 产品库存点检查' as check_type,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT product_code) as product_codes
FROM mds_product_stock_point 
WHERE product_code = '01009LDR06001';

-- =====================================================
-- 2. 检查相似产品编码（前缀匹配）
-- =====================================================
SELECT 
    '2. 相似产品编码检查' as check_type,
    product_code,
    stock_point_code,
    id
FROM mds_product_stock_point 
WHERE product_code LIKE '01009LDR%'
ORDER BY product_code;

-- =====================================================
-- 3. 检查成型工序的操作任务
-- =====================================================
SELECT 
    '3. 成型工序任务检查' as check_type,
    COUNT(DISTINCT ot.id) as operation_task_count,
    COUNT(DISTINCT wo.id) as work_order_count,
    COUNT(DISTINCT psp.product_code) as product_count
FROM sds_ord_operation_task ot
JOIN sds_ord_operation op ON ot.operation_id = op.id
JOIN mds_rou_standard_step ss ON op.standard_step_id = ss.id
JOIN sds_ord_work_order wo ON op.order_id = wo.id
JOIN mds_product_stock_point psp ON wo.product_id = psp.id
WHERE ss.standard_step_name = '成型'
AND psp.product_code = '01009LDR06001';

-- =====================================================
-- 4. 详细的工单和任务信息
-- =====================================================
SELECT 
    '4. 详细工单任务信息' as check_type,
    ot.id as operation_task_id,
    wo.id as work_order_id,
    wo.order_no as work_order_no,
    psp.product_code as work_order_product_code,
    wo.quantity as work_order_quantity,
    wo.top_order_id,
    top_wo.order_no as top_work_order_no,
    top_psp.product_code as top_work_order_product_code,
    top_wo.quantity as top_work_order_quantity,
    ss.standard_step_name
FROM sds_ord_operation_task ot
JOIN sds_ord_operation op ON ot.operation_id = op.id
JOIN mds_rou_standard_step ss ON op.standard_step_id = ss.id
JOIN sds_ord_work_order wo ON op.order_id = wo.id
JOIN mds_product_stock_point psp ON wo.product_id = psp.id
LEFT JOIN sds_ord_work_order top_wo ON wo.top_order_id = top_wo.id
LEFT JOIN mds_product_stock_point top_psp ON top_wo.product_id = top_psp.id
WHERE ss.standard_step_name = '成型'
AND psp.product_code = '01009LDR06001'
ORDER BY ot.id;

-- =====================================================
-- 5. 检查顶层工单匹配情况
-- =====================================================
SELECT 
    '5. 顶层工单匹配检查' as check_type,
    COUNT(*) as matching_count,
    SUM(CASE 
        WHEN wo.top_order_id IS NOT NULL AND top_wo.quantity IS NOT NULL 
        THEN top_wo.quantity 
        ELSE wo.quantity 
    END) as total_scheduled_quantity
FROM sds_ord_operation_task ot
JOIN sds_ord_operation op ON ot.operation_id = op.id
JOIN mds_rou_standard_step ss ON op.standard_step_id = ss.id
JOIN sds_ord_work_order wo ON op.order_id = wo.id
JOIN mds_product_stock_point psp ON wo.product_id = psp.id
LEFT JOIN sds_ord_work_order top_wo ON wo.top_order_id = top_wo.id
LEFT JOIN mds_product_stock_point top_psp ON top_wo.product_id = top_psp.id
WHERE ss.standard_step_name = '成型'
AND (
    psp.product_code = '01009LDR06001'  -- 直接匹配
    OR top_psp.product_code = '01009LDR06001'  -- 顶层匹配
    OR psp.product_code LIKE '01009LDR%'  -- 产品族匹配
);

-- =====================================================
-- 6. 检查所有01009开头的产品的成型工序情况
-- =====================================================
SELECT 
    '6. 01009产品族成型工序统计' as check_type,
    psp.product_code,
    COUNT(DISTINCT ot.id) as operation_task_count,
    COUNT(DISTINCT wo.id) as work_order_count,
    SUM(wo.quantity) as total_work_order_quantity,
    SUM(CASE 
        WHEN wo.top_order_id IS NOT NULL AND top_wo.quantity IS NOT NULL 
        THEN top_wo.quantity 
        ELSE wo.quantity 
    END) as total_scheduled_quantity
FROM sds_ord_operation_task ot
JOIN sds_ord_operation op ON ot.operation_id = op.id
JOIN mds_rou_standard_step ss ON op.standard_step_id = ss.id
JOIN sds_ord_work_order wo ON op.order_id = wo.id
JOIN mds_product_stock_point psp ON wo.product_id = psp.id
LEFT JOIN sds_ord_work_order top_wo ON wo.top_order_id = top_wo.id
WHERE ss.standard_step_name = '成型'
AND psp.product_code LIKE '01009%'
GROUP BY psp.product_code
ORDER BY psp.product_code;

-- =====================================================
-- 7. 检查工单层级关系
-- =====================================================
SELECT 
    '7. 工单层级关系检查' as check_type,
    wo.id as work_order_id,
    wo.order_no,
    psp.product_code as current_product_code,
    wo.top_order_id,
    top_wo.order_no as top_order_no,
    top_psp.product_code as top_product_code,
    CASE 
        WHEN wo.top_order_id IS NULL THEN '无顶层工单'
        WHEN top_wo.id IS NULL THEN '顶层工单不存在'
        WHEN psp.product_code = top_psp.product_code THEN '产品编码相同'
        ELSE '产品编码不同'
    END as hierarchy_status
FROM sds_ord_work_order wo
JOIN mds_product_stock_point psp ON wo.product_id = psp.id
LEFT JOIN sds_ord_work_order top_wo ON wo.top_order_id = top_wo.id
LEFT JOIN mds_product_stock_point top_psp ON top_wo.product_id = top_psp.id
WHERE psp.product_code LIKE '01009LDR%'
   OR top_psp.product_code LIKE '01009LDR%'
ORDER BY wo.id;

-- =====================================================
-- 8. 验证MPS服务逻辑的SQL等价查询
-- =====================================================
SELECT 
    '8. MPS服务逻辑验证' as check_type,
    ot.id as operation_task_id,
    wo.id as work_order_id,
    -- 模拟MPS服务的parentProductCode逻辑
    CASE 
        WHEN wo.top_order_id IS NOT NULL AND top_psp.product_code IS NOT NULL 
        THEN top_psp.product_code 
        ELSE psp.product_code 
    END as parent_product_code,
    -- 模拟topWorkOrderQuantity逻辑
    CASE 
        WHEN wo.top_order_id IS NOT NULL AND top_wo.quantity IS NOT NULL 
        THEN top_wo.quantity 
        ELSE wo.quantity 
    END as top_work_order_quantity
FROM sds_ord_operation_task ot
JOIN sds_ord_operation op ON ot.operation_id = op.id
JOIN mds_rou_standard_step ss ON op.standard_step_id = ss.id
JOIN sds_ord_work_order wo ON op.order_id = wo.id
JOIN mds_product_stock_point psp ON wo.product_id = psp.id
LEFT JOIN sds_ord_work_order top_wo ON wo.top_order_id = top_wo.id
LEFT JOIN mds_product_stock_point top_psp ON top_wo.product_id = top_psp.id
WHERE ss.standard_step_name = '成型'
AND (
    -- 直接匹配
    psp.product_code = '01009LDR06001'
    OR 
    -- 顶层匹配（MPS服务的主要逻辑）
    (wo.top_order_id IS NOT NULL AND top_psp.product_code = '01009LDR06001')
)
ORDER BY ot.id;

-- =====================================================
-- 9. 最终汇总查询（应该与代码逻辑一致）
-- =====================================================
SELECT 
    '9. 最终汇总结果' as check_type,
    '01009LDR06001' as target_product_code,
    COUNT(DISTINCT ot.id) as matching_operation_tasks,
    SUM(CASE 
        WHEN wo.top_order_id IS NOT NULL AND top_wo.quantity IS NOT NULL 
        THEN top_wo.quantity 
        ELSE wo.quantity 
    END) as calculated_schedule_qty
FROM sds_ord_operation_task ot
JOIN sds_ord_operation op ON ot.operation_id = op.id
JOIN mds_rou_standard_step ss ON op.standard_step_id = ss.id
JOIN sds_ord_work_order wo ON op.order_id = wo.id
JOIN mds_product_stock_point psp ON wo.product_id = psp.id
LEFT JOIN sds_ord_work_order top_wo ON wo.top_order_id = top_wo.id
LEFT JOIN mds_product_stock_point top_psp ON top_wo.product_id = top_psp.id
WHERE ss.standard_step_name = '成型'
AND (
    -- 按照修复后的匹配逻辑
    (wo.top_order_id IS NOT NULL AND top_psp.product_code = '01009LDR06001') -- 顶层匹配（优先）
    OR psp.product_code = '01009LDR06001' -- 直接匹配（兜底）
    OR (psp.product_code LIKE '01009LDR%' AND LENGTH(psp.product_code) >= 8 AND LEFT(psp.product_code, 8) = LEFT('01009LDR06001', 8)) -- 产品族匹配
);
