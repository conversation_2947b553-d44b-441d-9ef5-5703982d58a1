package com.yhl.scp.ips.config;

import io.lettuce.core.dynamic.support.ReflectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.*;

import javax.annotation.Nonnull;
import java.lang.reflect.Field;
import java.util.List;

/**
 * <code>WebMvcConfiguration</code>
 * <p>
 * WebMvc配置
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021-11-19 16:17:00
 */
@Configuration
@Slf4j
@Order(-1)
public class WebMvcConfiguration implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/");
        registry.addResourceHandler("/idm/**")
                .addResourceLocations("classpath:/static/idm/");
        registry.addResourceHandler("/modeler/**")
                .addResourceLocations("classpath:/static/modeler/");
        registry.addResourceHandler("/workflow/**")
                .addResourceLocations("classpath:/static/workflow/");
        registry.addResourceHandler("/admin/**")
                .addResourceLocations("classpath:/static/admin/");
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
        registry.addResourceHandler("/doc.html")
                .addResourceLocations("classpath:/META-INF/resources/doc.html");
    }

    @Override
    @SuppressWarnings("unchecked")
    public void addInterceptors(@Nonnull InterceptorRegistry registry) {
        try {
            Field registrationsField = FieldUtils.getField(InterceptorRegistry.class, "registrations", true);
            List<InterceptorRegistration> registrations = (List<InterceptorRegistration>) ReflectionUtils
                    .getField(registrationsField, registry);
            if (registrations != null) {
                for (InterceptorRegistration interceptorRegistration : registrations) {
                    interceptorRegistration
                            .excludePathPatterns("/swagger**/**")
                            .excludePathPatterns("/webjars/**")
                            .excludePathPatterns("/v3/**")
                            .excludePathPatterns("/doc.html");
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", buildCorsConfig());
        return new CorsFilter(source);
    }

    /**
     * 构建跨域配置
     *
     * @return org.springframework.web.cors.CorsConfiguration
     */
    private CorsConfiguration buildCorsConfig() {
        CorsConfiguration config = new CorsConfiguration();
        config.addAllowedOriginPattern("*");
        /*config.addAllowedOriginPattern("http://localhost:8887");
        config.addAllowedOriginPattern("https://bpim-test.fuyaogroup.com");
        config.addAllowedOriginPattern("https://bpim-api-test.fuyaogroup.com");
        config.addAllowedOriginPattern("https://bpim-uat.fuyaogroup.com");
        config.addAllowedOriginPattern("https://bpim-api-uat.fuyaogroup.com");
        config.addAllowedOriginPattern("http://bpim.fuyaogroup.com");
        config.addAllowedOriginPattern("http://bpim-api.fuyaogroup.com");*/

        config.addAllowedMethod("GET");
        config.addAllowedMethod("POST");
        config.addAllowedMethod("PUT");
        config.addAllowedMethod("DELETE");
        config.addAllowedMethod("OPTIONS");

        // 允许的请求头
        config.addAllowedHeader("*");
        // 允许凭据（cookies、token 等）
        config.setAllowCredentials(true);
        // 预检请求缓存时间（减少重复预检，单位：秒）
        config.setMaxAge(3600L);
        return config;
    }

}