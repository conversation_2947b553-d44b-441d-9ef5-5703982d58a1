package com.yhl.scp.ips.rbac.controller;

import com.yhl.platform.common.PlatformUser;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.component.i18n.service.UserDefaultLanguageService;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.common.enums.UserTypeEnum;
import com.yhl.scp.ips.rbac.entity.Tenant;
import com.yhl.scp.ips.rbac.entity.TenantUser;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.rbac.service.TenantService;
import com.yhl.scp.ips.rbac.service.TenantUserService;
import com.yhl.scp.ips.rbac.service.UserService;
import com.yhl.scp.ips.system.service.ScenarioService;
import com.yhl.scp.ips.utils.AuthCodeUtils;
import io.seata.common.util.CollectionUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;

/**
 * <code>LoginController</code>
 * <p>
 * LoginController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-17 20:21:06
 */
@Slf4j
@Api(tags = "登陆认证")
@RestController
@RequestMapping("/login")
public class LoginController extends BaseController {

    public static final String CUSTOM_HEADER_TENANT = "tenant";
    public static final String CUSTOM_HEADER_TENANT_NAME = "tenantName";
    public static final String CUSTOM_HEADER_MODULE = "module";
    public static final String CUSTOM_HEADER_SCENARIO = "scenario";
    @Resource
    private UserService userService;
    @Resource
    private TenantUserService tenantUserService;
    @Resource
    private TenantService tenantService;
    @Resource
    private ScenarioService scenarioService;
    @Resource
    private AuthCodeUtils authCodeUtils;
    @Resource
    private UserDefaultLanguageService defaultLanguageService;

    @ApiOperation(value = "登录")
    @PostMapping(value = "loginSystem")
    public BaseResponse<User> login(String un, String pw, String authCode) {
        if (StringUtils.isEmpty(un) || StringUtils.isEmpty(pw)) {
            return BaseResponse.error("用户名和密码不能为空!");
        }
        User systemUser = userService.getUserByUserName(un);
        if (systemUser == null) {
            return BaseResponse.error("用户不存在!");
        }
        if (!YesOrNoEnum.YES.name().equals(systemUser.getEnabled())) {
            return BaseResponse.error("用户处于失效状态，请联系管理员!");
        }
        if (!pw.equals(systemUser.getPassword())) {
            return BaseResponse.error("密码不正确!");
        }
        BaseResponse verificationCode = authCodeUtils.verificationCode(session, authCode);
        if (Boolean.FALSE.equals(verificationCode.getSuccess())) {
            return BaseResponse.error(verificationCode.getMsg());
        }
        // 清空session用户
        session.removeAttribute(Constants.SESSION_USER);
        // 初始化响应头
        HttpSession newSession = request.getSession(true);
        String newSessionId = newSession.getId();
        initResponse(newSessionId);
        // 特殊用户类型返回
        if (UserTypeEnum.SYSTEM_ADMIN.getCode().equals(systemUser.getUserType())) {
            session.setAttribute(Constants.SESSION_USER, convert2PlatformUser(systemUser));
            return BaseResponse.success(systemUser);
        }
        String userId = systemUser.getId();
        // 设置响应头
        List<TenantUser> tenantUsers = tenantUserService.selectByUserId(userId);
        String tenantId = getTenantId(tenantUsers);
        String moduleCode = getDefaultModule(tenantUsers);
        setResponseHeaders(response, tenantId, userId, moduleCode);
        // 设置语言
        String language = defaultLanguageService.getDefaultLanguageByUserId(userId);
        if (StringUtils.isEmpty(language)) {
            language = request.getLocale().toString();
        }
        LocaleContextHolder.setLocale(new Locale(language.split("_")[0], language.split("_")[1]));
        session.setAttribute(Constants.LANGUAGE, language);
        // 组装用户信息
        Tenant tenant = tenantService.getById(tenantId);
        assembleSystemUser(systemUser, tenant, tenantUsers, language);

        session.setAttribute(CUSTOM_HEADER_TENANT_NAME, tenant.getTenantName());
        session.setAttribute(Constants.SESSION_USER, convert2PlatformUser(systemUser));
        return BaseResponse.success(systemUser);
    }

    public PlatformUser convert2PlatformUser(User user) {
        PlatformUser platformUser = new PlatformUser();
        platformUser.setId(user.getId());
        platformUser.setStaffCode(user.getStaffCode());
        platformUser.setUserName(user.getUserName());
        platformUser.setCnName(user.getCnName());
        platformUser.setUserType(user.getUserType());
        platformUser.setOrgCode(user.getOrgCode());
        platformUser.setTenantCode(user.getTenantCode());
        platformUser.setCompanyCode(user.getCompanyCode());
        platformUser.setDefaultLanguage(user.getDefaultLanguage());
        return platformUser;
    }

    /**
     * 获取租户ID
     *
     * @param tenantUsers 租户用户
     * @return java.lang.String
     */
    private String getTenantId(List<TenantUser> tenantUsers) {
        Optional<TenantUser> masterUser = tenantUsers.stream()
                .filter(item -> YesOrNoEnum.YES.getCode().equals(item.getMaster())).findAny();
        if (masterUser.isPresent()) {
            return masterUser.get().getTenantId();
        }
        if (tenantUsers.isEmpty()) {
            return "";
        }
        return tenantUsers.get(0).getTenantId();
    }

    /**
     * 设置响应头
     *
     * @param response    响应体
     * @param tenantId    租户ID
     * @param userId      租户ID
     * @param moduleCode  模块代码
     */
    private void setResponseHeaders(HttpServletResponse response, String tenantId, String userId, String moduleCode) {
        response.setHeader(CUSTOM_HEADER_TENANT, tenantId);
        response.setHeader(CUSTOM_HEADER_MODULE, moduleCode);
        String scenario = scenarioService.selectDefaultScenario(tenantId, userId, moduleCode);
        response.setHeader(CUSTOM_HEADER_SCENARIO, scenario);
    }

    /**
     * 获取默认模块
     *
     * @param tenantUsers 租户用户列表
     * @return java.lang.String
     */
    private String getDefaultModule(List<TenantUser> tenantUsers) {
        Optional<TenantUser> masterUser = tenantUsers.stream()
                .filter(item -> YesOrNoEnum.YES.getCode().equals(item.getMaster()))
                .findAny();
        if (masterUser.isPresent()) {
            return StringUtils.isEmpty(masterUser.get().getDefaultModule())
                    ? SystemModuleEnum.MDS.getCode() : masterUser.get().getDefaultModule();
        }
        return SystemModuleEnum.MDS.getCode();
    }

    /**
     * 更新用户对象
     *
     * @param systemUser  用户
     * @param tenant      租户
     * @param tenantUsers 租户用户列表
     * @param language    语言
     */
    private void assembleSystemUser(User systemUser, Tenant tenant, List<TenantUser> tenantUsers, String language) {
        /*Optional<TenantUser> masterUser = tenantUsers.stream()
                .filter(item -> YesOrNoEnum.YES.getCode().equals(item.getMaster()))
                .findAny();*/
        /*if (masterUser.isPresent()) {
            systemUser.setUserType(masterUser.get().getUserType());
        } else {
            if (CollectionUtils.isNotEmpty(tenantUsers)) {
                systemUser.setUserType(tenantUsers.get(0).getUserType());
            }
        }*/
        systemUser.setTenant(tenant);
        systemUser.setTenantCode(Objects.isNull(tenant) ? "" : tenant.getTenantCode());
        systemUser.setCompanyCode(Objects.isNull(tenant) ? "" : tenant.getCompanyCode());
        systemUser.setDefaultLanguage(language);
    }

    private void initResponse(String newSessionId) {
        Cookie cookie = new Cookie("JSESSIONID", newSessionId);
        cookie.setMaxAge(60 * 30 * 10);
        cookie.setPath("/");
        cookie.setSecure(true);
        response.addCookie(cookie);

        response.setHeader(CUSTOM_HEADER_TENANT, null);
        response.setHeader(CUSTOM_HEADER_MODULE, null);
        response.setHeader(CUSTOM_HEADER_SCENARIO, null);
    }

    @ApiOperation(value = "注销")
    @PostMapping(value = "logout")
    public BaseResponse<Map<String, Object>> loginOut() {
        session.removeAttribute(Constants.SESSION_USER);
        Map<String, Object> result = new HashMap<>(4);
        String referer = request.getHeader("referer");
        int position = StringUtils.ordinalIndexOf(referer, "/", 3);
        if (position > 0) {
            referer = referer.substring(0, position);
        }
        result.put("directUrl", referer);
        session.invalidate();
        return BaseResponse.success(result);
    }

    @ApiOperation("session")
    @GetMapping(value = "session")
    public BaseResponse<String> getSession() {
        return BaseResponse.success(session.getId());
    }

    @ApiOperation("生成图片验证码")
    @GetMapping("getAuthCodeNew")
    public void getAuthCodeNew() {
        try {
            log.info("getAuthCodeNew sessionId is {}", session.getId());
            // 生成算术验证码，还可选择其他验证方式，如：（中文，中英文混合）
            authCodeUtils.createArithmeticCode(response, session);
        } catch (IOException e) {
            log.error("图片验证码生成异常：", e);
        }
    }

    @ApiOperation("校验图片验证码结果")
    @GetMapping("verifyAuthCodeNew")
    @SuppressWarnings("rawtypes")
    public BaseResponse verifyAuthCodeNew(HttpSession session, String authCode) {
        log.info("verifyAuthCodeNew sessionId is {}", session.getId());
        return authCodeUtils.verificationCode(session, authCode);
    }

}