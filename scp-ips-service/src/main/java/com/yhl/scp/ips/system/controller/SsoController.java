package com.yhl.scp.ips.system.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yhl.platform.common.PlatformUser;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.JacksonUtils;
import com.yhl.platform.component.i18n.service.UserDefaultLanguageService;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.common.enums.UserTypeEnum;
import com.yhl.scp.ips.config.IamConfig;
import com.yhl.scp.biz.common.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.rbac.entity.Tenant;
import com.yhl.scp.ips.rbac.entity.TenantUser;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.rbac.service.TenantService;
import com.yhl.scp.ips.rbac.service.TenantUserService;
import com.yhl.scp.ips.rbac.service.UserService;
import com.yhl.scp.ips.system.entity.IamUser;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.system.service.ScenarioService;
import io.seata.common.util.CollectionUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>SsoController</code>
 * <p>
 * SsoController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-15 18:10:19
 */
@Api(tags = "单点登录")
@Slf4j
@RestController
@RequestMapping("/sso")
public class SsoController extends BaseController {

    /**
     * 请求头常量
     **/
    public static final String CUSTOM_HEADER_TENANT = "tenant";
    public static final String CUSTOM_HEADER_TENANT_NAME = "tenantName";
    public static final String CUSTOM_HEADER_MODULE = "module";
    public static final String CUSTOM_HEADER_SCENARIO = "scenario";

    @Resource
    private UserService userService;

    @Resource
    private TenantUserService tenantUserService;

    @Resource
    private TenantService tenantService;

    @Resource
    private ScenarioService scenarioService;

    @Resource
    private UserDefaultLanguageService defaultLanguageService;

    @Resource
    private IamConfig iamConfig;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @ApiOperation(value = "登录")
    @PostMapping(value = "codeLogin")
    public BaseResponse<User> codeLogin(@RequestParam(value = "code") String code,
                                        @RequestParam(value = "redirect_uri") String redirectUri) {
        String ssoDomain = iamConfig.getSsoDomain();
        String code2TokenUri = String.format(iamConfig.getCode2TokenUri(), code, redirectUri);
        String code2TokenUrl = ssoDomain + code2TokenUri;
        log.info("code2TokenUrl:{}", code2TokenUrl);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(httpHeaders, null);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(code2TokenUrl, httpEntity, String.class);
        if (HttpStatus.OK.value() != responseEntity.getStatusCodeValue()) {
            return BaseResponse.error("IAM Code换Token接口调用失败");
        }

        String body = responseEntity.getBody();
        Map<String, Object> dataMap = JacksonUtils.toObj(body, new TypeReference<Map<String, Object>>() {
        });
        String accessToken = (String) dataMap.get("access_token");

        String token2UserUri = String.format(iamConfig.getToken2UserUri(), accessToken);
        String token2UserUrl = ssoDomain + token2UserUri;
        log.info("code2TokenUrl:{}", code2TokenUrl);
        ResponseEntity<IamUser> responseEntity1 = restTemplate.getForEntity(token2UserUrl, IamUser.class);
        if (HttpStatus.OK.value() != responseEntity1.getStatusCodeValue()) {
            return BaseResponse.error("IAM Token换User接口调用失败");
        }
        IamUser iamUser = responseEntity1.getBody();
        log.info("IamUser = {}", JacksonUtils.toJson(iamUser));
        if (iamUser == null) {
            return BaseResponse.error("IAM用户不存在");
        }
        IamUser.IamAttributes attributes = iamUser.getAttributes();
        if (attributes == null) {
            return BaseResponse.error("IAM用户不存在");
        }
        String accountNo = attributes.getAccountNo();
        if (StringUtils.isBlank(accountNo)) {
            return BaseResponse.error("IAM用户账号为空");
        }
        User systemUser = userService.getUserByUserName(accountNo);
        if (systemUser == null) {
            return BaseResponse.error("系统用户不存在");
        }
        systemUser.setPassword(null);
        if (!YesOrNoEnum.YES.name().equals(systemUser.getEnabled())) {
            return BaseResponse.error("系统用户处于失效状态，请联系管理员");
        }
        // 清空session用户
        session.removeAttribute(Constants.SESSION_USER);
        // 初始化响应头
        HttpSession newSession = request.getSession(true);
        String newSessionId = newSession.getId();
        initResponse(newSessionId);
        // 缓存IAM Token
        redisTemplate.opsForValue().set(String.format(RedisKeyManageEnum.ACCESS_TOKEN.getKey(), newSessionId), accessToken);
        // 特殊用户类型返回
        if (UserTypeEnum.SYSTEM_ADMIN.getCode().equals(systemUser.getUserType())) {
            session.setAttribute(Constants.SESSION_USER, convert2PlatformUser(systemUser));
            return BaseResponse.success(systemUser);
        }
        String userId = systemUser.getId();
        // 设置响应头
        List<TenantUser> tenantUsers = tenantUserService.selectByUserId(userId);
        String tenantId = getTenantId(tenantUsers);
        String moduleCode = getDefaultModule(tenantUsers);
        setResponseHeaders(response, tenantId, userId, moduleCode);
        // 设置语言
        String language = defaultLanguageService.getDefaultLanguageByUserId(userId);
        if (StringUtils.isEmpty(language)) {
            language = request.getLocale().toString();
        }
        LocaleContextHolder.setLocale(new Locale(language.split("_")[0], language.split("_")[1]));
        session.setAttribute(Constants.LANGUAGE, language);
        // 组装用户信息
        Tenant tenant = tenantService.getById(tenantId);
        assembleSystemUser(systemUser, tenant, tenantUsers, language);

        session.setAttribute(CUSTOM_HEADER_TENANT_NAME, tenant.getTenantName());
        session.setAttribute(Constants.SESSION_USER, convert2PlatformUser(systemUser));
        return BaseResponse.success(systemUser);
    }

    private void initResponse(String newSessionId) {
        Cookie cookie = new Cookie("JSESSIONID", newSessionId);
        cookie.setMaxAge(60 * 30 * 10);
        cookie.setPath("/");
        // cookie.setSecure(true);
        response.addCookie(cookie);

        response.setHeader(CUSTOM_HEADER_TENANT, null);
        response.setHeader(CUSTOM_HEADER_MODULE, null);
        response.setHeader(CUSTOM_HEADER_SCENARIO, null);
    }

    public PlatformUser convert2PlatformUser(User user) {
        PlatformUser platformUser = new PlatformUser();
        platformUser.setId(user.getId());
        platformUser.setStaffCode(user.getStaffCode());
        platformUser.setUserName(user.getUserName());
        platformUser.setCnName(user.getCnName());
        platformUser.setUserType(user.getUserType());
        platformUser.setOrgCode(user.getOrgCode());
        platformUser.setTenantCode(user.getTenantCode());
        platformUser.setCompanyCode(user.getCompanyCode());
        platformUser.setDefaultLanguage(user.getDefaultLanguage());
        return platformUser;
    }

    /**
     * 获取租户ID
     *
     * @param tenantUsers 租户用户
     * @return java.lang.String
     */
    private String getTenantId(List<TenantUser> tenantUsers) {
        if (tenantUsers.isEmpty()) {
            // 设置默认租户，默认租户为场景关联的租户
            List<Scenario> scenarios = scenarioService.selectAll();
            return scenarios.get(0).getTenantId();
        }
        Optional<TenantUser> masterUser = tenantUsers.stream()
                .filter(item -> YesOrNoEnum.YES.getCode().equals(item.getMaster())).findAny();
        if (masterUser.isPresent()) {
            return masterUser.get().getTenantId();
        }

        // 需要获取有效租户
        List<Scenario> scenarios = scenarioService.selectAll();
        List<String> tenantIdList = scenarios.stream()
                .map(Scenario::getTenantId)
                .collect(Collectors.toList());
        List<TenantUser> filterList = tenantUsers.stream()
                .filter(item -> tenantIdList.contains(item.getTenantId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)){
            return scenarios.get(0).getTenantId();
        }
        return filterList.get(0).getTenantId();
    }

    /**
     * 设置响应头
     *
     * @param response   响应体
     * @param tenantId   租户ID
     * @param userId     用户ID
     * @param moduleCode 模块代码
     */
    private void setResponseHeaders(HttpServletResponse response, String tenantId, String userId, String moduleCode) {
        response.setHeader(CUSTOM_HEADER_TENANT, tenantId);
        response.setHeader(CUSTOM_HEADER_MODULE, moduleCode);
        String scenario = scenarioService.selectDefaultScenario(tenantId, userId, moduleCode);
        response.setHeader(CUSTOM_HEADER_SCENARIO, scenario);
    }

    /**
     * 获取默认模块
     *
     * @param tenantUsers 租户用户列表
     * @return java.lang.String
     */
    private String getDefaultModule(List<TenantUser> tenantUsers) {
        Optional<TenantUser> masterUser = tenantUsers.stream()
                .filter(item -> YesOrNoEnum.YES.getCode().equals(item.getMaster()))
                .findAny();
        if (masterUser.isPresent()) {
            return StringUtils.isEmpty(masterUser.get().getDefaultModule())
                    ? SystemModuleEnum.MDS.getCode() : masterUser.get().getDefaultModule();
        }
        return SystemModuleEnum.MDS.getCode();
    }

    /**
     * 更新用户对象
     *
     * @param systemUser  用户
     * @param tenant      租户
     * @param tenantUsers 租户用户列表
     * @param language    语言
     */
    private void assembleSystemUser(User systemUser, Tenant tenant, List<TenantUser> tenantUsers, String language) {
        /*Optional<TenantUser> masterUser = tenantUsers.stream()
                .filter(item -> YesOrNoEnum.YES.getCode().equals(item.getMaster()))
                .findAny();
        if (masterUser.isPresent()) {
            systemUser.setUserType(masterUser.get().getUserType());
        } else {
            if (CollectionUtils.isNotEmpty(tenantUsers)) {
                systemUser.setUserType(tenantUsers.get(0).getUserType());
            }
        }*/
        systemUser.setTenant(tenant);
        systemUser.setTenantCode(Objects.isNull(tenant) ? "" : tenant.getTenantCode());
        systemUser.setCompanyCode(Objects.isNull(tenant) ? "" : tenant.getCompanyCode());
        systemUser.setDefaultLanguage(language);
    }

    @ApiOperation(value = "注销")
    @PostMapping(value = "logout")
    public BaseResponse<Map<String, Object>> logout() {
        session.removeAttribute(Constants.SESSION_USER);
        Map<String, Object> result = new HashMap<>(4);
        String referer = request.getHeader("referer");
        int position = StringUtils.ordinalIndexOf(referer, "/", 3);
        if (position > 0) {
            referer = referer.substring(0, position);
        }
        result.put("directUrl", referer);
        session.invalidate();
        // 根据token
        String jSessionId = null;
        for (Cookie cookie : request.getCookies()) {
            if ("JSESSIONID".equals(cookie.getName())) {
                jSessionId = cookie.getValue();
                break;
            }
        }
        if (StringUtils.isBlank(jSessionId)) {
            return BaseResponse.success(result);
        }
        String accessToken = redisTemplate.opsForValue().get(String.format(RedisKeyManageEnum.ACCESS_TOKEN.getKey(), jSessionId));
        if (StringUtils.isBlank(accessToken)) {
            return BaseResponse.success(result);
        }
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("accesstoken", accessToken);
        HttpEntity<Object> httpEntity = new HttpEntity<>(httpHeaders, null);
        String ssoDomain = iamConfig.getSsoDomain();
        String logOutUri = iamConfig.getLogOutUri();
        String logOutUrl = ssoDomain + logOutUri;
        ResponseEntity<String> response = restTemplate.postForEntity(logOutUrl, httpEntity, String.class);
        int statusCodeValue = response.getStatusCodeValue();
        result.put("directUrl", referer);
        if (HttpStatus.OK.value() != statusCodeValue) {
            String body = response.getBody();
            Map<String, Object> responseMap = JacksonUtils.toObj(body, new TypeReference<Map<String, Object>>() {
            });
            result.put("logOutCode", responseMap.get("errorCode"));
        } else {
            result.put("logOutCode", "-100");
        }
        return BaseResponse.success(result);
    }

}