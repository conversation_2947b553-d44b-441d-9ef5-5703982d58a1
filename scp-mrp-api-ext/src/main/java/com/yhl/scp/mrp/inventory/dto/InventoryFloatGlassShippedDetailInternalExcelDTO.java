package com.yhl.scp.mrp.inventory.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>InventoryFloatGlassShippedDetailInternalExcelDTO</code>
 * <p>
 * 原片浮法已发运（内部）库存批次明细excel导入DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 20:31:57
 */
@ApiModel(value = "原片浮法已发运（内部）库存批次明细excel导入DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InventoryFloatGlassShippedDetailInternalExcelDTO implements Serializable {

    private static final long serialVersionUID = 5571285145092801215L;

    /**
     * 柜号
     */
    @ApiModelProperty(value = "柜号")
    @FieldInterpretation(value = "柜号")
    @ExcelProperty(value = "柜号*")
    private String containerNumber;

    /**
     * 港口
     */
    @ApiModelProperty(value = "港口")
    @FieldInterpretation(value = "港口")
    @ExcelProperty(value = "港口*")
    private String portName;

    /**
     * 承运商
     */
    @ApiModelProperty(value = "承运商")
    @FieldInterpretation(value = "承运商")
    @ExcelProperty(value = "承运商")
    private String carrier;

    /**
     * 提单号
     */
    @ApiModelProperty(value = "提单号")
    @FieldInterpretation(value = "提单号")
    @ExcelProperty(value = "提单号")
    private String billNo;

    /**
     * 正确柜号
     */
    @ApiModelProperty(value = "正确柜号")
    @FieldInterpretation(value = "正确柜号")
    @ExcelProperty(value = "正确柜号")
    private String correctContainerNumber;

    /**
     * 预计到港时间
     */
    @ApiModelProperty(value = "预计到港时间")
    @FieldInterpretation(value = "预计到港时间")
    @ExcelProperty(value = "预计到港时间")
    private Date estimatedArrivalTime;
    /**
     * 实际到港时间
     */
    @ApiModelProperty(value = "实际到港时间")
    @FieldInterpretation(value = "实际到港时间")
    @ExcelProperty(value = "实际到港时间")
    private Date actualArrivalTime;
}
