package com.yhl.scp.mrp.report.vehicle.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>VehicleInventoryClassDataReportDTO</code>
 * <p>
 * 车型库存报表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-29 11:40:07
 */
@ApiModel(value = "车型库存报表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class VehicleInventoryClassDataReportDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -4574049597301284773L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 客户
     */
    @ApiModelProperty(value = "客户")
    private String customer;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    private String productFactoryCode;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String productCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String productName;
    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;
    /**
     * 采购周期
     */
    @ApiModelProperty(value = "采购周期")
    private BigDecimal purchaseLot = BigDecimal.ZERO;
    /**
     * 单耗
     */
    @ApiModelProperty(value = "单耗")
    private BigDecimal inputFactor = BigDecimal.ZERO;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;
    /**
     * 产出率
     */
    @ApiModelProperty(value = "产出率")
    private BigDecimal outputRate = BigDecimal.ZERO;
    /**
     * 库龄
     */
    @ApiModelProperty(value = "库龄")
    private BigDecimal stockAge;
    /**
     * 材料预测占比
     */
    @ApiModelProperty(value = "材料预测占比")
    private String materialPredictedProportion;
    /**
     * 材料预测占比（车型）
     */
    @ApiModelProperty(value = "材料预测占比（车型）")
    private String materialPredictedProportionVehicle;
    /**
     * 是否专用（占比>=50%）
     */
    @ApiModelProperty(value = "是否专用（占比>=50%）")
    private String isDedicatedGe50pct;
    /**
     * 未来30天日均需求
     */
    @ApiModelProperty(value = "未来30天日均需求")
    private BigDecimal next30DaysDailyAverageDemand = BigDecimal.ZERO;
    /**
     * 本厂库存
     */
    @ApiModelProperty(value = "本厂库存")
    private BigDecimal factoryInventory = BigDecimal.ZERO;
    /**
     * 供应商库存
     */
    @ApiModelProperty(value = "供应商库存")
    private BigDecimal supplierInventory = BigDecimal.ZERO;
    /**
     * 现有库存可生产玻璃零件数量
     */
    @ApiModelProperty(value = "现有库存可生产玻璃零件数量")
    private BigDecimal currentStockPartProdQuantity = BigDecimal.ZERO;
    /**
     * 福耀库存可用天数
     */
    @ApiModelProperty(value = "福耀库存可用天数")
    private Integer factoryInventoryUseDay = 0;
    /**
     * 供应商库存可用天数
     */
    @ApiModelProperty(value = "供应商库存可用天数")
    private Integer supplierInventoryUseDay = 0;
    /**
     * 材料总天数
     */
    @ApiModelProperty(value = "材料总天数")
    private Integer materialTotalDay = 0;
    /**
     * 当前月预测数量
     */
    @ApiModelProperty(value = "当前月预测数量")
    private BigDecimal currentMonthForecastQuantity = BigDecimal.ZERO;
    /**
     * 下个月预测数量
     */
    @ApiModelProperty(value = "下个月预测数量")
    private BigDecimal nextMonthForecastQuantity = BigDecimal.ZERO;
    /**
     * 下下个月预测数量
     */
    @ApiModelProperty(value = "下下个月预测数量")
    private BigDecimal afterNextMonthForecastQuantity = BigDecimal.ZERO;
    /**
     * 成本总金额（万元）
     */
    @ApiModelProperty(value = "成本总金额（万元）")
    private BigDecimal totalCostAmountThousand = BigDecimal.ZERO;
    /**
     * 本厂库存金额（万元）
     */
    @ApiModelProperty(value = "本厂库存金额（万元）")
    private BigDecimal factoryInventoryAmountThousand = BigDecimal.ZERO;
    /**
     * 供应商库存金额（万元）
     */
    @ApiModelProperty(value = "供应商库存金额（万元）")
    private BigDecimal supplierInventoryAmountThousand = BigDecimal.ZERO;
    /**
     * 日均需求金额
     */
    @ApiModelProperty(value = "日均需求金额")
    private BigDecimal dailyAverageDemandAmount = BigDecimal.ZERO;
    /**
     * 管控标准
     */
    @ApiModelProperty(value = "管控标准")
    private BigDecimal controlStandard = BigDecimal.ZERO;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer versionValue;
    /**
     * 材料类型
     */
    @ApiModelProperty(value = "材料类型")
    private String productType;
    /**
     * 材料代码
     */
    @ApiModelProperty(value = "材料代码")
    private String materialCode;
    /**
     * 材料名称
     */
    @ApiModelProperty(value = "材料名称")
    private String materialName;
    /**
     * 材料类型
     */
    @ApiModelProperty(value = "材料类型")
    private String materialType;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    /**
     * 未来30天日均需求
     */
    @ApiModelProperty(value = "未来30天日均需求")
    private BigDecimal futureThirtyDayDemand;
    /**
     * 福耀库存
     */
    @ApiModelProperty(value = "福耀库存")
    private BigDecimal fuyaoInventoryQuantity;
    /**
     * 供应商库存
     */
    @ApiModelProperty(value = "供应商库存")
    private BigDecimal supplierInventoryQuantity;
    /**
     * 未生产订单
     */
    @ApiModelProperty(value = "未生产订单")
    private BigDecimal unprocessedOrder;
    /**
     * 现有库存可生产玻璃零件数量
     */
    @ApiModelProperty(value = "现有库存可生产玻璃零件数量")
    private BigDecimal producedGlassQuantity;
    /**
     * 福耀库存可用天数
     */
    @ApiModelProperty(value = "福耀库存可用天数")
    private BigDecimal fuyaoInventoryAvailableDay;
    /**
     * 供应商库存可用天数
     */
    @ApiModelProperty(value = "供应商库存可用天数")
    private BigDecimal supplierInventoryAvailableDay;
    /**
     * 成本总金额
     */
    @ApiModelProperty(value = "成本总金额")
    private BigDecimal costAmountTotal;
    /**
     * 福耀在库金额(万元)
     */
    @ApiModelProperty(value = "福耀在库金额(万元)")
    private BigDecimal fuyaoInStockAmount;
    /**
     * 供应商库存金额(万元)
     */
    @ApiModelProperty(value = "供应商库存金额(万元)")
    private BigDecimal supplierInStockAmount;
    /**
     * 日均需求金额
     */
    @ApiModelProperty(value = "日均需求金额")
    private BigDecimal dayDemandAmount;
    /**
     * 管控标准
     */
    @ApiModelProperty(value = "管控标准")
    private BigDecimal controlStandards;
}
