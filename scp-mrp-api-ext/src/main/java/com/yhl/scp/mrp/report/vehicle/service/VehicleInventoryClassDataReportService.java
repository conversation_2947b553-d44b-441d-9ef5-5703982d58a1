package com.yhl.scp.mrp.report.vehicle.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.clean.vo.CleanDemandProductInventoryReportVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassBFactoryReportDTO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassDataReportDTO;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBFactoryReportVO;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;

import java.util.List;
import java.util.Map;

/**
 * <code>VehicleInventoryClassBFactoryReportService</code>
 * <p>
 * 车型库存（B类本厂）报表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:48:09
 */
public interface VehicleInventoryClassDataReportService{

    void assembleVehicleInventoryFactoryReport(List<String> materialTypeList,
                                               List<VehicleInventoryClassDataReportDTO> dataList) throws Exception;
}
