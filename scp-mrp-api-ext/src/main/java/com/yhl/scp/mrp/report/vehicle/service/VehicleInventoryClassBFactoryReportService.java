package com.yhl.scp.mrp.report.vehicle.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.clean.vo.CleanDemandProductInventoryReportVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassBFactoryReportDTO;
import com.yhl.scp.mrp.report.vehicle.dto.VehicleInventoryClassDataReportDTO;
import com.yhl.scp.mrp.report.vehicle.vo.VehicleInventoryClassBFactoryReportVO;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;

import java.util.List;
import java.util.Map;

/**
 * <code>VehicleInventoryClassBFactoryReportService</code>
 * <p>
 * 车型库存（B类本厂）报表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-26 09:48:09
 */
public interface VehicleInventoryClassBFactoryReportService extends BaseService<VehicleInventoryClassBFactoryReportDTO, VehicleInventoryClassBFactoryReportVO> {

    /**
     * 查询所有
     *
     * @return list {@link VehicleInventoryClassBFactoryReportVO}
     */
    List<VehicleInventoryClassBFactoryReportVO> selectAll();

    void generateReport() throws Exception;

    void assembleVehicleInventoryFactoryReport(List<String> threeMonths,
                                               List<String> threeMonths02,
                                               List<VehicleInventoryClassDataReportDTO> addList,
                                               Map<String, CleanDemandProductInventoryReportVO> cleanDemandProductInventoryReportVOMap,
                                               List<String> productCodeList,
                                               Map<String, List<ProductBomVO>> productBomGroupOfProductCode,
                                               Map<String, List<MaterialSupplierPurchaseVO>> materialSupplierPurchaseVOMap,
                                               Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailVOMap,
                                               Map<String, List<MaterialGrossDemandVO>> grossDemandGroupOfProductCode,
                                               Map<String, List<MaterialGrossDemandVO>> grossDemandGroupOfVehicleModeCode,
                                               Map<String, List<MaterialArrivalTrackingVO>> materialArrivalTrackingVOMap,
                                               Map<String, List<NoGlassInventoryShiftDataVO>> shiftDataMap,
                                               Map<String, List<NoGlassInventoryShiftDetailVO>> shiftDetailMap,
                                               Map<String, List<ConsistenceDemandForecastDataDetailVO>> forecastDetailMap,
                                               Map<String, NewProductStockPointVO> productStockPointVOMap);
}
