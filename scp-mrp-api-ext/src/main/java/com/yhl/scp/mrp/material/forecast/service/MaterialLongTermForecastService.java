package com.yhl.scp.mrp.material.forecast.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mds.extension.supplier.vo.SupplierPurchaseRatioVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.material.forecast.dto.MaterialLongTermForecastDTO;
import com.yhl.scp.mrp.material.forecast.dto.MaterialLongTermForecastParam;
import com.yhl.scp.mrp.material.forecast.vo.MaterialLongTermForecastVO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryShiftSupplyVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;

import java.util.List;
import java.util.Map;

/**
 * <code>MaterialLongTermForecastService</code>
 * <p>
 * 材料长期预测应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-22 15:46:19
 */
public interface MaterialLongTermForecastService extends BaseService<MaterialLongTermForecastDTO, MaterialLongTermForecastVO> {

    List<MaterialLongTermForecastVO> selectVOByParams(Map<String, Object> params);

    /**
     * 查询所有
     *
     * @return list {@link MaterialLongTermForecastVO}
     */
    List<MaterialLongTermForecastVO> selectAll();

    void doSyncLongTermForecastData(List<NoGlassInventoryShiftDataVO> shiftDataList, List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftListOfForecastPo,
                                    Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMap, Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup,
                                    Map<String, SupplierVO> supplierVOMapOfId, Map<String, SupplierVO> supplierVOMapOfCode,
                                    String demandPattern, List<MaterialPlanInventoryShiftSupplyVO> inventoryShiftSupplyVOS, String versionCode);


    PageInfo<MaterialLongTermForecastVO> selectByPage2(MaterialLongTermForecastParam materialLongTermForecastParam);

    BaseResponse<Void> issue(List<String> ids, String type, Boolean checkPermission);

}
