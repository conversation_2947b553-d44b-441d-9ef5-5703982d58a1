# 已排产量计算修复设计文档

## 概述

本设计文档描述了如何修复需求发货生产报表中已排产量(scheduleQty)的计算逻辑。当前实现基于包装工序的计划数量汇总，需要改为参照MPS服务的逻辑，通过工单的顶层订单数量进行计算。

## 架构

### 当前架构问题
- 当前逻辑在`DemandDeliveryProductionServiceImpl.processProductData()`方法中
- 使用包装工序的`MasterPlanTaskVO.getOperationVO().getQuantity()`进行汇总
- 没有考虑工单层级关系和顶层订单数量
- **关键问题**：使用了错误的工序维度（包装工序），应该使用成型工序

### 目标架构
- 保持现有的异步查询架构
- 在现有的`getMasterPlanData()`方法基础上增加工单数据查询
- 修改`processProductData()`方法中的已排产量计算逻辑
- 参照MPS服务的`masterPlanWorkOrder()`方法逻辑
- **关键修正**：使用成型工序（FORMING_OPERATION）而不是包装工序进行汇总

## 组件和接口

### 1. 数据查询组件

#### 1.1 工单数据查询
```java
// 在现有的getMasterPlanData方法中增加工单查询
private Map<String, WorkOrderPO> getWorkOrderMap(List<MasterPlanTaskVO> operationTasks)
```

#### 1.2 顶层工单数据查询  
```java
// 查询顶层工单数据
private Map<String, WorkOrderPO> getTopWorkOrderMap(Map<String, WorkOrderPO> workOrderMap)
```

### 2. 计算组件

#### 2.1 已排产量计算
```java
// 修改现有的计算逻辑
private BigDecimal calculateScheduleQty(String productCode, List<MasterPlanTaskVO> masterPlanTasks, 
                                       Map<String, WorkOrderPO> workOrderMap, 
                                       Map<String, WorkOrderPO> topWorkOrderMap)
```

## 数据模型

### 工单数据结构
```java
// 参考现有的WorkOrderPO结构
class WorkOrderPO {
    private String id;
    private String orderNo;
    private BigDecimal quantity;
    private String topOrderId;  // 顶层订单ID
    // 其他字段...
}
```

### 操作任务数据结构
```java
// 现有的MasterPlanTaskVO结构
class MasterPlanTaskVO {
    private String operationId;
    private OperationVO operationVO;
    private String standardStepName;
    // 其他字段...
}
```

## 错误处理

### 1. 数据缺失处理
- 当工单数据不存在时，已排产量设置为0
- 当顶层工单不存在时，使用当前工单的数量
- 记录相应的警告日志

### 2. 异常处理
- 数据库查询异常：记录错误日志，返回默认值
- 数据转换异常：记录错误日志，跳过异常数据
- 空指针异常：增加空值检查

### 3. 日志记录
```java
// 关键步骤的日志记录
log.info("开始计算产品{}的已排产量", productCode);
log.warn("工单{}的顶层订单不存在，使用当前工单数量", workOrderId);
log.error("计算已排产量时发生异常", e);
```

## 测试策略

### 1. 单元测试
- 测试`calculateScheduleQty`方法的各种场景
- 测试工单数据查询方法
- 测试异常处理逻辑

### 2. 集成测试
- 测试完整的报表查询流程
- 验证已排产量计算结果的正确性
- 测试性能影响

### 3. 测试用例
- 正常情况：有完整工单层级数据
- 边界情况：只有当前工单，没有顶层工单
- 异常情况：工单数据缺失
- 性能测试：大量数据的处理性能

## 实现细节

### 1. 查询优化
- 批量查询工单数据，避免N+1查询问题
- 使用Map结构缓存查询结果
- 保持现有的异步查询架构

### 2. 计算逻辑
```java
// 伪代码示例
BigDecimal calculateScheduleQty(String productCode, List<MasterPlanTaskVO> tasks, 
                               Map<String, WorkOrderPO> workOrderMap, 
                               Map<String, WorkOrderPO> topWorkOrderMap) {
    
    BigDecimal totalQuantity = BigDecimal.ZERO;
    
    for (MasterPlanTaskVO task : tasks) {
        if (isPackagingOperation(task)) {
            OperationVO operation = task.getOperationVO();
            if (operation != null && operation.getOrderId() != null) {
                WorkOrderPO workOrder = workOrderMap.get(operation.getOrderId());
                if (workOrder != null) {
                    BigDecimal quantity = getTopWorkOrderQuantity(workOrder, topWorkOrderMap);
                    if (matchesProduct(workOrder, productCode)) {
                        totalQuantity = totalQuantity.add(quantity);
                    }
                }
            }
        }
    }
    
    return totalQuantity;
}
```

### 3. 产品匹配逻辑
- 通过parentProductCode和productCode进行匹配
- 考虑产品层级关系
- 处理产品编码的变体情况

## 性能考虑

### 1. 查询性能
- 使用批量查询减少数据库访问次数
- 合理使用索引优化查询性能
- 考虑添加缓存机制

### 2. 内存使用
- 使用流式处理处理大量数据
- 及时释放不需要的数据引用
- 避免创建过多临时对象

### 3. 并发处理
- 保持现有的异步处理架构
- 使用线程安全的数据结构
- 合理控制并发度