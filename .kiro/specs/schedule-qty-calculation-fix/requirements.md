# 已排产量计算修复需求文档

## 介绍

当前需求发货生产报表中的已排产量(scheduleQty)计算逻辑需要修复。现有逻辑是基于包装工序的计划数量进行汇总，但应该改为参照MPS服务masterPlanWorkOrder方法的逻辑，通过**成型工序**工单的顶层订单数量(topWorkOrderQuantity)进行汇总，并根据parentProductCode和productCode进行匹配。

**重要发现**：经过对MPS服务源代码的详细分析，发现MPS服务的已排产量计算是基于**成型工序（FORMING_PROCESS）**而不是包装工序进行汇总的。

## 需求

### 需求 1

**用户故事:** 作为系统用户，我希望已排产量能够准确反映实际的排产情况，以便更好地进行生产计划管理。

#### 验收标准

1. WHEN 系统计算已排产量时 THEN 系统应该参照MPS服务masterPlanWorkOrder方法的逻辑，通过**成型工序**工单的顶层订单获取topWorkOrderQuantity数据
2. WHEN 获取到工单数据时 THEN 系统应该通过工单的topOrderId找到顶层工单，获取其quantity作为topWorkOrderQuantity
3. WHEN 获取到topWorkOrderQuantity数据时 THEN 系统应该根据parentProductCode和当前productCode进行匹配
4. WHEN 匹配成功时 THEN 系统应该对匹配的topWorkOrderQuantity进行汇总作为已排产量
5. IF 没有匹配的数据 THEN 已排产量应该设置为0

### 需求 2

**用户故事:** 作为开发人员，我希望修改后的代码能够保持良好的性能，不影响现有报表查询速度。

#### 验收标准

1. WHEN 查询工单和操作数据时 THEN 系统应该使用高效的查询方式以保持性能
2. WHEN 处理大量数据时 THEN 系统应该使用流式处理和分组操作提高效率
3. WHEN 发生异常时 THEN 系统应该有适当的错误处理和日志记录

### 需求 3

**用户故事:** 作为系统维护人员，我希望修改后的代码具有良好的可维护性和可测试性。

#### 验收标准

1. WHEN 修改代码时 THEN 应该保持现有的代码结构和命名规范
2. WHEN 添加新方法时 THEN 应该添加适当的注释和文档
3. WHEN 修改逻辑时 THEN 应该确保不影响其他功能的正常运行