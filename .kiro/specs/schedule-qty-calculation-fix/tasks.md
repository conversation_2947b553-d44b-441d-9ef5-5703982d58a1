# 已排产量计算修复实施计划

- [x] 1. 分析现有代码结构和依赖关系





  - 深入分析`DemandDeliveryProductionServiceImpl`中现有的已排产量计算逻辑
  - 研究MPS服务中`masterPlanWorkOrder`方法的工单查询和处理逻辑
  - 确定需要引入的新依赖和数据访问对象
  - _需求: 1.1, 1.2_

- [x] 2. 创建工单数据查询方法





  - 在`DemandDeliveryProductionServiceImpl`中添加`getWorkOrderMap`方法
  - 实现批量查询工单数据的逻辑，根据操作任务中的orderId查询对应工单
  - 添加适当的异常处理和日志记录
  - _需求: 1.1, 2.2, 3.2_

- [x] 3. 创建顶层工单数据查询方法





  - 添加`getTopWorkOrderMap`方法，根据工单的topOrderId查询顶层工单
  - 实现批量查询逻辑，避免N+1查询问题
  - 处理顶层工单不存在的情况，使用当前工单作为备选
  - _需求: 1.2, 2.2, 3.2_

- [x] 4. 重构已排产量计算逻辑





  - 修改`processProductData`方法中的已排产量计算部分
  - 创建新的`calculateScheduleQty`方法，实现基于顶层工单数量的计算逻辑
  - 实现产品编码匹配逻辑，根据parentProductCode和productCode进行匹配
  - **🚨 关键修正**：发现MPS服务使用成型工序而非包装工序进行汇总，已修正
  - _需求: 1.3, 1.4, 1.5_

- [x] 5. 集成工单查询到异步处理流程





  - 修改`queryDemandDeliveryProductionReport`方法，在异步任务中添加工单数据查询
  - 确保工单查询与其他数据查询并行执行，保持性能
  - 更新`CompletableFuture.allOf`调用，包含新的异步任务
  - _需求: 2.1, 2.2_

- [x] 6. 添加错误处理和日志记录





  - 在工单查询方法中添加数据库异常处理
  - 在计算方法中添加空值检查和数据转换异常处理
  - 添加关键步骤的信息日志和异常情况的警告日志
  - _需求: 2.3, 3.2_

- [x] 7. 编写单元测试





  - 为`calculateScheduleQty`方法编写单元测试，覆盖正常和异常场景
  - 为工单查询方法编写单元测试
  - 测试产品匹配逻辑的各种情况
  - _需求: 3.3_

- [x] 8. 性能测试和优化





  - 测试修改后代码的查询性能，确保不影响现有报表速度
  - 验证异步处理的并发效果
  - 如需要，添加查询优化或缓存机制
  - _需求: 2.1, 2.2_

- [x] 9. 集成测试和验证





  - 运行完整的报表查询流程，验证已排产量计算结果的正确性
  - 对比修改前后的计算结果，确保逻辑正确
  - 测试各种边界情况和异常场景
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5_

- [x] 10. 代码审查和文档更新





  - 进行代码审查，确保代码质量和规范性
  - 更新相关方法的注释和文档
  - 确保修改不影响其他功能的正常运行
  - _需求: 3.1, 3.2, 3.3_